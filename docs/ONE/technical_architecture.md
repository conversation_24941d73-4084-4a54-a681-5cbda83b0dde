# PFC Technical Architecture

## Overview

PFC (Prompt Factory/Requirements Factory) is a multi-component system designed to automate prompt engineering and code generation through intelligent requirement extraction, test case generation, and continuous feedback loops. The system consists of two main pillars: a Domain Knowledge Extractor and an Automatic Prompt Engineer (Requirements Factory).

## System Components

### 1. Domain Knowledge Extractor (Automation Guild)

**Purpose**: Extract domain knowledge and work patterns from subject matter experts within organizations.

**Key Components:**
- **Screen Recording Agent**: Rust-based software that captures screenshots every second
- **OCR Processing Pipeline**: Extracts text and context from screen captures
- **Activity Monitoring**: Tracks user actions, button clicks, and website navigation
- **Narrative Builder**: Constructs abstracted understanding of tasks being performed

**Technical Implementation:**
- Runs locally on employee computers within company infrastructure
- Processes screenshots and video data to understand workflows
- Abstracts concrete actions into generalizable task descriptions
- Decomposes complex workflows into agent-achievable subtasks

### 2. Requirements Factory (Automatic Prompt Engineer)

**Purpose**: Generate optimized prompts and code by automating requirement extraction and implementing feedback loops for progressive improvement.

**Core Components:**

#### 2.1 Document Generation System
- **Requirements Document Generator**: Creates hierarchical requirement specifications
  - Natural language format similar to traditional requirements docs
  - Defines success and failure criteria
  - Describes unwanted end states to avoid
  
- **Test Case Document Generator**: Produces scenario-based test specifications
  - Human-readable format (150 character width per line)
  - Natural language test descriptions
  - Converts to JSON format for systematic processing

#### 2.2 Test Case Generation Pipeline
- **Synthetic Test Case Generator**: Creates diverse test inputs in parallel
  - Each LLM call generates test cases independently (no cross-visibility)
  - Maintains diversity through test case document guidance
  - Generates actual inputs for prompt testing
  
- **Test Categories**:
  - Golden Path cases (standard expected usage)
  - Edge cases (unusual but valid scenarios)
  - Boundary cases (ambiguous situations requiring interpretation)
  - Hard cases (complex or challenging scenarios)

#### 2.3 Evaluation and Grading System
- **Output Evaluator**: Simple pass/fail assessment of outputs
- **Requirements Graders**: One grader per requirement
  - Can produce pass/fail or numerical scores (0-10)
  - Includes rubrics for consistent evaluation
  - Designed for future optimization with Pi Labs' headless models (10ms runtime)

## Data Flow Architecture

### Primary Flow
1. **Task Input** → Requirements Generation → Test Case Document Creation
2. Test Case Document → Parallel Synthetic Test Case Generation
3. Synthetic Test Cases → Prompt Execution → Output Collection
4. Outputs → Multi-Level Evaluation:
   - Level 1: Prompt Quality Assessment
   - Level 2: Output Correctness (per test case)
   - Level 3: Requirements Fulfillment Grading

### Feedback Loops

#### Loop 1: Prompt Quality Optimization
- Evaluates prompt structure without execution
- Uses established best practices for prompt design
- Iterates until quality threshold is met

#### Loop 2: Output Feedback
- Compares actual outputs against expected results
- Groups feedback by test case categories
- Aggregates improvement suggestions

#### Loop 3: Requirements-Based Grading
- Each requirement has dedicated grader
- Scores aggregated to identify weak areas
- Feedback used to refine prompts/instructions

## Technical Implementation Details

### Parallelization Strategy
- Test case generation runs in isolated parallel processes
- Each grader operates independently per requirement
- Clustering algorithms group similar feedback for efficient processing

### Token Optimization
- Continuous token usage monitoring
- Periodic prompt compression when size increases
- Balance between comprehensiveness and efficiency

### Evolution Path
1. **Phase 1**: Single prompt generation and optimization
2. **Phase 2**: Dynamic few-shot implementation
   - Router selects relevant examples based on input
   - Example database maintained separately from main prompt
3. **Phase 3**: Multi-agent system
   - Specialized agents for different tasks
   - Single mind coordination to reduce error propagation

### Storage and Versioning
- All document versions saved with diff tracking
- Edit history captures reasoning for changes
- Enables learning from human feedback patterns

## Optimization Strategies

### Reinforcement Learning Approach
- Uses natural language feedback instead of binary rewards
- "That's not quite right, here's why" → Score (e.g., 5/10)
- LLM agents interpret feedback to improve prompts
- Similar to Direct Policy Optimization but for language tasks

### Cost Management
- Initial system is token-intensive (especially grading phase)
- Future optimization through:
  - Pi Labs' efficient grader models
  - Selective evaluation based on sensitivity scores
  - Caching and reuse of common patterns

### Quality Assurance
- Sensitivity scoring: Run 100 variations, measure success rate
- Robustness testing across diverse inputs
- Continuous improvement through accumulated feedback

## Integration Points

### Client Feedback Integration
- Real-time incorporation when client is present
- Document editing with change tracking
- Feedback captured as structured data for learning

### Production Deployment
- Gradual rollout starting with decision support
- Initial focus on high-value tasks (e.g., due diligence)
- Progressive automation as confidence increases

### External Tool Integration
- Compatible with Claude Code and other AI agents
- Modular design allows component replacement
- API-driven architecture for flexibility

## Future Enhancements

### Dynamic Few-Shot System
- Intelligent example selection based on context
- Reduces prompt size while maintaining coverage
- Router-based architecture for efficiency

### Advanced Agent Coordination
- Move beyond role-based agents to step-based processing
- Unified context management across agent steps
- Tool usage optimization for complex tasks

### Enterprise Features
- Multi-tenant architecture for different organizations
- Compliance and audit trails
- Integration with existing enterprise systems