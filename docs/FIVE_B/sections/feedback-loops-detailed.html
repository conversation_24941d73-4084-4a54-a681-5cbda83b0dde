<!-- Feedback Loops - Detailed Section -->
<div class="section-detail">
    <h1>Three-Level Feedback System - Implementation Details</h1>
    
    <div class="expandable-section">
        <h2 class="section-header expandable">Level 1: Prompt Quality Assessment</h2>
        <div class="section-content">
            <h3>Static Analysis Engine</h3>
            <p>Evaluates prompts without execution using pattern recognition and best practices</p>
            
            <div class="analysis-components">
                <div class="component-detail">
                    <h4>Structure Analysis</h4>
                    <div class="code-example">
                        <pre>class PromptStructureAnalyzer:
    def analyze(self, prompt):
        scores = {}
        
        # Check for clear sections
        scores['has_objective'] = self._has_clear_objective(prompt)
        scores['has_instructions'] = self._has_structured_instructions(prompt)
        scores['has_examples'] = self._has_relevant_examples(prompt)
        scores['has_output_format'] = self._has_defined_output(prompt)
        
        # Analyze instruction clarity
        scores['instruction_clarity'] = self._measure_clarity(prompt)
        scores['ambiguity_score'] = self._detect_ambiguity(prompt)
        
        # Check organization
        scores['logical_flow'] = self._check_logical_flow(prompt)
        scores['section_balance'] = self._measure_section_balance(prompt)
        
        return {
            'overall_score': self._calculate_overall(scores),
            'details': scores,
            'recommendations': self._generate_recommendations(scores)
        }
    
    def _measure_clarity(self, prompt):
        """Analyze instruction clarity using NLP metrics"""
        
        metrics = {
            'sentence_complexity': self._flesch_reading_ease(prompt),
            'passive_voice_ratio': self._count_passive_voice(prompt),
            'instruction_specificity': self._measure_specificity(prompt),
            'technical_term_density': self._technical_density(prompt)
        }
        
        # Weighted scoring
        clarity_score = (
            metrics['sentence_complexity'] * 0.3 +
            (1 - metrics['passive_voice_ratio']) * 0.2 +
            metrics['instruction_specificity'] * 0.3 +
            (1 - min(metrics['technical_term_density'] / 0.2, 1)) * 0.2
        )
        
        return clarity_score</pre>
                    </div>
                </div>
                
                <div class="component-detail">
                    <h4>Best Practices Validation</h4>
                    <table class="validation-rules">
                        <tr>
                            <th>Rule</th>
                            <th>Description</th>
                            <th>Impact</th>
                        </tr>
                        <tr>
                            <td>Clear Objective</td>
                            <td>Prompt starts with clear goal statement</td>
                            <td>+15% success rate</td>
                        </tr>
                        <tr>
                            <td>Structured Format</td>
                            <td>Uses headers, lists, or numbered steps</td>
                            <td>+22% consistency</td>
                        </tr>
                        <tr>
                            <td>Example Presence</td>
                            <td>Includes 1-3 relevant examples</td>
                            <td>+31% accuracy</td>
                        </tr>
                        <tr>
                            <td>Output Specification</td>
                            <td>Defines exact output format</td>
                            <td>+28% format compliance</td>
                        </tr>
                        <tr>
                            <td>Edge Case Handling</td>
                            <td>Addresses common edge cases</td>
                            <td>+19% robustness</td>
                        </tr>
                    </table>
                </div>
                
                <div class="component-detail">
                    <h4>Token Efficiency Analysis</h4>
                    <div class="token-metrics">
                        <pre class="code-example">{
  "token_analysis": {
    "total_tokens": 487,
    "distribution": {
      "instructions": 245,
      "examples": 142,
      "formatting": 67,
      "redundancy": 33
    },
    "efficiency_score": 0.93,
    "optimization_potential": {
      "removable_tokens": 33,
      "compressible_sections": ["examples", "formatting"],
      "estimated_savings": "6.8%"
    }
  }
}</pre>
                    </div>
                </div>
            </div>
            
            <h3>Quality Scoring Rubric</h3>
            <div class="rubric-section">
                <div class="score-card">
                    <h4>Structure (30%)</h4>
                    <ul>
                        <li>Clear sections: 10%</li>
                        <li>Logical flow: 10%</li>
                        <li>Consistent formatting: 10%</li>
                    </ul>
                </div>
                
                <div class="score-card">
                    <h4>Clarity (25%)</h4>
                    <ul>
                        <li>Unambiguous language: 10%</li>
                        <li>Specific instructions: 10%</li>
                        <li>Appropriate complexity: 5%</li>
                    </ul>
                </div>
                
                <div class="score-card">
                    <h4>Completeness (25%)</h4>
                    <ul>
                        <li>All requirements covered: 15%</li>
                        <li>Edge cases addressed: 10%</li>
                    </ul>
                </div>
                
                <div class="score-card">
                    <h4>Efficiency (20%)</h4>
                    <ul>
                        <li>Token optimization: 10%</li>
                        <li>No redundancy: 10%</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="expandable-section">
        <h2 class="section-header expandable">Level 2: Output Validation System</h2>
        <div class="section-content">
            <h3>Execution & Testing Framework</h3>
            <div class="execution-pipeline">
                <pre class="architecture-diagram">
┌─────────────────┐     ┌──────────────┐     ┌─────────────────┐
│ Test Case       │     │ Prompt       │     │ Execution       │
│ Selector        │ ──► │ Executor     │ ──► │ Engine          │
└─────────────────┘     └──────────────┘     └─────────────────┘
         │                                              │
         ▼                                              ▼
┌─────────────────┐                          ┌─────────────────┐
│ Test Batches    │                          │ Raw Outputs     │
│ - Golden Path   │                          │ - Structured    │
│ - Edge Cases    │                          │ - Unstructured  │
│ - Boundaries    │                          │ - Errors        │
└─────────────────┘                          └─────────────────┘
                                                       │
                          ┌────────────────────────────┘
                          ▼
                ┌─────────────────┐     ┌─────────────────┐
                │ Output Parser   │ ──► │ Validator       │
                └─────────────────┘     └─────────────────┘
                                                 │
                                                 ▼
                                        ┌─────────────────┐
                                        │ Validation      │
                                        │ Results         │
                                        └─────────────────┘</pre>
            </div>
            
            <h3>Output Validation Rules</h3>
            <div class="validation-framework">
                <div class="rule-category">
                    <h4>Format Compliance</h4>
                    <pre class="code-example">class FormatValidator:
    def validate_output(self, output, expected_format):
        validations = {
            'json': self._validate_json,
            'xml': self._validate_xml,
            'csv': self._validate_csv,
            'structured_text': self._validate_structured_text
        }
        
        validator = validations.get(expected_format['type'])
        if not validator:
            raise ValueError(f"Unknown format: {expected_format['type']}")
        
        result = validator(output, expected_format.get('schema'))
        
        return {
            'valid': result['valid'],
            'errors': result.get('errors', []),
            'warnings': result.get('warnings', []),
            'compliance_score': result.get('score', 0)
        }
    
    def _validate_json(self, output, schema):
        try:
            data = json.loads(output)
            
            # Schema validation
            if schema:
                errors = self._validate_against_schema(data, schema)
                if errors:
                    return {
                        'valid': False,
                        'errors': errors,
                        'score': 0.5  # Partial credit for valid JSON
                    }
            
            return {
                'valid': True,
                'score': 1.0
            }
        
        except json.JSONDecodeError as e:
            return {
                'valid': False,
                'errors': [f"JSON parse error: {str(e)}"],
                'score': 0
            }</pre>
                </div>
                
                <div class="rule-category">
                    <h4>Semantic Validation</h4>
                    <pre class="code-example">class SemanticValidator:
    def validate_semantics(self, output, test_case):
        """Validate output meaning matches expectations"""
        
        validations = []
        
        # Check required information presence
        for required_field in test_case['expected_fields']:
            presence = self._check_field_presence(output, required_field)
            validations.append({
                'field': required_field,
                'present': presence['found'],
                'confidence': presence['confidence'],
                'value': presence.get('value')
            })
        
        # Validate relationships
        relationships = test_case.get('expected_relationships', [])
        for rel in relationships:
            valid = self._validate_relationship(output, rel)
            validations.append({
                'relationship': rel,
                'valid': valid['satisfied'],
                'evidence': valid.get('evidence')
            })
        
        # Check constraints
        constraints = test_case.get('constraints', [])
        for constraint in constraints:
            satisfied = self._check_constraint(output, constraint)
            validations.append({
                'constraint': constraint,
                'satisfied': satisfied['met'],
                'details': satisfied.get('details')
            })
        
        return {
            'overall_valid': all(v.get('present', v.get('valid', v.get('satisfied'))) 
                           for v in validations),
            'validations': validations,
            'semantic_score': self._calculate_semantic_score(validations)
        }</pre>
                </div>
                
                <div class="rule-category">
                    <h4>Business Logic Validation</h4>
                    <div class="business-rules">
                        <h5>Example: Invoice Processing Validation</h5>
                        <pre class="code-example">{
  "business_rules": [
    {
      "rule": "total_calculation",
      "description": "Total must equal sum of line items plus tax",
      "implementation": "abs(total - (sum(line_items) + tax)) < 0.01"
    },
    {
      "rule": "tax_rate_validity",
      "description": "Tax rate must be valid for jurisdiction",
      "implementation": "tax_rate in valid_tax_rates[jurisdiction]"
    },
    {
      "rule": "vendor_verification",
      "description": "Vendor must exist in approved vendor list",
      "implementation": "vendor_id in approved_vendors"
    },
    {
      "rule": "duplicate_detection",
      "description": "Invoice number must be unique",
      "implementation": "!exists(invoice_number in processed_invoices)"
    }
  ]
}</pre>
                    </div>
                </div>
            </div>
            
            <h3>Failure Analysis & Patterns</h3>
            <div class="failure-analysis">
                <h4>Common Failure Patterns</h4>
                <div class="pattern-grid">
                    <div class="pattern-card">
                        <h5>Format Failures</h5>
                        <ul>
                            <li>Missing closing brackets</li>
                            <li>Incorrect data types</li>
                            <li>Schema violations</li>
                        </ul>
                        <div class="solution">
                            <strong>Solution:</strong> Add explicit format examples and validation rules to prompt
                        </div>
                    </div>
                    
                    <div class="pattern-card">
                        <h5>Incomplete Extraction</h5>
                        <ul>
                            <li>Missing optional fields</li>
                            <li>Partial data capture</li>
                            <li>Truncated outputs</li>
                        </ul>
                        <div class="solution">
                            <strong>Solution:</strong> Emphasize completeness requirements and add field checklist
                        </div>
                    </div>
                    
                    <div class="pattern-card">
                        <h5>Logic Errors</h5>
                        <ul>
                            <li>Incorrect calculations</li>
                            <li>Wrong relationships</li>
                            <li>Invalid assumptions</li>
                        </ul>
                        <div class="solution">
                            <strong>Solution:</strong> Add step-by-step logic instructions and validation rules
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="expandable-section">
        <h2 class="section-header expandable">Level 3: Requirements Grading System</h2>
        <div class="section-content">
            <h3>Grader Architecture</h3>
            <div class="grader-system">
                <pre class="architecture-diagram">
┌─────────────────────────────────────────────────────┐
│               Requirements Document                  │
└──────────────────────┬──────────────────────────────┘
                       │
                       ▼
         ┌─────────────────────────────┐
         │    Grader Generator         │
         └─────────────┬───────────────┘
                       │
      ┌────────────────┼────────────────┐
      ▼                ▼                ▼
┌──────────┐    ┌──────────┐    ┌──────────┐
│ Grader 1 │    │ Grader 2 │    │ Grader N │
│ REQ-001  │    │ REQ-002  │    │ REQ-NNN  │
└─────┬────┘    └─────┬────┘    └─────┬────┘
      │                │                │
      ▼                ▼                ▼
┌──────────────────────────────────────────┐
│         Parallel Execution Engine        │
└──────────────────┬───────────────────────┘
                   │
                   ▼
┌──────────────────────────────────────────┐
│          Aggregated Scores               │
│  ┌────────────────────────────────────┐  │
│  │ REQ-001: 0.95 (PASS)              │  │
│  │ REQ-002: 0.73 (PASS with issues)  │  │
│  │ REQ-003: 0.42 (FAIL)              │  │
│  └────────────────────────────────────┘  │
└──────────────────────────────────────────┘</pre>
            </div>
            
            <h3>Grader Generation Process</h3>
            <div class="grader-generation">
                <h4>Example Grader for Requirement</h4>
                <div class="grader-example">
                    <h5>Requirement:</h5>
                    <pre class="requirement-example">{
  "id": "REQ-001",
  "description": "Extract vendor information accurately",
  "acceptance_criteria": [
    "Vendor name matches ERP database",
    "Tax ID validation passes",
    "Address components parsed correctly"
  ]
}</pre>
                    
                    <h5>Generated Grader:</h5>
                    <pre class="code-example">class VendorExtractionGrader:
    """Auto-generated grader for REQ-001"""
    
    def __init__(self):
        self.requirement_id = "REQ-001"
        self.erp_database = ERPConnector()
        self.tax_validator = TaxIDValidator()
    
    def grade(self, output, expected):
        scores = {}
        
        # Criterion 1: Vendor name matching
        vendor_name = output.get('vendor_name', '')
        erp_match = self.erp_database.fuzzy_match(vendor_name)
        scores['vendor_match'] = {
            'score': erp_match['confidence'],
            'pass': erp_match['confidence'] > 0.85,
            'details': f"Matched to: {erp_match['matched_name']}"
        }
        
        # Criterion 2: Tax ID validation
        tax_id = output.get('tax_id', '')
        tax_valid = self.tax_validator.validate(tax_id)
        scores['tax_id_valid'] = {
            'score': 1.0 if tax_valid['valid'] else 0.0,
            'pass': tax_valid['valid'],
            'details': tax_valid.get('error', 'Valid')
        }
        
        # Criterion 3: Address parsing
        address = output.get('address', {})
        address_score = self._score_address_parsing(address)
        scores['address_parsing'] = {
            'score': address_score['score'],
            'pass': address_score['score'] > 0.8,
            'details': address_score['missing_components']
        }
        
        # Overall scoring
        overall_score = (
            scores['vendor_match']['score'] * 0.4 +
            scores['tax_id_valid']['score'] * 0.3 +
            scores['address_parsing']['score'] * 0.3
        )
        
        return {
            'requirement_id': self.requirement_id,
            'overall_score': overall_score,
            'pass': overall_score > 0.8,
            'criteria_scores': scores,
            'recommendations': self._generate_recommendations(scores)
        }
    
    def _score_address_parsing(self, address):
        required_components = ['street', 'city', 'state', 'zip', 'country']
        present = [c for c in required_components if address.get(c)]
        
        score = len(present) / len(required_components)
        missing = [c for c in required_components if c not in present]
        
        return {
            'score': score,
            'missing_components': missing
        }</pre>
                </div>
            </div>
            
            <h3>Scoring Aggregation & Feedback</h3>
            <div class="scoring-system">
                <h4>Multi-Level Scoring</h4>
                <div class="scoring-levels">
                    <div class="level-detail">
                        <h5>Individual Requirement Scores</h5>
                        <pre class="score-example">{
  "REQ-001": {
    "score": 0.95,
    "status": "PASS",
    "criteria": {
      "vendor_match": 0.98,
      "tax_id_valid": 1.00,
      "address_parsing": 0.87
    }
  },
  "REQ-002": {
    "score": 0.73,
    "status": "PASS_WITH_ISSUES",
    "criteria": {
      "calculation_accuracy": 0.65,
      "format_compliance": 0.90,
      "edge_case_handling": 0.64
    }
  }
}</pre>
                    </div>
                    
                    <div class="level-detail">
                        <h5>Category Aggregation</h5>
                        <pre class="score-example">{
  "categories": {
    "data_extraction": {
      "requirements": ["REQ-001", "REQ-003", "REQ-005"],
      "average_score": 0.89,
      "pass_rate": 1.0
    },
    "business_logic": {
      "requirements": ["REQ-002", "REQ-004", "REQ-006"],
      "average_score": 0.71,
      "pass_rate": 0.67
    },
    "error_handling": {
      "requirements": ["REQ-007", "REQ-008"],
      "average_score": 0.82,
      "pass_rate": 1.0
    }
  }
}</pre>
                    </div>
                    
                    <div class="level-detail">
                        <h5>Overall System Score</h5>
                        <pre class="score-example">{
  "overall_metrics": {
    "total_requirements": 25,
    "passed": 22,
    "failed": 3,
    "pass_rate": 0.88,
    "average_score": 0.84,
    "weighted_score": 0.86,  // Critical requirements weighted higher
    "readiness": "PRODUCTION_READY_WITH_MONITORING"
  }
}</pre>
                    </div>
                </div>
                
                <h4>Feedback Generation</h4>
                <div class="feedback-engine">
                    <pre class="code-example">class FeedbackGenerator:
    def generate_improvement_plan(self, grading_results):
        """Generate actionable feedback from grading results"""
        
        # Identify failure patterns
        failures = self._analyze_failures(grading_results)
        
        # Group by improvement strategy
        improvements = {
            'prompt_modifications': [],
            'example_additions': [],
            'rule_clarifications': [],
            'edge_case_handling': []
        }
        
        for pattern in failures['patterns']:
            strategy = self._determine_strategy(pattern)
            improvement = self._generate_improvement(pattern, strategy)
            improvements[strategy].append(improvement)
        
        # Prioritize improvements
        prioritized = self._prioritize_improvements(improvements)
        
        return {
            'immediate_actions': prioritized['high_priority'],
            'medium_term': prioritized['medium_priority'],
            'long_term': prioritized['low_priority'],
            'estimated_impact': self._estimate_impact(prioritized)
        }
    
    def _generate_improvement(self, pattern, strategy):
        """Generate specific improvement recommendation"""
        
        templates = {
            'prompt_modifications': 
                "Modify prompt section '{section}' to address {issue}:\n{suggestion}",
            'example_additions':
                "Add example for {scenario}:\nInput: {input}\nExpected: {output}",
            'rule_clarifications':
                "Clarify rule for {requirement}:\nCurrent: {current}\nSuggested: {improved}",
            'edge_case_handling':
                "Add edge case handling for {case}:\n{handling_logic}"
        }
        
        return templates[strategy].format(**pattern.details)</pre>
                </div>
            </div>
        </div>
    </div>
    
    <div class="expandable-section">
        <h2 class="section-header expandable">Feedback Loop Integration</h2>
        <div class="section-content">
            <h3>Continuous Improvement Cycle</h3>
            <div class="improvement-cycle">
                <pre class="architecture-diagram">
                    ┌─────────────────┐
                    │   Monitoring    │
                    │   Dashboard     │
                    └────────┬────────┘
                             │
                ┌────────────▼────────────┐
                │   Performance Metrics   │
                │  - Success rates        │
                │  - Error patterns       │
                │  - Response times       │
                └────────────┬────────────┘
                             │
                ┌────────────▼────────────┐
                │   Analysis Engine       │
                │  - Pattern detection    │
                │  - Root cause analysis  │
                │  - Impact assessment    │
                └────────────┬────────────┘
                             │
                ┌────────────▼────────────┐
                │  Optimization Queue     │
                │  - Prioritized fixes    │
                │  - A/B test setup       │
                │  - Rollout planning     │
                └────────────┬────────────┘
                             │
                ┌────────────▼────────────┐
                │  Automated Updates      │
                │  - Prompt refinement    │
                │  - Test case addition   │
                │  - Grader adjustment    │
                └─────────────────────────┘</pre>
            </div>
            
            <h3>Real-Time Adaptation</h3>
            <div class="adaptation-system">
                <h4>Dynamic Adjustment Parameters</h4>
                <pre class="code-example">{
  "adaptation_config": {
    "thresholds": {
      "failure_rate_trigger": 0.15,
      "performance_degradation": 0.10,
      "confidence_minimum": 0.85
    },
    "response_actions": {
      "minor_degradation": "adjust_parameters",
      "major_degradation": "rollback_version",
      "pattern_detected": "create_targeted_fix",
      "edge_case_discovered": "add_test_coverage"
    },
    "update_frequency": {
      "monitoring": "real_time",
      "analysis": "every_15_minutes",
      "optimization": "hourly",
      "major_updates": "daily"
    }
  }
}</pre>
            </div>
        </div>
    </div>
</div>