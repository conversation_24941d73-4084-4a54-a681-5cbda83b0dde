<!-- Requirements Factory - Detailed Section -->
<div class="section-detail">
    <h1>Requirements Factory - Complete Implementation Guide</h1>
    
    <div class="expandable-section">
        <h2 class="section-header expandable">Requirements Document Generation</h2>
        <div class="section-content">
            <h3>Document Structure & Format</h3>
            <div class="document-structure">
                <pre class="code-example">{
  "requirements_document": {
    "version": "1.2.3",
    "task": "invoice_processing_automation",
    "created": "2024-01-15T10:30:00Z",
    "sections": {
      "objective": {
        "primary": "Automate vendor invoice processing for amounts under $10,000",
        "success_criteria": [
          "Extract all invoice fields with 99%+ accuracy",
          "Complete processing in under 30 seconds",
          "Handle PDF, image, and email formats"
        ]
      },
      "functional_requirements": {
        "core": [
          {
            "id": "REQ-001",
            "description": "Extract vendor information from invoice",
            "priority": "critical",
            "acceptance_criteria": [
              "Vendor name matches ERP database",
              "Tax ID validation passes",
              "Address components parsed correctly"
            ]
          },
          {
            "id": "REQ-002",
            "description": "Calculate and validate totals",
            "priority": "critical",
            "acceptance_criteria": [
              "Line items sum to subtotal",
              "Tax calculations correct for jurisdiction",
              "Total matches sum of subtotal + tax"
            ]
          }
        ]
      },
      "non_functional_requirements": {
        "performance": {
          "processing_time": "< 30 seconds per invoice",
          "throughput": "100 invoices per hour",
          "accuracy": "> 99% field extraction"
        },
        "reliability": {
          "availability": "99.9% uptime",
          "error_recovery": "Automatic retry with exponential backoff",
          "data_integrity": "Zero data loss tolerance"
        }
      },
      "edge_cases": {
        "handled": [
          "Multi-page invoices",
          "Multiple tax rates",
          "Foreign currency conversion",
          "Partial payments",
          "Credit memos"
        ],
        "excluded": [
          "Handwritten invoices",
          "Invoices over $10,000",
          "Non-standard formats"
        ]
      }
    }
  }
}</pre>
            </div>
            
            <h3>Generation Process</h3>
            <div class="process-flow">
                <div class="step-card">
                    <h4>Step 1: Task Analysis</h4>
                    <p>Analyze captured workflows to understand the task</p>
                    <ul>
                        <li>Identify input types and sources</li>
                        <li>Map decision points and logic</li>
                        <li>Document success/failure patterns</li>
                        <li>Extract business rules</li>
                    </ul>
                </div>
                
                <div class="step-card">
                    <h4>Step 2: Requirement Synthesis</h4>
                    <p>Generate structured requirements from analysis</p>
                    <ul>
                        <li>Group related functionalities</li>
                        <li>Define measurable criteria</li>
                        <li>Prioritize based on frequency</li>
                        <li>Include edge case handling</li>
                    </ul>
                </div>
                
                <div class="step-card">
                    <h4>Step 3: Human Review Loop</h4>
                    <p>Collaborate with domain experts for validation</p>
                    <ul>
                        <li>Present draft to process owners</li>
                        <li>Capture feedback and corrections</li>
                        <li>Iterate until approval</li>
                        <li>Version control all changes</li>
                    </ul>
                </div>
            </div>
            
            <h3>Requirement Quality Metrics</h3>
            <table class="metrics-table">
                <tr>
                    <th>Metric</th>
                    <th>Target</th>
                    <th>Measurement</th>
                </tr>
                <tr>
                    <td>Completeness</td>
                    <td>&gt; 95%</td>
                    <td>Percentage of workflows covered by requirements</td>
                </tr>
                <tr>
                    <td>Clarity</td>
                    <td>&gt; 90%</td>
                    <td>Requirements passing ambiguity analysis</td>
                </tr>
                <tr>
                    <td>Testability</td>
                    <td>100%</td>
                    <td>Requirements with measurable acceptance criteria</td>
                </tr>
                <tr>
                    <td>Consistency</td>
                    <td>100%</td>
                    <td>No conflicting requirements detected</td>
                </tr>
            </table>
        </div>
    </div>
    
    <div class="expandable-section">
        <h2 class="section-header expandable">Test Case Document Creation</h2>
        <div class="section-content">
            <h3>Test Case Categories</h3>
            <div class="category-grid">
                <div class="category-card">
                    <h4>Golden Path Tests</h4>
                    <p>Happy path scenarios representing ideal workflows</p>
                    <div class="test-example">
                        <pre>{
  "test_id": "TC-GP-001",
  "category": "golden_path",
  "description": "Process standard vendor invoice",
  "input": "Single-page PDF invoice with all required fields",
  "expected_output": {
    "vendor_extracted": true,
    "totals_validated": true,
    "processing_time": "< 10s",
    "confidence_score": "> 0.95"
  }
}</pre>
                    </div>
                </div>
                
                <div class="category-card">
                    <h4>Edge Case Tests</h4>
                    <p>Unusual but valid scenarios</p>
                    <div class="test-example">
                        <pre>{
  "test_id": "TC-EC-001",
  "category": "edge_case",
  "description": "Process multi-currency invoice",
  "input": "Invoice with USD subtotal and EUR tax",
  "expected_output": {
    "currency_conversion": true,
    "exchange_rate_applied": true,
    "total_in_base_currency": true
  }
}</pre>
                    </div>
                </div>
                
                <div class="category-card">
                    <h4>Boundary Tests</h4>
                    <p>Tests at system limits</p>
                    <div class="test-example">
                        <pre>{
  "test_id": "TC-BD-001",
  "category": "boundary",
  "description": "Process invoice at $9,999.99 limit",
  "input": "Invoice totaling exactly $9,999.99",
  "expected_output": {
    "processed": true,
    "approval_routing": "automatic",
    "limit_warning": false
  }
}</pre>
                    </div>
                </div>
                
                <div class="category-card">
                    <h4>Negative Tests</h4>
                    <p>Invalid inputs and error conditions</p>
                    <div class="test-example">
                        <pre>{
  "test_id": "TC-NG-001",
  "category": "negative",
  "description": "Reject invoice over limit",
  "input": "Invoice totaling $10,000.01",
  "expected_output": {
    "processed": false,
    "error_code": "LIMIT_EXCEEDED",
    "manual_review_flagged": true
  }
}</pre>
                    </div>
                </div>
            </div>
            
            <h3>Test Case Generation Algorithm</h3>
            <div class="algorithm-section">
                <pre class="code-example">
class TestCaseGenerator:
    def generate_test_cases(self, requirements_doc, target_count=100):
        """Generate comprehensive test cases from requirements"""
        
        # Parse requirements into testable components
        testable_requirements = self.extract_testable_requirements(requirements_doc)
        
        # Allocate test cases by category
        allocation = {
            'golden_path': int(target_count * 0.3),
            'edge_case': int(target_count * 0.3),
            'boundary': int(target_count * 0.2),
            'negative': int(target_count * 0.2)
        }
        
        test_cases = []
        
        # Generate golden path tests
        for req in testable_requirements['critical']:
            test_cases.extend(
                self.generate_golden_path_tests(req, allocation['golden_path'])
            )
        
        # Generate edge cases from domain analysis
        edge_cases = self.analyze_edge_conditions(requirements_doc)
        for edge in edge_cases:
            test_cases.extend(
                self.generate_edge_tests(edge, allocation['edge_case'])
            )
        
        # Generate boundary tests
        boundaries = self.identify_boundaries(requirements_doc)
        for boundary in boundaries:
            test_cases.extend(
                self.generate_boundary_tests(boundary, allocation['boundary'])
            )
        
        # Generate negative tests
        test_cases.extend(
            self.generate_negative_tests(requirements_doc, allocation['negative'])
        )
        
        return self.diversify_test_cases(test_cases)
    
    def diversify_test_cases(self, test_cases):
        """Ensure test diversity using clustering"""
        
        # Embed test cases using semantic similarity
        embeddings = self.embed_test_cases(test_cases)
        
        # Cluster similar tests
        clusters = self.cluster_tests(embeddings)
        
        # Select diverse subset
        diverse_tests = []
        for cluster in clusters:
            # Take representative samples from each cluster
            diverse_tests.extend(
                self.select_representative_tests(cluster, max_per_cluster=3)
            )
        
        return diverse_tests
</pre>
            </div>
        </div>
    </div>
    
    <div class="expandable-section">
        <h2 class="section-header expandable">Synthetic Test Case Generation</h2>
        <div class="section-content">
            <h3>Parallel Generation Architecture</h3>
            <div class="architecture-diagram">
                <pre>
┌──────────────────┐
│ Test Case Doc    │
│ (JSON Format)    │
└────────┬─────────┘
         │
         ▼
┌──────────────────┐
│ Test Distributor │
└────────┬─────────┘
         │
    ┌────┴────┬────────┬────────┐
    ▼         ▼        ▼        ▼
┌────────┐┌────────┐┌────────┐┌────────┐
│Worker 1││Worker 2││Worker 3││Worker N│
└────┬───┘└────┬───┘└────┬───┘└────┬───┘
     │         │        │        │
     ▼         ▼        ▼        ▼
[Synthetic] [Synthetic] [Synthetic] [Synthetic]
[Test 1-25] [Test 26-50][Test 51-75][Test 76-100]
</pre>
            </div>
            
            <h3>Generation Parameters</h3>
            <div class="parameter-config">
                <pre class="code-example">{
  "synthetic_generation_config": {
    "parallelism": {
      "workers": 10,
      "tests_per_worker": 10,
      "timeout_seconds": 30
    },
    "diversity_settings": {
      "variation_types": [
        "field_values",
        "formatting",
        "data_quality",
        "edge_conditions"
      ],
      "minimum_similarity_distance": 0.3,
      "clustering_threshold": 0.7
    },
    "quality_controls": {
      "validation_required": true,
      "schema_compliance": true,
      "realistic_data": true,
      "business_rules_adherence": true
    }
  }
}</pre>
            </div>
            
            <h3>Example Synthetic Test Generation</h3>
            <div class="generation-example">
                <h4>Base Test Case:</h4>
                <pre class="code-example">{
  "test": "Process vendor invoice with line items",
  "scenario": "Standard invoice with 3 line items"
}</pre>
                
                <h4>Generated Synthetic Variations:</h4>
                <div class="variation-grid">
                    <div class="variation-card">
                        <h5>Variation 1: Field Values</h5>
                        <pre>{
  "vendor": "Acme Corp",
  "invoice_no": "INV-2024-001",
  "line_items": [
    {"desc": "Widget A", "qty": 10, "price": 25.00},
    {"desc": "Widget B", "qty": 5, "price": 40.00},
    {"desc": "Service Fee", "qty": 1, "price": 100.00}
  ],
  "tax_rate": 0.08,
  "currency": "USD"
}</pre>
                    </div>
                    
                    <div class="variation-card">
                        <h5>Variation 2: Edge Format</h5>
                        <pre>{
  "vendor": "Ñoño & Associates, Ltd.",
  "invoice_no": "2024/01/001-A",
  "line_items": [
    {"desc": "Consulting - Phase 1", "qty": 0.5, "price": 5000.00},
    {"desc": "Expenses (See attached)", "qty": 1, "price": 234.56},
    {"desc": "Discount", "qty": 1, "price": -100.00}
  ],
  "tax_rate": 0.0825,
  "currency": "USD"
}</pre>
                    </div>
                    
                    <div class="variation-card">
                        <h5>Variation 3: Data Quality</h5>
                        <pre>{
  "vendor": "GLOBAL SUPPLIES INC",
  "invoice_no": "inv_20240115_001",
  "line_items": [
    {"desc": "Office Supplies", "qty": 100, "price": 12.99},
    {"desc": "Shipping", "qty": 1, "price": 25},
    {"desc": "Rush Processing", "qty": 1, "price": 50.00}
  ],
  "tax_rate": 0.06625,
  "currency": "USD",
  "notes": "Net 30 terms"
}</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="expandable-section">
        <h2 class="section-header expandable">Prompt Optimization Engine</h2>
        <div class="section-content">
            <h3>Optimization Strategies</h3>
            <div class="strategy-grid">
                <div class="strategy-card">
                    <h4>Token Efficiency</h4>
                    <p>Minimize tokens while maintaining effectiveness</p>
                    <ul>
                        <li>Compress redundant instructions</li>
                        <li>Use concise variable names</li>
                        <li>Eliminate unnecessary examples</li>
                        <li>Optimize whitespace and formatting</li>
                    </ul>
                    <div class="metric-display">
                        <span class="metric-label">Average Reduction:</span>
                        <span class="metric-value">35% fewer tokens</span>
                    </div>
                </div>
                
                <div class="strategy-card">
                    <h4>Clarity Enhancement</h4>
                    <p>Improve instruction clarity and reduce ambiguity</p>
                    <ul>
                        <li>Structure with clear sections</li>
                        <li>Use consistent terminology</li>
                        <li>Add disambiguating context</li>
                        <li>Order by importance</li>
                    </ul>
                    <div class="metric-display">
                        <span class="metric-label">Error Reduction:</span>
                        <span class="metric-value">42% fewer misinterpretations</span>
                    </div>
                </div>
                
                <div class="strategy-card">
                    <h4>Example Selection</h4>
                    <p>Dynamic few-shot example optimization</p>
                    <ul>
                        <li>Select relevant examples</li>
                        <li>Balance edge vs common cases</li>
                        <li>Rotate examples by context</li>
                        <li>Learn from failure patterns</li>
                    </ul>
                    <div class="metric-display">
                        <span class="metric-label">Performance Gain:</span>
                        <span class="metric-value">28% accuracy improvement</span>
                    </div>
                </div>
            </div>
            
            <h3>Optimization Loop</h3>
            <div class="optimization-process">
                <pre class="code-example">
class PromptOptimizer:
    def optimize_prompt(self, base_prompt, test_results, requirements):
        """Main optimization loop"""
        
        current_prompt = base_prompt
        iteration = 0
        best_score = 0
        
        while iteration < self.max_iterations:
            # Analyze current performance
            analysis = self.analyze_failures(test_results)
            
            # Generate improvement hypotheses
            hypotheses = [
                self.hypothesis_clarity(analysis),
                self.hypothesis_examples(analysis),
                self.hypothesis_structure(analysis),
                self.hypothesis_edge_cases(analysis)
            ]
            
            # Test each hypothesis
            for hypothesis in hypotheses:
                candidate_prompt = self.apply_hypothesis(
                    current_prompt, hypothesis
                )
                
                # Run subset of tests
                quick_results = self.quick_test(
                    candidate_prompt, 
                    sample_size=20
                )
                
                score = self.score_results(quick_results, requirements)
                
                if score > best_score:
                    # Full test suite on promising candidate
                    full_results = self.full_test(candidate_prompt)
                    full_score = self.score_results(full_results, requirements)
                    
                    if full_score > best_score:
                        current_prompt = candidate_prompt
                        best_score = full_score
                        test_results = full_results
            
            # Token optimization pass
            current_prompt = self.optimize_tokens(current_prompt)
            
            iteration += 1
            
            # Check convergence
            if self.has_converged(best_score):
                break
        
        return current_prompt, best_score
    
    def analyze_failures(self, test_results):
        """Identify patterns in test failures"""
        
        failures = [r for r in test_results if not r.passed]
        
        # Cluster failures by similarity
        failure_clusters = self.cluster_failures(failures)
        
        # Extract common patterns
        patterns = {
            'misunderstood_instructions': [],
            'edge_case_failures': [],
            'consistency_issues': [],
            'performance_problems': []
        }
        
        for cluster in failure_clusters:
            pattern_type = self.classify_failure_pattern(cluster)
            patterns[pattern_type].extend(cluster)
        
        return patterns
</pre>
            </div>
            
            <h3>Prompt Evolution Example</h3>
            <div class="evolution-example">
                <h4>Initial Prompt (v1.0):</h4>
                <pre class="prompt-example">Extract invoice data from the provided text. Include vendor name, invoice number, line items, and total.</pre>
                
                <h4>After Optimization (v3.2):</h4>
                <pre class="prompt-example">Extract invoice data following this structure:

REQUIRED FIELDS:
1. vendor_name: Company name exactly as shown
2. invoice_number: Include any prefix/suffix
3. line_items: Array of {description, quantity, unit_price}
4. total_amount: Final total including tax

RULES:
- Preserve original formatting for names
- Convert all amounts to decimal format
- If tax is itemized, include as separate line item
- For missing data, use null (not empty string)

OUTPUT FORMAT: JSON with keys matching field names above</pre>
                
                <div class="improvement-metrics">
                    <h5>Improvements:</h5>
                    <ul>
                        <li>Accuracy: 76% → 94%</li>
                        <li>Consistency: 68% → 97%</li>
                        <li>Edge case handling: 45% → 89%</li>
                        <li>Token count: 23 → 87 (worth the trade-off)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>