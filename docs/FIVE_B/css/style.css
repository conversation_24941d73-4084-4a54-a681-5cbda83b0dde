:root {
    --primary-color: #2563eb;
    --secondary-color: #3b82f6;
    --accent-color: #60a5fa;
    --background-color: #f9fafb;
    --text-color: #1f2937;
    --border-color: #e5e7eb;
    --sidebar-width: 280px;
    --content-max-width: 900px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    display: flex;
    min-height: 100vh;
}

/* Sidebar Navigation */
#sidebar {
    width: var(--sidebar-width);
    background: white;
    border-right: 1px solid var(--border-color);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.nav-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-header h2 {
    font-size: 1.25rem;
    color: var(--primary-color);
}

#toggle-nav {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
}

#nav-list {
    list-style: none;
    padding: 1rem 0;
}

.nav-link {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: #f3f4f6;
    border-left-color: var(--accent-color);
}

.nav-link.active {
    background-color: #eff6ff;
    border-left-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
}

/* Main Content */
#content {
    margin-left: var(--sidebar-width);
    flex: 1;
    padding: 2rem;
    max-width: calc(var(--content-max-width) + 4rem);
    margin-right: auto;
}

.content-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

h2 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
    color: var(--text-color);
}

h3 {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
    color: var(--text-color);
}

p {
    margin-bottom: 1rem;
}

/* Expandable Sections */
.expandable-section {
    margin-bottom: 1.5rem;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.section-header {
    padding: 1rem 1.5rem;
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease;
}

.section-header:hover {
    background-color: #f9fafb;
}

.section-header.expandable::after {
    content: '▼';
    font-size: 0.8em;
    transition: transform 0.3s ease;
    color: var(--accent-color);
}

.section-header.expandable.collapsed::after {
    transform: rotate(-90deg);
}

.section-content {
    padding: 0 1.5rem 1.5rem;
    max-height: 2000px;
    transition: max-height 0.3s ease, padding 0.3s ease;
    overflow: hidden;
}

.section-content.collapsed {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
}

/* Component Cards */
.component-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.component-card {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.component-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: var(--accent-color);
}

.learn-more {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.learn-more:hover {
    background: var(--secondary-color);
}

/* Document Flow */
.document-flow {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1.5rem 0;
    overflow-x: auto;
    padding: 1rem 0;
}

.document-card {
    flex: 1;
    min-width: 200px;
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 2px solid var(--border-color);
}

.arrow {
    font-size: 2rem;
    color: var(--accent-color);
    flex-shrink: 0;
}

/* Diagrams */
.diagram-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin: 1rem 0;
    overflow-x: auto;
}

.architecture-diagram {
    background: #f3f4f6;
    padding: 1.5rem;
    border-radius: 4px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Glossary */
.glossary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.glossary-term {
    background: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-bottom: 0;
}

.glossary-term h3 {
    margin-bottom: 0;
    font-size: 1.1rem;
}

/* Sub-navigation */
.sub-nav {
    list-style: none;
    margin-left: 1rem;
    border-left: 2px solid var(--border-color);
}

.sub-nav-link {
    display: block;
    padding: 0.5rem 1rem 0.5rem 1.5rem;
    color: #6b7280;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.sub-nav-link:hover {
    color: var(--primary-color);
    background-color: #f9fafb;
}

/* Alert Boxes */
.alert-info {
    background: #eff6ff;
    border: 1px solid var(--accent-color);
    border-radius: 6px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-warning {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    padding: 1rem 1.5rem;
    margin: 1rem 0;
}

/* Value Cards */
.value-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.value-card {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.value-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Quick Start Steps */
.quick-start-steps {
    counter-reset: step-counter;
    list-style: none;
}

.quick-start-steps li {
    counter-increment: step-counter;
    position: relative;
    padding-left: 3rem;
    margin-bottom: 2rem;
}

.quick-start-steps li::before {
    content: counter(step-counter);
    position: absolute;
    left: 0;
    top: 0;
    width: 2rem;
    height: 2rem;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Code Examples */
.code-example {
    background: #1f2937;
    color: #e5e7eb;
    padding: 1.5rem;
    border-radius: 6px;
    overflow-x: auto;
    margin: 1rem 0;
}

.code-example pre {
    margin: 0;
    font-family: 'Fira Code', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Tables */
.spec-table, .metrics-table, .validation-rules {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.spec-table th, .metrics-table th, .validation-rules th {
    background: #f3f4f6;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
}

.spec-table td, .metrics-table td, .validation-rules td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 600;
}

.timeline-content {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Case Study Elements */
.case-study-header {
    margin-bottom: 2rem;
}

.case-metrics {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
}

.metric-box {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.metric-label {
    display: block;
    font-size: 0.9rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

/* Algorithm sections */
.algorithm-section {
    margin: 2rem 0;
}

.algorithm-detail {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1rem 0;
}

/* Phase cards */
.phase-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    #sidebar {
        transform: translateX(-100%);
    }
    
    #sidebar.open {
        transform: translateX(0);
    }
    
    #toggle-nav {
        display: block;
    }
    
    #content {
        margin-left: 0;
        padding: 1rem;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    .component-grid {
        grid-template-columns: 1fr;
    }
    
    .document-flow {
        flex-direction: column;
    }
    
    .arrow {
        transform: rotate(90deg);
    }
    
    .value-grid {
        grid-template-columns: 1fr;
    }
    
    .case-metrics {
        flex-direction: column;
        gap: 1rem;
    }
}