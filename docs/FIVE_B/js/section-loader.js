// Section Loader for Dynamic Content
document.addEventListener('DOMContentLoaded', function() {
    const sectionFiles = {
        'domain-extractor': 'sections/domain-extractor-detailed.html',
        'requirements-factory': 'sections/requirements-factory-detailed.html',
        'feedback-loops': 'sections/feedback-loops-detailed.html',
        'technical-details': 'sections/technical-implementation.html',
        'case-studies': 'sections/case-studies.html'
    };
    
    // Cache for loaded sections
    const sectionCache = {};
    
    // Load section content
    async function loadSection(sectionId) {
        if (sectionCache[sectionId]) {
            return sectionCache[sectionId];
        }
        
        const file = sectionFiles[sectionId];
        if (!file) return null;
        
        try {
            const response = await fetch(file);
            const content = await response.text();
            sectionCache[sectionId] = content;
            return content;
        } catch (error) {
            console.error(`Failed to load section ${sectionId}:`, error);
            return null;
        }
    }
    
    // Insert section content
    function insertSectionContent(sectionId, content) {
        const section = document.getElementById(sectionId);
        if (!section) return;
        
        // Check if content already loaded
        if (section.querySelector('.section-detail')) return;
        
        // Insert content
        const contentDiv = document.createElement('div');
        contentDiv.innerHTML = content;
        section.appendChild(contentDiv);
        
        // Re-initialize expandables in the new content
        initializeExpandablesInSection(section);
    }
    
    // Initialize expandables in a specific section
    function initializeExpandablesInSection(section) {
        const expandableHeaders = section.querySelectorAll('.section-header.expandable');
        
        expandableHeaders.forEach(header => {
            // Skip if already initialized
            if (header.hasAttribute('data-initialized')) return;
            
            header.setAttribute('data-initialized', 'true');
            header.classList.remove('collapsed');
            
            const content = header.nextElementSibling;
            if (content && content.classList.contains('section-content')) {
                content.classList.remove('collapsed');
            }
            
            header.addEventListener('click', function() {
                this.classList.toggle('collapsed');
                const content = this.nextElementSibling;
                
                if (content && content.classList.contains('section-content')) {
                    content.classList.toggle('collapsed');
                }
            });
        });
    }
    
    // Handle navigation clicks with section loading
    const originalNavHandler = window.initializeNavigation;
    
    window.initializeNavigation = function() {
        const navLinks = document.querySelectorAll('.nav-link');
        const sections = document.querySelectorAll('.content-section');
        
        navLinks.forEach(link => {
            link.addEventListener('click', async function(e) {
                e.preventDefault();
                
                // Get target section
                const targetId = this.getAttribute('href').substring(1);
                const targetSection = document.getElementById(targetId);
                
                // Update active states
                navLinks.forEach(l => l.classList.remove('active'));
                sections.forEach(s => s.classList.remove('active'));
                
                this.classList.add('active');
                targetSection.classList.add('active');
                
                // Load section content if needed
                if (sectionFiles[targetId]) {
                    const content = await loadSection(targetId);
                    if (content) {
                        insertSectionContent(targetId, content);
                    }
                }
                
                // Scroll to top of content
                window.scrollTo(0, 0);
                
                // Close mobile menu if open
                document.getElementById('sidebar').classList.remove('open');
                
                // Update URL hash
                history.pushState(null, null, '#' + targetId);
            });
        });
        
        // Handle sub-navigation
        const subNavLinks = document.querySelectorAll('.sub-nav-link');
        subNavLinks.forEach(link => {
            link.addEventListener('click', async function(e) {
                e.preventDefault();
                
                const hash = this.getAttribute('href');
                const [sectionId, subsectionId] = hash.substring(1).split('-');
                
                // Load main section first
                const mainLink = document.querySelector(`a[href="#${sectionId}"]`);
                if (mainLink) {
                    mainLink.click();
                    
                    // Wait for section to load
                    setTimeout(() => {
                        const targetElement = document.getElementById(subsectionId);
                        if (targetElement) {
                            targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }
                    }, 500);
                }
            });
        });
        
        // Handle initial hash
        const initialHash = window.location.hash.substring(1) || 'executive-summary';
        const initialLink = document.querySelector(`a[href="#${initialHash}"]`);
        if (initialLink) {
            initialLink.click();
        }
    };
    
    // Re-initialize navigation with section loading
    if (window.initializeNavigation) {
        window.initializeNavigation();
    }
});