# Prompt Generator

A sophisticated AI-powered prompt engineering tool that generates, tests, and optimizes prompts using a multi-agent system with real-time web interface.

## Features

### 🎯 Core Functionality
- **Multi-Agent Prompt Generation**: Uses specialized AI agents (Writer, Editor, Critic, Token Optimizer, Output Guardian) to collaboratively create high-quality prompts
- **Real-Time Web Interface**: Modern Flask-based UI with SocketIO for live updates during prompt generation
- **Interactive Prompt Testing**: Generate realistic test cases and execute prompts with real input data
- **Prompt Execution Engine**: Handles complex prompts with system/user message extraction and execution
- **Configurable AI Models**: Support for different LLM providers (OpenAI, Anthropic, etc.)

### 🔧 Advanced Features
- **Smart Message Extraction**: Uses LLM to intelligently parse prompts with various formats (tags, headers, etc.)
- **Parallel Test Generation**: Generates diverse test cases concurrently for faster testing
- **Realistic Test Data**: Creates substantial, context-rich input data with filled variables
- **Editable Prompts**: Modify generated prompts directly in the web interface
- **Sticky UI Elements**: Enhanced UX with persistent panels and stepper navigation

## Installation

### Prerequisites
- Python 3.8+
- pip

### Setup
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd prompt_generator
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure API keys**
   Create or edit `config.yaml`:
   ```yaml
   openai:
     api_key: "your-openai-api-key"
   anthropic:
     api_key: "your-anthropic-api-key"
   default_model: "gpt-4o"
   ```

## Usage

### Web Interface (Recommended)
1. **Start the web server**
   ```bash
   python app.py
   ```

2. **Open your browser**
   Navigate to `http://localhost:5000`

3. **Generate prompts**
   - Enter your initial prompt description
   - Select the number of test cases
   - Watch the multi-agent system work in real-time
   - Edit the final prompt if needed
   - Execute test cases to validate the prompt

### Command Line Interface
```bash
# Generate a prompt with CLI
python -m prompt_generator.cli --prompt "Create a professional email template" --save

# Available options
python -m prompt_generator.cli --help
```

## Architecture

### Multi-Agent System
The prompt generation process uses specialized AI agents:

1. **Writer**: Creates the initial prompt draft
2. **Editor**: Refines and improves the prompt structure
3. **Critic**: Evaluates prompt quality and suggests improvements
4. **Token Optimizer**: Reduces token usage while maintaining quality
5. **Output Guardian**: Ensures prompt safety and compliance

### File Structure
```
prompt_generator/
├── app.py                 # Flask web application
├── config.yaml           # Configuration file
├── requirements.txt      # Python dependencies
├── prompt_generator/     # Core package
│   ├── __init__.py
│   ├── cli.py           # Command line interface
│   ├── config.py        # Configuration management
│   ├── core.py          # Core prompt generation logic
│   ├── orchestrator.py  # Multi-agent orchestration
│   └── roles/           # AI agent implementations
│       ├── base.py      # Base agent class
│       ├── writer.py    # Writer agent
│       ├── editor.py    # Editor agent
│       ├── critic.py    # Critic agent
│       ├── token_optimizer.py
│       └── output_guardian.py
├── static/              # Web assets
│   ├── css/style.css
│   └── js/app.js
└── templates/           # HTML templates
    └── index.html
```

## Configuration

### Model Configuration
The system supports multiple LLM providers. Configure in `config.yaml`:

```yaml
openai:
  api_key: "your-key"
  model: "gpt-4o"
anthropic:
  api_key: "your-key"
  model: "claude-3-sonnet"
default_model: "gpt-4o"
```

### Agent Parameters
Each agent can be configured with specific parameters:
- Temperature settings
- Token limits
- Retry logic
- Quality thresholds

## Testing

### Test Case Generation
The system generates realistic test cases by:
1. Analyzing the prompt to determine appropriate input types
2. Creating diverse test scenarios (documents, emails, queries, etc.)
3. Filling in variables with realistic values
4. Ensuring substantial, context-rich input data

### Prompt Execution
Prompts are executed with:
- Intelligent message extraction (handles various formats)
- System/user message separation
- Real-time result display
- Error handling and retry logic

## Development

### Adding New Agents
1. Create a new agent class in `roles/`
2. Inherit from `BaseAgent`
3. Implement the `process` method
4. Add to the orchestrator workflow

### Extending the Web UI
- Modify `templates/index.html` for UI changes
- Update `static/js/app.js` for frontend logic
- Adjust `static/css/style.css` for styling

## Troubleshooting

### Common Issues
1. **API Key Errors**: Ensure your API keys are correctly set in `config.yaml`
2. **Import Errors**: Make sure all dependencies are installed
3. **Port Conflicts**: Change the port in `app.py` if 5000 is in use

### Debug Mode
Enable debug logging by setting environment variables:
```bash
export DEBUG=1
python app.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

[Add your license information here]

## Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the configuration examples 