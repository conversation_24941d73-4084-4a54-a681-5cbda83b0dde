let socket = null;
let currentRoom = null;
let messageCount = 0;
let finalPrompt = '';
let generatedPrompt = '';
let testData = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeSocket();
    setupEventListeners();
    setStepper(1);
    document.getElementById('generateTestDataBtn').disabled = true;
    document.getElementById('runPromptOnTestDataBtn').disabled = true;
    document.getElementById('testDataCard').classList.add('d-none');
    document.getElementById('testResultsCard').classList.add('d-none');
    document.getElementById('resetWorkflowBtn').addEventListener('click', resetWorkflow);
});

function initializeSocket() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('Connected to server');
        updateStatus('Connected', 'success');
    });
    
    socket.on('disconnect', function() {
        console.log('Disconnected from server');
        updateStatus('Disconnected', 'danger');
    });
    
    // Handle different types of messages
    socket.on('status_update', function(data) {
        addStatusMessage(data.message, data.status);
        updateStatus(data.status, getStatusColor(data.status));
    });
    
    socket.on('turn_update', function(data) {
        addTurnMessage(data.turn, data.role, data.reason);
        updateProgress(data.turn, data.role);
    });
    
    socket.on('draft_update', function(data) {
        addDraftMessage(data.draft, data.role, data.turn);
        finalPrompt = data.draft;
    });
    
    socket.on('critic_feedback', function(data) {
        addCriticMessage(data.score, data.feedback, data.turn);
    });
    
    socket.on('log_message', function(data) {
        addLogMessage(data.message, data.timestamp);
    });
    
    socket.on('final_result', function(data) {
        showFinalResult(data.prompt, data.termination_reason, data.total_turns);
        updateStatus('Completed', 'success');
        enableGenerateButton();
    });
    
    socket.on('error', function(data) {
        addErrorMessage(data.message);
        updateStatus('Error', 'danger');
        enableGenerateButton();
    });
}

function setupEventListeners() {
    const form = document.getElementById('promptForm');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        generatePrompt();
    });
    document.getElementById('generateTestDataBtn').addEventListener('click', generateTestData);
    document.getElementById('runPromptOnTestDataBtn').addEventListener('click', runPromptOnTestData);
}

function generatePrompt() {
    const task = document.getElementById('task').value.trim();
    const targetScore = parseFloat(document.getElementById('targetScore').value);
    const maxTurns = parseInt(document.getElementById('maxTurns').value);
    
    if (!task) {
        alert('Please enter a task description');
        return;
    }
    
    // Clear previous results
    clearConversation();
    hideFinalPrompt();
    document.getElementById('generateTestDataBtn').disabled = true;
    document.getElementById('runPromptOnTestDataBtn').disabled = true;
    document.getElementById('testDataCard').classList.add('d-none');
    document.getElementById('testResultsCard').classList.add('d-none');
    setStepper(1);
    
    // Disable button and show loading
    disableGenerateButton();
    updateStatus('Starting...', 'warning');
    
    // Send request to server
    fetch('/api/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            task: task,
            target_score: targetScore,
            max_turns: maxTurns
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        currentRoom = data.room;
        socket.emit('join', { room: currentRoom });
        addStatusMessage('Generation started successfully', 'info');
    })
    .catch(error => {
        addErrorMessage('Failed to start generation: ' + error.message);
        updateStatus('Error', 'danger');
        enableGenerateButton();
    });
}

function addStatusMessage(message, type) {
    const log = document.getElementById('conversationLog');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message status fade-in`;
    messageDiv.innerHTML = `
        <span class="timestamp">${new Date().toLocaleTimeString()}</span>
        <span class="badge bg-${type}">${type.toUpperCase()}</span>
        ${message}
    `;
    log.appendChild(messageDiv);
    incrementMessageCount();
    scrollToBottom();
}

function addTurnMessage(turn, role, reason) {
    const log = document.getElementById('conversationLog');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message turn fade-in`;
    messageDiv.innerHTML = `
        <span class="timestamp">${new Date().toLocaleTimeString()}</span>
        <span class="role-badge role-${role.toLowerCase()}">${role}</span>
        <strong>Turn ${turn}:</strong> ${reason}
    `;
    log.appendChild(messageDiv);
    incrementMessageCount();
    scrollToBottom();
}

function addDraftMessage(draft, role, turn) {
    const log = document.getElementById('conversationLog');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message draft fade-in`;
    messageDiv.innerHTML = `
        <span class="timestamp">${new Date().toLocaleTimeString()}</span>
        <span class="role-badge role-${role.toLowerCase()}">${role}</span>
        <strong>Updated Draft (Turn ${turn}):</strong>
        <div class="draft-content">${escapeHtml(draft)}</div>
    `;
    log.appendChild(messageDiv);
    incrementMessageCount();
    scrollToBottom();
}

function addCriticMessage(score, feedback, turn) {
    const log = document.getElementById('conversationLog');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message critic fade-in`;
    messageDiv.innerHTML = `
        <span class="timestamp">${new Date().toLocaleTimeString()}</span>
        <span class="role-badge role-critic">Critic</span>
        <strong>Score: <span class="critic-score">${score}/10</span></strong>
        <div class="mt-2">${escapeHtml(feedback)}</div>
    `;
    log.appendChild(messageDiv);
    incrementMessageCount();
    scrollToBottom();
}

function addLogMessage(message, timestamp) {
    const log = document.getElementById('conversationLog');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message log fade-in`;
    messageDiv.innerHTML = `
        <span class="timestamp">${timestamp || new Date().toLocaleTimeString()}</span>
        ${escapeHtml(message)}
    `;
    log.appendChild(messageDiv);
    incrementMessageCount();
    scrollToBottom();
}

function addErrorMessage(message) {
    const log = document.getElementById('conversationLog');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message error fade-in`;
    messageDiv.innerHTML = `
        <span class="timestamp">${new Date().toLocaleTimeString()}</span>
        <span class="badge bg-danger">ERROR</span>
        ${escapeHtml(message)}
    `;
    log.appendChild(messageDiv);
    incrementMessageCount();
    scrollToBottom();
}

function showFinalResult(prompt, terminationReason, totalTurns) {
    const finalPromptElement = document.getElementById('finalPrompt');
    const finalPromptCard = document.getElementById('finalPromptCard');
    
    finalPromptElement.value = prompt;
    finalPromptCard.classList.remove('d-none');
    
    addStatusMessage(`Generation completed in ${totalTurns} turns. Reason: ${terminationReason}`, 'success');
    showGenerateTestDataBtn(prompt);
    setStepper(2);
}

function updateStatus(status, color) {
    const statusDisplay = document.getElementById('statusDisplay');
    statusDisplay.innerHTML = `<span class="badge bg-${color}">${status}</span>`;
}

function updateProgress(turn, role) {
    const progressInfo = document.getElementById('progressInfo');
    const currentTurn = document.getElementById('currentTurn');
    const currentRole = document.getElementById('currentRole');
    
    currentTurn.textContent = turn;
    currentRole.textContent = role;
    progressInfo.classList.remove('d-none');
}

function incrementMessageCount() {
    messageCount++;
    document.getElementById('messageCount').textContent = `${messageCount} messages`;
}

function scrollToBottom() {
    const log = document.getElementById('conversationLog');
    log.scrollTop = log.scrollHeight;
}

function clearConversation() {
    const log = document.getElementById('conversationLog');
    log.innerHTML = '<div class="text-muted text-center py-4">Starting new generation...</div>';
    messageCount = 0;
    document.getElementById('messageCount').textContent = '0 messages';
    document.getElementById('progressInfo').classList.add('d-none');
}

function hideFinalPrompt() {
    document.getElementById('finalPromptCard').classList.add('d-none');
}

function disableGenerateButton() {
    const btn = document.getElementById('generateBtn');
    btn.disabled = true;
    btn.querySelector('.spinner-border').classList.remove('d-none');
    btn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Generating...';
}

function enableGenerateButton() {
    const btn = document.getElementById('generateBtn');
    btn.disabled = false;
    btn.innerHTML = 'Generate Prompt';
}

function clearLogs() {
    clearConversation();
    hideFinalPrompt();
    updateStatus('Ready', 'secondary');
}

function downloadPrompt() {
    if (!finalPrompt) {
        alert('No prompt generated yet');
        return;
    }
    
    const blob = new Blob([finalPrompt], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'generated_prompt.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function getStatusColor(status) {
    switch (status) {
        case 'starting': return 'warning';
        case 'running': return 'info';
        case 'completed': return 'success';
        case 'error': return 'danger';
        default: return 'secondary';
    }
}

// After prompt is generated, enable test data button
function showGenerateTestDataBtn(prompt) {
    generatedPrompt = prompt;
    document.getElementById('generateTestDataBtn').disabled = false;
    document.getElementById('testDataCard').classList.add('d-none');
    document.getElementById('runPromptOnTestDataBtn').disabled = true;
    document.getElementById('testResultsCard').classList.add('d-none');
}

// Call backend to generate test data
function generateTestData() {
    document.getElementById('generateTestDataBtn').disabled = true;
    document.getElementById('testDataDisplay').innerHTML = '<span class="spinner-border spinner-border-sm"></span> Generating test input data...';
    document.getElementById('testDataCard').classList.remove('d-none');
    const testCaseCount = parseInt(document.getElementById('testCaseCount').value) || 5;
    const promptToUse = document.getElementById('finalPrompt').value;
    fetch('/api/generate_test_data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: promptToUse, test_case_count: testCaseCount })
    })
    .then(res => res.json())
    .then(data => {
        testData = data;
        document.getElementById('testDataDisplay').textContent = JSON.stringify(data, null, 2);
        document.getElementById('runPromptOnTestDataBtn').disabled = false;
        setStepper(3);
    })
    .catch(err => {
        document.getElementById('testDataDisplay').textContent = 'Error generating test data.';
        document.getElementById('runPromptOnTestDataBtn').disabled = true;
    });
}

// Call backend to run prompt on test data
function runPromptOnTestData() {
    document.getElementById('runPromptOnTestDataBtn').disabled = true;
    document.getElementById('testResultsTable').innerHTML = '<span class="spinner-border spinner-border-sm"></span> Running prompt on test data...';
    document.getElementById('testResultsCard').classList.remove('d-none');
    const promptToUse = document.getElementById('finalPrompt').value;
    fetch('/api/run_prompt_on_test_data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: promptToUse, test_data: testData })
    })
    .then(res => res.json())
    .then(results => {
        renderTestResults(results);
    })
    .catch(err => {
        document.getElementById('testResultsTable').innerHTML = 'Error running prompt on test data.';
    });
}

// Render results as a table
function renderTestResults(results) {
    let html = '<table class="table table-bordered table-sm"><thead><tr><th>Input</th><th>Output</th></tr></thead><tbody>';
    results.forEach(row => {
        html += `<tr><td><pre style="white-space:pre-wrap;">${escapeHtml(row.input)}</pre></td><td><pre style="white-space:pre-wrap;">${escapeHtml(row.output)}</pre></td></tr>`;
    });
    html += '</tbody></table>';
    document.getElementById('testResultsTable').innerHTML = html;
}

// Stepper state control
function setStepper(step) {
    [1,2,3].forEach(n => {
        const el = document.getElementById('step'+n);
        el.classList.remove('active','completed');
        if (n < step) el.classList.add('completed');
        else if (n === step) el.classList.add('active');
    });
}

// Reset workflow
function resetWorkflow() {
    document.getElementById('promptForm').reset();
    clearConversation();
    hideFinalPrompt();
    document.getElementById('generateTestDataBtn').disabled = true;
    document.getElementById('runPromptOnTestDataBtn').disabled = true;
    document.getElementById('testDataCard').classList.add('d-none');
    document.getElementById('testResultsCard').classList.add('d-none');
    setStepper(1);
}

// Update copyFinalPrompt to use textarea value
function copyFinalPrompt() {
  const el = document.getElementById('finalPrompt');
  if (!el.value) return;
  navigator.clipboard.writeText(el.value).then(() => {
    alert('Prompt copied to clipboard!');
  });
} 