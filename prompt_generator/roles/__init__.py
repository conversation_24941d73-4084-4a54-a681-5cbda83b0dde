"""Virtual teammate role implementations used by the orchestrator."""

from importlib import import_module
from pathlib import Path
from typing import Dict, Type

from .base import BaseRole

__all__ = [
    "discover_roles",
]


def discover_roles() -> Dict[str, BaseRole]:
    """Dynamically import all python files in this directory (except base) to
    collect available role classes.
    Returns a mapping name -> instance ready to use.
    """
    roles: Dict[str, BaseRole] = {}
    directory = Path(__file__).resolve().parent
    for path in directory.glob("*.py"):
        if path.stem in {"__init__", "base"}:
            continue
        module = import_module(f"{__name__}.{path.stem}")
        for attr in dir(module):
            value = getattr(module, attr)
            if isinstance(value, type) and issubclass(value, BaseRole) and value is not BaseRole:
                roles[value.__name__] = value()  # instantiate default
    return roles 