"""OutputGuardian – validates that the prompt instructs the model to reply in a specific format.

Currently we support two formats:
1. JSON_OUTPUT: signals that the model should respond with a JSON object
2. MARKDOWN_OUTPUT: signals that the model should respond with Markdown format

In production you would load an external JSON Schema and run deterministic validation, 
but this keeps the demo light.
"""

from typing import Any, Dict, List

from .base import BaseRole, RoleOutput


class OutputGuardian(BaseRole):
    allowed_markers: List[str] = ["JSON_OUTPUT:", "MARKDOWN_OUTPUT:"]

    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        draft = state.get("draft", "")
        
        # Check if any of the allowed markers are present
        found_markers = [marker for marker in self.allowed_markers if marker in draft]
        passes = len(found_markers) > 0
        
        if passes:
            marker_used = found_markers[0]
            feedback = f"✔ Draft contains required marker '{marker_used}'."
        else:
            feedback = f"✖ Draft is missing required marker. Must include one of: {', '.join(self.allowed_markers)}."
        
        return RoleOutput({
            "output_guardian_pass": passes,
            "output_guardian_feedback": feedback,
            "log": f"OutputGuardian check -> {feedback}",
        }) 