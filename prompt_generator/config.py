"""Configuration utilities."""

from __future__ import annotations

import os
from pathlib import Path
from typing import Any, Dict, Optional

import yaml
from pydantic_settings import BaseSettings

DEFAULT_CONFIG_PATH = Path(os.getenv("PROMPTGEN_CONFIG", "config.yaml"))


def load_config(path: Optional[os.PathLike | str] = None) -> Dict[str, Any]:
    """Load YAML configuration file if it exists.

    Returns an empty dict if no config file present, which allows library to
    fall back on defaults.
    """
    path = Path(path or DEFAULT_CONFIG_PATH)
    if not path.exists():
        print(f"Warning: Config file {path} not found. Using default values.")
        return {}
    try:
        with path.open("r", encoding="utf-8") as fh:
            config = yaml.safe_load(fh) or {}
        return config
    except Exception as e:
        print(f"Error loading config file: {e}")
        return {}


class Settings(BaseSettings):
    openai_api_key: str = ""
    default_model: str = "o4-mini-2025-04-16"  # Default AI model
    reasoning_effort: str = "medium"  # Default value
    max_completion_tokens: int = 4000  # Reduced to avoid context length issues
    demo_mode: bool = False  # Use mock responses when True

    class Config:
        env_prefix = "OPENAI_"
        env_file = ".env"

    def load_from_yaml(self, path: Optional[os.PathLike | str] = None):
        cfg = load_config(path)
        if cfg.get("openai_api_key"):
            self.openai_api_key = cfg["openai_api_key"]
        else:
            # Try to get from environment
            env_api_key = os.environ.get("OPENAI_API_KEY")
            if env_api_key:
                self.openai_api_key = env_api_key
            else:
                print("Warning: No OpenAI API key found in config or environment. Using demo mode.")
                self.demo_mode = True
        
        # Load model settings if available
        if cfg.get("model_settings"):
            model_settings = cfg["model_settings"]
            if "reasoning_effort" in model_settings:
                self.reasoning_effort = model_settings["reasoning_effort"]
            if "max_completion_tokens" in model_settings:
                # Make sure we accept the higher limit for o3 models
                tokens = model_settings["max_completion_tokens"]
                self.max_completion_tokens = tokens


settings = Settings()
settings.load_from_yaml() 