"""High-level API for external callers."""

from __future__ import annotations

from typing import <PERSON><PERSON>, Dict, Any

from .orchestrator import Orchestrator, DEFAULT_TARGET_SCORE, DEFAULT_MAX_TURNS


def run_orchestrator(task_description: str, *, target_score: float = DEFAULT_TARGET_SCORE, max_turns: int = DEFAULT_MAX_TURNS) -> Tuple[Dict[str, Any], list[Dict[str, Any]]]:
    """Run the default orchestrator for *task_description* and return (*state*, *history*)."""
    orchestrator = Orchestrator(target_score=target_score, max_turns=max_turns)
    return orchestrator.run(task_description) 