digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/token_optimizer_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/token_optimizer_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"TokenOptimizer – suggests ways to reduce token usage in the prompt.\"\"\"\limport re\lfrom typing import Any, Dict, Optional\lfrom ..config import load_config\lfrom .base import BaseRole, RoleOutput\lconfig = load_config()\ltoken_optimizer_config = config.get('token_optimizer', {})\lMIN_SAVINGS_PCT = token_optimizer_config.get('min_savings_pct'\l    ) if 'min_savings_pct' in token_optimizer_config else None\lMIN_TOKENS_SAVED = token_optimizer_config.get('min_tokens_saved'\l    ) if 'min_tokens_saved' in token_optimizer_config else None\lMAX_SAVINGS_PCT = token_optimizer_config.get('max_savings_pct'\l    ) if 'max_savings_pct' in token_optimizer_config else None" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	1 -> 2 [label=calls style=dashed]
	1 -> 3 [label=calls style=dashed]
	1 -> 4 [label=calls style=dashed]
	1 -> 5 [label=calls style=dashed]
	1 -> 6 [label=calls style=dashed]
	subgraph cluster_1 {
		graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		2 [label=load_config color="#E552FF" shape=tab style=filled]
		3 [label="config.get" color="#E552FF" shape=tab style=filled]
		4 [label="token_optimizer_config.get" color="#E552FF" shape=tab style=filled]
		5 [label="token_optimizer_config.get" color="#E552FF" shape=tab style=filled]
		6 [label="token_optimizer_config.get" color="#E552FF" shape=tab style=filled]
	}
	7 [label="if MIN_SAVINGS_PCT is None and MIN_TOKENS_SAVED is None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
	8 [label="MIN_SAVINGS_PCT = 5" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	9 [label="class TokenOptimizer(BaseRole):...
" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	8 -> 9 [label="" color=black]
	7 -> 8 [label="MIN_SAVINGS_PCT is None and MIN_TOKENS_SAVED is None" color=green]
	7 -> 9 [label="(not (MIN_SAVINGS_PCT is None and MIN_TOKENS_SAVED is None))" color=red]
	1 -> 7 [label="" color=black]
	subgraph cluster0TokenOptimizer {
		graph [compound=True fontname="DejaVu Sans Mono" label=TokenOptimizer pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		11 [label="system_prompt = \"\"\"You are **Prompt-Compressor-PE-v1**, a token-efficiency specialist.\l=============  TASK  =============\lRewrite the draft prompt so it uses as few tokens as possible **without** changing:\l• semantic intent\l• role titles and wrapper tags (<system>, <user>, placeholders {{…}})\l• explicit constraints (confidentiality, GAAP accuracy, refusal rules)\l• expected-output description and section headers\l• citation-style instruction\l=============  RULES  =============\l1. Remove superfluous words, redundancies, and filler phrases.\l2. Keep bullets or numbered lists when they improve clarity ≥ tokens saved.\l3. Do **NOT** introduce abbreviations that a downstream LLM might misinterpret.\l4. Preserve line breaks that delimit sections; do not collapse everything into one paragraph.\l5. Return **only** the compressed prompt pair—no commentary, no JSON.\l6. Preserve critical meaning - do not remove essential information just to save tokens.\l\"\"\"\ldef estimate_tokens(self, text: str) ->int:...\ldef eval(self, state: Dict[str, Any]) ->RoleOutput:..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0estimate_tokens {
			graph [compound=True fontname="DejaVu Sans Mono" label=estimate_tokens pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			13 [label="return max(1, len(text) // 4)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		}
		subgraph cluster0eval {
			graph [compound=True fontname="DejaVu Sans Mono" label=eval pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			17 [label="draft = state.get('draft')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			17 -> 18 [label=calls style=dashed]
			subgraph cluster_17 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				18 [label="state.get" color="#E552FF" shape=tab style=filled]
			}
			19 [label="if not draft:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			20 [label="return RoleOutput({'log': 'TokenOptimizer skipped – no draft.'})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			19 -> 20 [label="not draft" color=green]
			21 [label="original_tokens = self.estimate_tokens(draft)\lmessages = [{'role': 'system', 'content': self.system_prompt}, {'role':\l    'user', 'content': draft}]\lcompressed = self._call_llm(messages)\lcompressed_tokens = self.estimate_tokens(compressed)\ltokens_saved = original_tokens - compressed_tokens\lsaving_pct = tokens_saved / original_tokens * 100\lshould_apply_optimization = True\lthreshold_messages = []" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			21 -> 23 [label=calls style=dashed]
			21 -> 24 [label=calls style=dashed]
			21 -> 25 [label=calls style=dashed]
			subgraph cluster_21 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				23 [label="self.estimate_tokens" color="#E552FF" shape=tab style=filled]
				24 [label="self._call_llm" color="#E552FF" shape=tab style=filled]
				25 [label="self.estimate_tokens" color="#E552FF" shape=tab style=filled]
			}
			26 [label="if MIN_SAVINGS_PCT is not None and saving_pct < MIN_SAVINGS_PCT:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			27 [label="should_apply_optimization = False\lthreshold_messages.append(\l    f'{saving_pct:.1f}% below {MIN_SAVINGS_PCT}% threshold')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			27 -> 29 [label=calls style=dashed]
			subgraph cluster_27 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				29 [label="threshold_messages.append" color="#E552FF" shape=tab style=filled]
			}
			28 [label="if MIN_TOKENS_SAVED is not None and tokens_saved < MIN_TOKENS_SAVED:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			30 [label="should_apply_optimization = False\lthreshold_messages.append(\l    f'{tokens_saved} tokens below {MIN_TOKENS_SAVED} threshold')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			30 -> 32 [label=calls style=dashed]
			subgraph cluster_30 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				32 [label="threshold_messages.append" color="#E552FF" shape=tab style=filled]
			}
			31 [label="if MAX_SAVINGS_PCT is not None and saving_pct > MAX_SAVINGS_PCT:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			33 [label="should_apply_optimization = False\lthreshold_messages.append(\l    f'{saving_pct:.1f}% exceeds {MAX_SAVINGS_PCT}% maximum - probable content loss'\l    )" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			33 -> 35 [label=calls style=dashed]
			subgraph cluster_33 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				35 [label="threshold_messages.append" color="#E552FF" shape=tab style=filled]
			}
			34 [label="if not should_apply_optimization:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			36 [label="return RoleOutput({'token_saving_pct': saving_pct, 'tokens_saved':\l    tokens_saved, 'log':\l    f\"TokenOptimizer: No changes applied. {', '.join(threshold_messages)}.\"})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			34 -> 36 [label="not should_apply_optimization" color=green]
			37 [label="return RoleOutput({'draft': compressed, 'token_saving_pct': saving_pct,\l    'tokens_saved': tokens_saved, 'log':\l    f'TokenOptimizer reduced by {tokens_saved} tokens ({saving_pct:.1f}%).'})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			34 -> 37 [label="(not not should_apply_optimization)" color=red]
			33 -> 34 [label="" color=black]
			31 -> 33 [label="MAX_SAVINGS_PCT is not None and saving_pct > MAX_SAVINGS_PCT" color=green]
			31 -> 34 [label="(not (MAX_SAVINGS_PCT is not None and saving_pct > MAX_SAVINGS_PCT))" color=red]
			30 -> 31 [label="" color=black]
			28 -> 30 [label="MIN_TOKENS_SAVED is not None and tokens_saved < MIN_TOKENS_SAVED" color=green]
			28 -> 31 [label="(not (MIN_TOKENS_SAVED is not None and tokens_saved < MIN_TOKENS_SAVED))" color=red]
			27 -> 28 [label="" color=black]
			26 -> 27 [label="MIN_SAVINGS_PCT is not None and saving_pct < MIN_SAVINGS_PCT" color=green]
			26 -> 28 [label="(not (MIN_SAVINGS_PCT is not None and saving_pct < MIN_SAVINGS_PCT))" color=red]
			21 -> 26 [label="" color=black]
			19 -> 21 [label="(not not draft)" color=red]
			17 -> 19 [label="" color=black]
		}
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
