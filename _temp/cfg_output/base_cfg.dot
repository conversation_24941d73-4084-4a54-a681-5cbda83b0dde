digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/base_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/base_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="from __future__ import annotations\limport abc\lfrom dataclasses import dataclass, field\limport os\limport sys\lfrom typing import Any, Dict, List, Optional\lfrom ..config import settings\lfrom pydantic import BaseModel\lfrom openai import OpenAI\lfrom openai import AuthenticationError, RateLimitError, APIConnectionError, APIError, BadRequestError\l@dataclass\lclass RoleOutput:\l    \"\"\"Standardised output from a role evaluation.\l\l    Each role may add arbitrary keys to update shared *state* but MUST include\l    a `log` key with human-readable notes explaining what it did. That ensures\l    the orchestrator can always display something meaningful.\l    \"\"\"\l    data: Dict[str, Any] = field(default_factory=dict)\l\l    def to_state_update(self) ->Dict[str, Any]:\l        \"\"\"Return key/value pairs that will be merged into orchestration state.\"\"\"\l        return self.data\lclass BaseRole(abc.ABC):\l    \"\"\"Abstract base class every role must subclass.\"\"\"\l    name: str\l\l    def __init__(self, name: (str | None)=None):\l        self.name = name or self.__class__.__name__\l    system_prompt: str | None = None\l    model: str = 'o3-2025-04-16'\l    temperature: float = 1.0\l    max_tokens: int = 10000\l\l    @abc.abstractmethod\l    def eval(self, state: Dict[str, Any]) ->RoleOutput:\l        \"\"\"Run the role on current *state* and return updates.\l\l        Subclasses should NOT mutate *state* in-place; instead return a\l        RoleOutput whose ``data`` will be merged by the orchestrator.\l        \"\"\"\l\l    def _call_llm(self, messages: list[dict[str, str]], **kwargs) ->str:\l        \"\"\"Utility wrapper to send ChatCompletion request.\l\l        Falls back to providing a mock response if API is unavailable.\l        \"\"\"\l        try:\l            if not settings.openai_api_key or len(settings.openai_api_key\l                ) < 20:\l                print(\l                    f'Warning: Invalid or missing OpenAI API key. Using mock response.'\l                    )\l                return self._mock_response(messages)\l            client = OpenAI(api_key=settings.openai_api_key)\l            model_to_use = kwargs.get('model', self.model)\l            max_tokens = min(kwargs.get('max_tokens', 5000), settings.\l                max_completion_tokens)\l            print(\l                f'Requesting completion with model {model_to_use}, max_tokens: {max_tokens}'\l                )\l            response = client.chat.completions.create(model=model_to_use,\l                messages=messages, temperature=kwargs.get('temperature',\l                self.temperature), max_completion_tokens=max_tokens)\l            return response.choices[0].message.content.strip()\l        except (AuthenticationError, BadRequestError) as e:\l            print(f'API Error: {str(e)}')\l            return self._mock_response(messages)\l        except (APIError, APIConnectionError, RateLimitError) as e:\l            print(f'API Error: {str(e)}')\l            return self._mock_response(messages)\l        except Exception as e:\l            print(f'Unexpected error: {str(e)}')\l            return self._mock_response(messages)\l\l    def _mock_response(self, messages: list[dict[str, str]]) ->str:\l        \"\"\"Generate a mock response for demonstration purposes.\"\"\"\l        if self.name == 'Writer':\l            return \"\"\"System Message:\lYou are WeatherBot, a helpful assistant that provides weather forecasts.\lYou must always respond with accurate and up-to-date information.\lYou should never make up weather data, but instead ask for clarification.\l\lUser Message:\lI need to know the weather forecast for {{LOCATION}} on {{DATE}}.\lPlease provide temperature, precipitation chance, and wind conditions.\"\"\"\l        elif self.name == 'Critic':\l            return 'The prompt is good but could be improved. Score: 7.5/10'\l        elif self.name == 'Editor':\l            return \"\"\"System Message:\lYou are \"WeatherBot\", a precise and reliable assistant that provides weather forecasts.\lYou must always respond with accurate and up-to-date information based on reliable weather data sources.\lYou should never make up weather data. If data is unavailable or location is unclear, ask for clarification.\lAll user data should be treated as confidential and not shared or stored.\l\lUser Message:\lI need to know the weather forecast for {{LOCATION}} on {{DATE}}.\lPlease provide:\l- Temperature (high/low in °C and °F)\l- Precipitation chance (%)\l- Wind conditions (speed in km/h and mph, plus direction)\l- Air quality index (with interpretation)\l- Any weather warnings or alerts\l\lIf the location is ambiguous, ask for more specific information before providing a forecast.\"\"\"\l        elif self.name == 'OutputGuardian':\l            return 'Format is compliant.'\l        elif self.name == 'TokenOptimizer':\l            for msg in messages:\l                if msg['role'] == 'user' and 'draft' in msg['content']:\l                    return msg['content'].split('draft:', 1)[1].strip()\l            return 'Token optimization complete.'\l        else:\l            return 'Mock response for ' + self.name\lclass Role(BaseModel):\l    name: str\l    description: str\l    model: str = 'o4-mini-2025-04-16'\l    temperature: float = 0.7\l    max_tokens: int = 5000\l    system_prompt: str = ''\l\l    def __init__(self, **data):\l        super().__init__(**data)\l        self.client = OpenAI(api_key=settings.openai_api_key)\l\l    def _call_llm(self, messages: List[Dict[str, str]], **kwargs) ->str:\l        \"\"\"Call the LLM with the given messages.\"\"\"\l        try:\l            if not settings.openai_api_key or len(settings.openai_api_key\l                ) < 20:\l                print(\l                    f'Warning: Invalid or missing OpenAI API key. Using mock response.'\l                    )\l                return self._mock_response(messages)\l            model_to_use = kwargs.get('model', self.model)\l            max_tokens = min(kwargs.get('max_tokens', 3000), settings.\l                max_completion_tokens)\l            response = self.client.chat.completions.create(model=\l                model_to_use, messages=messages, temperature=kwargs.get(\l                'temperature', self.temperature), max_completion_tokens=\l                max_tokens)\l            return response.choices[0].message.content\l        except (AuthenticationError, BadRequestError) as e:\l            print(f'API Error: {str(e)}')\l            return self._mock_response(messages)\l        except (APIError, APIConnectionError, RateLimitError) as e:\l            print(f'API Error: {str(e)}')\l            return self._mock_response(messages)\l        except Exception as e:\l            print(f'Unexpected error: {str(e)}')\l            return self._mock_response(messages)\l\l    def _mock_response(self, messages: List[Dict[str, str]]) ->str:\l        \"\"\"Generate a mock response for demonstration purposes.\"\"\"\l        return f'Mock response for {self.name}'\l\l    def eval(self, state: Dict[str, Any]) ->Dict[str, Any]:\l        \"\"\"Evaluate the current state and return the next state.\"\"\"\l        raise NotImplementedError('Subclasses must implement eval()')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	subgraph cluster0RoleOutput {
		graph [compound=True fontname="DejaVu Sans Mono" label=RoleOutput pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		3 [label="\"\"\"Standardised output from a role evaluation.\l\l    Each role may add arbitrary keys to update shared *state* but MUST include\l    a `log` key with human-readable notes explaining what it did. That ensures\l    the orchestrator can always display something meaningful.\l    \"\"\"\ldata: Dict[str, Any] = field(default_factory=dict)\ldef to_state_update(self) ->Dict[str, Any]:..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		3 -> 4 [label=calls style=dashed]
		subgraph cluster_3 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			4 [label=field color="#E552FF" shape=tab style=filled]
		}
		subgraph cluster0to_state_update {
			graph [compound=True fontname="DejaVu Sans Mono" label=to_state_update pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			6 [label="\"\"\"Return key/value pairs that will be merged into orchestration state.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			7 [label="return self.data" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			6 -> 7 [label="" color=black]
		}
	}
	subgraph cluster0BaseRole {
		graph [compound=True fontname="DejaVu Sans Mono" label=BaseRole pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		12 [label="\"\"\"Abstract base class every role must subclass.\"\"\"\lname: str\ldef __init__(self, name: (str | None)=None):...\lsystem_prompt: str | None = None\lmodel: str = 'o3-2025-04-16'\ltemperature: float = 1.0\lmax_tokens: int = 10000\<EMAIL>...\ldef _call_llm(self, messages: list[dict[str, str]], **kwargs) ->str:...\ldef _mock_response(self, messages: list[dict[str, str]]) ->str:..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0__init__ {
			graph [compound=True fontname="DejaVu Sans Mono" label=__init__ pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			14 [label="self.name = name or self.__class__.__name__" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		}
		subgraph cluster0eval {
			graph [compound=True fontname="DejaVu Sans Mono" label=eval pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			17 [label="\"\"\"Run the role on current *state* and return updates.\l\l        Subclasses should NOT mutate *state* in-place; instead return a\l        RoleOutput whose ``data`` will be merged by the orchestrator.\l        \"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		}
		subgraph cluster0_call_llm {
			graph [compound=True fontname="DejaVu Sans Mono" label=_call_llm pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			20 [label="\"\"\"Utility wrapper to send ChatCompletion request.\l\l        Falls back to providing a mock response if API is unavailable.\l        \"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			21 [label="try:" fillcolor=orange shape=Mdiamond style="filled,solid"]
			28 [label="print(f'API Error: {str(e)}')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			28 -> 29 [label=calls style=dashed]
			subgraph cluster_28 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				29 [label=print color="#E552FF" shape=tab style=filled]
				30 [label=str color="#E552FF" shape=tab style=filled]
				29 -> 30 [color=black]
			}
			31 [label="return self._mock_response(messages)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			28 -> 31 [label="" color=black]
			33 [label="print(f'Unexpected error: {str(e)}')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			33 -> 34 [label=calls style=dashed]
			subgraph cluster_33 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				34 [label=print color="#E552FF" shape=tab style=filled]
				35 [label=str color="#E552FF" shape=tab style=filled]
				34 -> 35 [color=black]
			}
			36 [label="return self._mock_response(messages)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			33 -> 36 [label="" color=black]
			38 [label="if not settings.openai_api_key or len(settings.openai_api_key) < 20:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			39 [label="print(f'Warning: Invalid or missing OpenAI API key. Using mock response.')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			39 -> 41 [label=calls style=dashed]
			subgraph cluster_39 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				41 [label=print color="#E552FF" shape=tab style=filled]
			}
			42 [label="return self._mock_response(messages)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			39 -> 42 [label="" color=black]
			38 -> 39 [label="not settings.openai_api_key or len(settings.openai_api_key) < 20" color=green]
			40 [label="client = OpenAI(api_key=settings.openai_api_key)\lmodel_to_use = kwargs.get('model', self.model)\lmax_tokens = min(kwargs.get('max_tokens', 5000), settings.max_completion_tokens\l    )\lprint(\l    f'Requesting completion with model {model_to_use}, max_tokens: {max_tokens}'\l    )\lresponse = client.chat.completions.create(model=model_to_use, messages=\l    messages, temperature=kwargs.get('temperature', self.temperature),\l    max_completion_tokens=max_tokens)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			40 -> 44 [label=calls style=dashed]
			40 -> 45 [label=calls style=dashed]
			40 -> 46 [label=calls style=dashed]
			40 -> 48 [label=calls style=dashed]
			40 -> 49 [label=calls style=dashed]
			subgraph cluster_40 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				44 [label=OpenAI color="#E552FF" shape=tab style=filled]
				45 [label="kwargs.get" color="#E552FF" shape=tab style=filled]
				46 [label=min color="#E552FF" shape=tab style=filled]
				47 [label="kwargs.get" color="#E552FF" shape=tab style=filled]
				46 -> 47 [color=black]
				48 [label=print color="#E552FF" shape=tab style=filled]
				49 [label="client.chat.completions.create" color="#E552FF" shape=tab style=filled]
			}
			50 [label="return response.choices[0].message.content.strip()" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			40 -> 50 [label="" color=black]
			38 -> 40 [label="(not (not settings.openai_api_key or len(settings.openai_api_key) < 20))" color=red]
			21 -> 38 [label="" color=black]
			20 -> 21 [label="" color=black]
		}
		subgraph cluster0_mock_response {
			graph [compound=True fontname="DejaVu Sans Mono" label=_mock_response pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			54 [label="\"\"\"Generate a mock response for demonstration purposes.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			55 [label="if self.name == 'Writer':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			56 [label="return \"\"\"System Message:\lYou are WeatherBot, a helpful assistant that provides weather forecasts.\lYou must always respond with accurate and up-to-date information.\lYou should never make up weather data, but instead ask for clarification.\l\lUser Message:\lI need to know the weather forecast for {{LOCATION}} on {{DATE}}.\lPlease provide temperature, precipitation chance, and wind conditions.\"\"\"" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			55 -> 56 [label="self.name == 'Writer'" color=green]
			58 [label="if self.name == 'Critic':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			59 [label="return 'The prompt is good but could be improved. Score: 7.5/10'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			58 -> 59 [label="self.name == 'Critic'" color=green]
			61 [label="if self.name == 'Editor':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			62 [label="return \"\"\"System Message:\lYou are \"WeatherBot\", a precise and reliable assistant that provides weather forecasts.\lYou must always respond with accurate and up-to-date information based on reliable weather data sources.\lYou should never make up weather data. If data is unavailable or location is unclear, ask for clarification.\lAll user data should be treated as confidential and not shared or stored.\l\lUser Message:\lI need to know the weather forecast for {{LOCATION}} on {{DATE}}.\lPlease provide:\l- Temperature (high/low in °C and °F)\l- Precipitation chance (%)\l- Wind conditions (speed in km/h and mph, plus direction)\l- Air quality index (with interpretation)\l- Any weather warnings or alerts\l\lIf the location is ambiguous, ask for more specific information before providing a forecast.\"\"\"" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			61 -> 62 [label="self.name == 'Editor'" color=green]
			64 [label="if self.name == 'OutputGuardian':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			65 [label="return 'Format is compliant.'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			64 -> 65 [label="self.name == 'OutputGuardian'" color=green]
			67 [label="if self.name == 'TokenOptimizer':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			68 [label="for msg in messages:" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
			72 [label="if msg['role'] == 'user' and 'draft' in msg['content']:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			74 [label="return msg['content'].split('draft:', 1)[1].strip()" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			72 -> 74 [label="msg['role'] == 'user' and 'draft' in msg['content']" color=green]
			72 -> 68 [label="(not (msg['role'] == 'user' and 'draft' in msg['content']))" color=red]
			68 -> 72 [label=messages color=green]
			73 [label="return 'Token optimization complete.'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			68 -> 73 [label="" color=green]
			67 -> 68 [label="self.name == 'TokenOptimizer'" color=green]
			70 [label="return 'Mock response for ' + self.name" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			67 -> 70 [label="(self.name != 'TokenOptimizer')" color=red]
			64 -> 67 [label="(self.name != 'OutputGuardian')" color=red]
			61 -> 64 [label="(self.name != 'Editor')" color=red]
			58 -> 61 [label="(self.name != 'Critic')" color=red]
			55 -> 58 [label="(self.name != 'Writer')" color=red]
			54 -> 55 [label="" color=black]
		}
	}
	subgraph cluster0Role {
		graph [compound=True fontname="DejaVu Sans Mono" label=Role pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		85 [label="name: str\ldescription: str\lmodel: str = 'o4-mini-2025-04-16'\ltemperature: float = 0.7\lmax_tokens: int = 5000\lsystem_prompt: str = ''\ldef __init__(self, **data):...\ldef _call_llm(self, messages: List[Dict[str, str]], **kwargs) ->str:...\ldef _mock_response(self, messages: List[Dict[str, str]]) ->str:...\ldef eval(self, state: Dict[str, Any]) ->Dict[str, Any]:..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster1__init__ {
			graph [compound=True fontname="DejaVu Sans Mono" label=__init__ pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			87 [label="super().__init__(**data)\lself.client = OpenAI(api_key=settings.openai_api_key)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			87 -> 88 [label=calls style=dashed]
			87 -> 89 [label=calls style=dashed]
			subgraph cluster_87 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				88 [label="super.__init__" color="#E552FF" shape=tab style=filled]
				89 [label=OpenAI color="#E552FF" shape=tab style=filled]
			}
		}
		subgraph cluster1_call_llm {
			graph [compound=True fontname="DejaVu Sans Mono" label=_call_llm pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			92 [label="\"\"\"Call the LLM with the given messages.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			93 [label="try:" fillcolor=orange shape=Mdiamond style="filled,solid"]
			100 [label="print(f'API Error: {str(e)}')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			100 -> 101 [label=calls style=dashed]
			subgraph cluster_100 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				101 [label=print color="#E552FF" shape=tab style=filled]
				102 [label=str color="#E552FF" shape=tab style=filled]
				101 -> 102 [color=black]
			}
			103 [label="return self._mock_response(messages)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			100 -> 103 [label="" color=black]
			105 [label="print(f'Unexpected error: {str(e)}')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			105 -> 106 [label=calls style=dashed]
			subgraph cluster_105 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				106 [label=print color="#E552FF" shape=tab style=filled]
				107 [label=str color="#E552FF" shape=tab style=filled]
				106 -> 107 [color=black]
			}
			108 [label="return self._mock_response(messages)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			105 -> 108 [label="" color=black]
			110 [label="if not settings.openai_api_key or len(settings.openai_api_key) < 20:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			111 [label="print(f'Warning: Invalid or missing OpenAI API key. Using mock response.')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			111 -> 113 [label=calls style=dashed]
			subgraph cluster_111 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				113 [label=print color="#E552FF" shape=tab style=filled]
			}
			114 [label="return self._mock_response(messages)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			111 -> 114 [label="" color=black]
			110 -> 111 [label="not settings.openai_api_key or len(settings.openai_api_key) < 20" color=green]
			112 [label="model_to_use = kwargs.get('model', self.model)\lmax_tokens = min(kwargs.get('max_tokens', 3000), settings.max_completion_tokens\l    )\lresponse = self.client.chat.completions.create(model=model_to_use, messages\l    =messages, temperature=kwargs.get('temperature', self.temperature),\l    max_completion_tokens=max_tokens)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			112 -> 116 [label=calls style=dashed]
			112 -> 117 [label=calls style=dashed]
			112 -> 119 [label=calls style=dashed]
			subgraph cluster_112 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				116 [label="kwargs.get" color="#E552FF" shape=tab style=filled]
				117 [label=min color="#E552FF" shape=tab style=filled]
				118 [label="kwargs.get" color="#E552FF" shape=tab style=filled]
				117 -> 118 [color=black]
				119 [label="self.client.chat.completions.create" color="#E552FF" shape=tab style=filled]
			}
			120 [label="return response.choices[0].message.content" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			112 -> 120 [label="" color=black]
			110 -> 112 [label="(not (not settings.openai_api_key or len(settings.openai_api_key) < 20))" color=red]
			93 -> 110 [label="" color=black]
			92 -> 93 [label="" color=black]
		}
		subgraph cluster1_mock_response {
			graph [compound=True fontname="DejaVu Sans Mono" label=_mock_response pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			124 [label="\"\"\"Generate a mock response for demonstration purposes.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			125 [label="return f'Mock response for {self.name}'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			124 -> 125 [label="" color=black]
		}
		subgraph cluster1eval {
			graph [compound=True fontname="DejaVu Sans Mono" label=eval pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			129 [label="\"\"\"Evaluate the current state and return the next state.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			130 [label="raise NotImplementedError('Subclasses must implement eval()')" fillcolor="#98fb98" shape=house style="filled,solid"]
			129 -> 130 [label="" color=black]
		}
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
