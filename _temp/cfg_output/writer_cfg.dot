digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/writer_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/writer_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"Writer role – produces the first draft prompt based on the task description.\"\"\"\lfrom typing import Any, Dict\lfrom .base import BaseRole, RoleOutput\lclass Writer(BaseRole):\l    system_prompt = (\l        'You are **Prompt-Architect-PE-v1**, a senior LLM-prompt engineer who specialises inprivate-equity research, transaction due-diligence, and portfolio-monitoring tasks.================== GUIDING PRINCIPLES ==================1. **Accuracy first** – require IFRS/GAAP terminology; never invent or round numbers.2. **Data security** – remind downstream model that all content is confidential.3. **Clear contract** – every prompt you output must   • declare *role* & *tone*,   • list *inputs* (placeholders in ([[double-square-brackets]]),   • specify *format* of the answer (tables / bullet hierarchy / JSON / etc.),   • state *constraints* (cite style, numeric precision, refusal policy),   • outline *evaluation rubric* if the task involves scoring.4. **Fail loudly** – instruct the model to return “Insufficient data” rather than guessing.5. **Citations** – default to Markdown footnotes unless otherwise specified by the user6. The prompt should consist of both a System and User message.=================== OUTPUT YOU MUST RETURN ================Return **nothing except** the fully-formed prompt pair below, with placeholders the caller will fill at runtime.'\l        )\l\l    def eval(self, state: Dict[str, Any]) ->RoleOutput:\l        if 'draft' in state:\l            return RoleOutput({'log':\l                'Writer skipped – draft already present.'})\l        task = state.get('task', '')\l        user_prompt = 'Task description: ' + task + '\n'\l        messages = [{'role': 'system', 'content': self.system_prompt}, {\l            'role': 'user', 'content': user_prompt}]\l        draft = self._call_llm(messages)\l        return RoleOutput({'draft': draft, 'log':\l            'Writer produced initial draft prompt.'})" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	subgraph cluster0Writer {
		graph [compound=True fontname="DejaVu Sans Mono" label=Writer pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		3 [label="system_prompt = (\l    'You are **Prompt-Architect-PE-v1**, a senior LLM-prompt engineer who specialises inprivate-equity research, transaction due-diligence, and portfolio-monitoring tasks.================== GUIDING PRINCIPLES ==================1. **Accuracy first** – require IFRS/GAAP terminology; never invent or round numbers.2. **Data security** – remind downstream model that all content is confidential.3. **Clear contract** – every prompt you output must   • declare *role* & *tone*,   • list *inputs* (placeholders in ([[double-square-brackets]]),   • specify *format* of the answer (tables / bullet hierarchy / JSON / etc.),   • state *constraints* (cite style, numeric precision, refusal policy),   • outline *evaluation rubric* if the task involves scoring.4. **Fail loudly** – instruct the model to return “Insufficient data” rather than guessing.5. **Citations** – default to Markdown footnotes unless otherwise specified by the user6. The prompt should consist of both a System and User message.=================== OUTPUT YOU MUST RETURN ================Return **nothing except** the fully-formed prompt pair below, with placeholders the caller will fill at runtime.'\l    )\ldef eval(self, state: Dict[str, Any]) ->RoleOutput:..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0eval {
			graph [compound=True fontname="DejaVu Sans Mono" label=eval pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			5 [label="if 'draft' in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			6 [label="return RoleOutput({'log': 'Writer skipped – draft already present.'})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			5 -> 6 [label="'draft' in state" color=green]
			7 [label="task = state.get('task', '')\luser_prompt = 'Task description: ' + task + '\n'\lmessages = [{'role': 'system', 'content': self.system_prompt}, {'role':\l    'user', 'content': user_prompt}]\ldraft = self._call_llm(messages)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			7 -> 9 [label=calls style=dashed]
			7 -> 10 [label=calls style=dashed]
			subgraph cluster_7 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				9 [label="state.get" color="#E552FF" shape=tab style=filled]
				10 [label="self._call_llm" color="#E552FF" shape=tab style=filled]
			}
			11 [label="return RoleOutput({'draft': draft, 'log':\l    'Writer produced initial draft prompt.'})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			7 -> 11 [label="" color=black]
			5 -> 7 [label="('draft' not in state)" color=red]
		}
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
