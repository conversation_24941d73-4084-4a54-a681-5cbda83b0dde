digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/core_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/core_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"High-level API for external callers.\"\"\"\lfrom __future__ import annotations\lfrom typing import Tuple, Dict, Any\lfrom .orchestrator import Orchestrator, DEFAULT_TARGET_SCORE, DEFAULT_MAX_TURNS\ldef run_orchestrator(task_description: str, *, target_score: float=..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	subgraph cluster0run_orchestrator {
		graph [compound=True fontname="DejaVu Sans Mono" label=run_orchestrator pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		3 [label="\"\"\"Run the default orchestrator for *task_description* and return (*state*, *history*).\"\"\"\lorchestrator = Orchestrator(target_score=target_score, max_turns=max_turns)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		3 -> 4 [label=calls style=dashed]
		subgraph cluster_3 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			4 [label=Orchestrator color="#E552FF" shape=tab style=filled]
		}
		5 [label="return orchestrator.run(task_description)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		3 -> 5 [label="" color=black]
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
