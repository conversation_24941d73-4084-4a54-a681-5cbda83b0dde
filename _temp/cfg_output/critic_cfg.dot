digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/critic_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/critic_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"Critic role - evaluates quality of the prompt and suggests improvements.\"\"\"\limport re\lfrom typing import Any, Dict, Optional\lfrom .base import BaseRole, RoleOutput\lclass Critic(BaseRole):\l    system_prompt = \"\"\"You are a Prompt Engineering Critic with expertise in creating effective prompts for LLMs.\l    Your task is to evaluate the quality of a prompt based on the following criteria:\l    \l    1. Clarity: Is the prompt clear about what is being asked?\l    2. Specificity: Does it include enough details to guide the response?\l    3. Context: Does it provide necessary background information?\l    4. Format guidance: Does it specify how the answer should be structured?\l    5. Constraints: Does it include relevant constraints or guardrails?\l    \l    You MUST assign a score from 0-10 with one decimal place (e.g., 7.5).\l    You MUST include \"Score: X.Y/10\" at the end of your feedback.\l    \l    Provide specific, actionable feedback that would help improve the prompt.\l    \"\"\"\l\l    def eval(self, state: Dict[str, Any]) ->RoleOutput:\l        draft = state.get('draft', '')\l        if not draft:\l            return RoleOutput({'log': 'Critic skipped – no draft to evaluate.'}\l                )\l        user_prompt = f\"\"\"Task: {state.get('task', 'No task provided')}\l\lDraft prompt to evaluate:\l\l{draft}\l\lPlease evaluate this prompt on a scale of 0-10 (with one decimal place), and provide specific feedback for improvement. End with 'Score: X.Y/10'.\"\"\"\l        messages = [{'role': 'system', 'content': self.system_prompt}, {\l            'role': 'user', 'content': user_prompt}]\l        feedback = self._call_llm(messages)\l        score_match = re.search('Score:\\s*(\\d+\\.\\d+)\\/10', feedback)\l        score: Optional[float] = None\l        if score_match:\l            try:\l                score = float(score_match.group(1))\l            except (ValueError, IndexError):\l                print('Warning: Could not parse score from critic feedback')\l                score = 7.0\l        else:\l            print('Warning: No score found in critic feedback')\l            score = 7.0\l            feedback += '\nScore: 7.0/10'\l        if score is not None:\l            if score < 0 or score > 10:\l                print(f'Warning: Score {score} out of range 0-10, clamping')\l                score = max(0, min(10, score))\l        return RoleOutput({'critic_feedback': feedback, 'critic_score':\l            score, 'log': f'Critic evaluated prompt. Score: {score}/10'})" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	subgraph cluster0Critic {
		graph [compound=True fontname="DejaVu Sans Mono" label=Critic pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		3 [label="system_prompt = \"\"\"You are a Prompt Engineering Critic with expertise in creating effective prompts for LLMs.\l    Your task is to evaluate the quality of a prompt based on the following criteria:\l    \l    1. Clarity: Is the prompt clear about what is being asked?\l    2. Specificity: Does it include enough details to guide the response?\l    3. Context: Does it provide necessary background information?\l    4. Format guidance: Does it specify how the answer should be structured?\l    5. Constraints: Does it include relevant constraints or guardrails?\l    \l    You MUST assign a score from 0-10 with one decimal place (e.g., 7.5).\l    You MUST include \"Score: X.Y/10\" at the end of your feedback.\l    \l    Provide specific, actionable feedback that would help improve the prompt.\l    \"\"\"\ldef eval(self, state: Dict[str, Any]) ->RoleOutput:..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0eval {
			graph [compound=True fontname="DejaVu Sans Mono" label=eval pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			5 [label="draft = state.get('draft', '')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			5 -> 6 [label=calls style=dashed]
			subgraph cluster_5 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				6 [label="state.get" color="#E552FF" shape=tab style=filled]
			}
			7 [label="if not draft:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			8 [label="return RoleOutput({'log': 'Critic skipped – no draft to evaluate.'})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			7 -> 8 [label="not draft" color=green]
			9 [label="user_prompt = f\"\"\"Task: {state.get('task', 'No task provided')}\l\lDraft prompt to evaluate:\l\l{draft}\l\lPlease evaluate this prompt on a scale of 0-10 (with one decimal place), and provide specific feedback for improvement. End with 'Score: X.Y/10'.\"\"\"\lmessages = [{'role': 'system', 'content': self.system_prompt}, {'role':\l    'user', 'content': user_prompt}]\lfeedback = self._call_llm(messages)\lscore_match = re.search('Score:\\s*(\\d+\\.\\d+)\\/10', feedback)\lscore: Optional[float] = None" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			9 -> 11 [label=calls style=dashed]
			9 -> 12 [label=calls style=dashed]
			9 -> 13 [label=calls style=dashed]
			subgraph cluster_9 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				11 [label="state.get" color="#E552FF" shape=tab style=filled]
				12 [label="self._call_llm" color="#E552FF" shape=tab style=filled]
				13 [label="re.search" color="#E552FF" shape=tab style=filled]
			}
			14 [label="if score_match:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			17 [label="print('Warning: No score found in critic feedback')\lscore = 7.0\lfeedback += '\nScore: 7.0/10'" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			17 -> 18 [label=calls style=dashed]
			subgraph cluster_17 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				18 [label=print color="#E552FF" shape=tab style=filled]
			}
			16 [label="if score is not None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			25 [label="if score < 0 or score > 10:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			27 [label="print(f'Warning: Score {score} out of range 0-10, clamping')\lscore = max(0, min(10, score))" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			27 -> 29 [label=calls style=dashed]
			27 -> 30 [label=calls style=dashed]
			subgraph cluster_27 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				29 [label=print color="#E552FF" shape=tab style=filled]
				30 [label=max color="#E552FF" shape=tab style=filled]
				31 [label=min color="#E552FF" shape=tab style=filled]
				30 -> 31 [color=black]
			}
			26 [label="return RoleOutput({'critic_feedback': feedback, 'critic_score': score,\l    'log': f'Critic evaluated prompt. Score: {score}/10'})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			27 -> 26 [label="" color=black]
			25 -> 27 [label="score < 0 or score > 10" color=green]
			25 -> 26 [label="(not (score < 0 or score > 10))" color=red]
			16 -> 25 [label="score is not None" color=green]
			16 -> 26 [label="(score is None)" color=red]
			17 -> 16 [label="" color=black]
			14 -> 17 [label="(not score_match)" color=red]
			19 [label="score = float(score_match.group(1))" fillcolor=orange shape=Mdiamond style="filled,solid"]
			21 [label="print('Warning: Could not parse score from critic feedback')\lscore = 7.0" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			21 -> 22 [label=calls style=dashed]
			subgraph cluster_21 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				22 [label=print color="#E552FF" shape=tab style=filled]
			}
			21 -> 16 [label="" color=black]
			19 -> 23 [label=calls style=dashed]
			subgraph cluster_19 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				23 [label=float color="#E552FF" shape=tab style=filled]
				24 [label="score_match.group" color="#E552FF" shape=tab style=filled]
				23 -> 24 [color=black]
			}
			19 -> 16 [label="" color=black]
			14 -> 19 [label=score_match color=green]
			9 -> 14 [label="" color=black]
			7 -> 9 [label="(not not draft)" color=red]
			5 -> 7 [label="" color=black]
		}
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
