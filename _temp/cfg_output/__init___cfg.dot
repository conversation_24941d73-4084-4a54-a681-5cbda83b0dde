digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/__init___cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/__init___cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"Virtual teammate role implementations used by the orchestrator.\"\"\"\lfrom importlib import import_module\lfrom pathlib import Path\lfrom typing import Dict, Type\lfrom .base import BaseRole\l__all__ = ['discover_roles']\ldef discover_roles() ->Dict[str, BaseRole]:..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	subgraph cluster0discover_roles {
		graph [compound=True fontname="DejaVu Sans Mono" label=discover_roles pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		3 [label="\"\"\"Dynamically import all python files in this directory (except base) to\l    collect available role classes.\l    Returns a mapping name -> instance ready to use.\l    \"\"\"\lroles: Dict[str, BaseRole] = {}\ldirectory = Path(__file__).resolve().parent" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		3 -> 4 [label=calls style=dashed]
		subgraph cluster_3 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			4 [label="Path.resolve" color="#E552FF" shape=tab style=filled]
		}
		5 [label="for path in directory.glob('*.py'):" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		5 -> 6 [label=calls style=dashed]
		subgraph cluster_5 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			6 [label="directory.glob" color="#E552FF" shape=tab style=filled]
		}
		7 [label="if path.stem in {'__init__', 'base'}:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		9 [label=continue fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		9 -> 5 [label="" color=black]
		7 -> 9 [label="path.stem in {'__init__', 'base'}" color=green]
		10 [label="module = import_module(f'{__name__}.{path.stem}')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		10 -> 12 [label=calls style=dashed]
		subgraph cluster_10 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			12 [label=import_module color="#E552FF" shape=tab style=filled]
		}
		13 [label="for attr in dir(module):" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		13 -> 14 [label=calls style=dashed]
		subgraph cluster_13 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			14 [label=dir color="#E552FF" shape=tab style=filled]
		}
		15 [label="value = getattr(module, attr)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		15 -> 17 [label=calls style=dashed]
		subgraph cluster_15 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			17 [label=getattr color="#E552FF" shape=tab style=filled]
		}
		18 [label="if isinstance(value, type) and issubclass(value, BaseRole" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		19 [label="roles[value.__name__] = value()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		19 -> 21 [label=calls style=dashed]
		subgraph cluster_19 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			21 [label=value color="#E552FF" shape=tab style=filled]
		}
		19 -> 13 [label="" color=black]
		18 -> 19 [label="isinstance(value, type) and issubclass(value, BaseRole
    ) and value is not BaseRole" color=red]
		18 -> 13 [label="(not (isinstance(value, type) and issubclass(value, BaseRole) and value is not
    BaseRole))" color=red]
		15 -> 18 [label="" color=black]
		13 -> 15 [label="dir(module)" color=green]
		13 -> 5 [label="" color=green]
		10 -> 13 [label="" color=black]
		7 -> 10 [label="(path.stem not in {'__init__', 'base'})" color=red]
		5 -> 7 [label="directory.glob('*.py')" color=green]
		8 [label="return roles" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		5 -> 8 [label="" color=green]
		3 -> 5 [label="" color=black]
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
