digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/config_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/config_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"Configuration utilities.\"\"\"\lfrom __future__ import annotations\limport os\lfrom pathlib import Path\lfrom typing import Any, Dict, Optional\limport yaml\lfrom pydantic_settings import BaseSettings\lDEFAULT_CONFIG_PATH = Path(os.getenv('PROMPTGEN_CONFIG', 'config.yaml'))\ldef load_config(path: Optional[os.PathLike | str]=None) ->Dict[str, Any]:...\lclass Settings(BaseSettings):\l    openai_api_key: str = ''\l    default_model: str = 'o4-mini-2025-04-16'\l    reasoning_effort: str = 'medium'\l    max_completion_tokens: int = 4000\l    demo_mode: bool = False\l\l\l    class Config:\l        env_prefix = 'OPENAI_'\l        env_file = '.env'\l\l    def load_from_yaml(self, path: Optional[os.PathLike | str]=None):\l        cfg = load_config(path)\l        if cfg.get('openai_api_key'):\l            self.openai_api_key = cfg['openai_api_key']\l        else:\l            env_api_key = os.environ.get('OPENAI_API_KEY')\l            if env_api_key:\l                self.openai_api_key = env_api_key\l            else:\l                print(\l                    'Warning: No OpenAI API key found in config or environment. Using demo mode.'\l                    )\l                self.demo_mode = True\l        if cfg.get('model_settings'):\l            model_settings = cfg['model_settings']\l            if 'reasoning_effort' in model_settings:\l                self.reasoning_effort = model_settings['reasoning_effort']\l            if 'max_completion_tokens' in model_settings:\l                tokens = model_settings['max_completion_tokens']\l                self.max_completion_tokens = tokens\lsettings = Settings()\lsettings.load_from_yaml()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	1 -> 2 [label=calls style=dashed]
	1 -> 53 [label=calls style=dashed]
	1 -> 54 [label=calls style=dashed]
	subgraph cluster_1 {
		graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		2 [label=Path color="#E552FF" shape=tab style=filled]
		3 [label="os.getenv" color="#E552FF" shape=tab style=filled]
		2 -> 3 [color=black]
		53 [label=Settings color="#E552FF" shape=tab style=filled]
		54 [label="settings.load_from_yaml" color="#E552FF" shape=tab style=filled]
	}
	subgraph cluster0Settings {
		graph [compound=True fontname="DejaVu Sans Mono" label=Settings pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		25 [label="openai_api_key: str = ''\ldefault_model: str = 'o4-mini-2025-04-16'\lreasoning_effort: str = 'medium'\lmax_completion_tokens: int = 4000\ldemo_mode: bool = False\lclass Config:\l    env_prefix = 'OPENAI_'\l    env_file = '.env'\ldef load_from_yaml(self, path: Optional[os.PathLike | str]=None):..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0Config {
			graph [compound=True fontname="DejaVu Sans Mono" label=Config pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			27 [label="env_prefix = 'OPENAI_'\lenv_file = '.env'" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		}
		subgraph cluster0load_from_yaml {
			graph [compound=True fontname="DejaVu Sans Mono" label=load_from_yaml pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			30 [label="cfg = load_config(path)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			30 -> 31 [label=calls style=dashed]
			subgraph cluster_30 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				31 [label=load_config color="#E552FF" shape=tab style=filled]
			}
			32 [label="if cfg.get('openai_api_key'):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			32 -> 33 [label=calls style=dashed]
			subgraph cluster_32 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				33 [label="cfg.get" color="#E552FF" shape=tab style=filled]
			}
			34 [label="self.openai_api_key = cfg['openai_api_key']" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			35 [label="if cfg.get('model_settings'):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			35 -> 43 [label=calls style=dashed]
			subgraph cluster_35 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				43 [label="cfg.get" color="#E552FF" shape=tab style=filled]
			}
			44 [label="model_settings = cfg['model_settings']" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			46 [label="if 'reasoning_effort' in model_settings:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			47 [label="self.reasoning_effort = model_settings['reasoning_effort']" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			48 [label="if 'max_completion_tokens' in model_settings:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			49 [label="tokens = model_settings['max_completion_tokens']\lself.max_completion_tokens = tokens" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			48 -> 49 [label="'max_completion_tokens' in model_settings" color=green]
			47 -> 48 [label="" color=black]
			46 -> 47 [label="'reasoning_effort' in model_settings" color=green]
			46 -> 48 [label="('reasoning_effort' not in model_settings)" color=red]
			44 -> 46 [label="" color=black]
			35 -> 44 [label="cfg.get('model_settings')" color=green]
			34 -> 35 [label="" color=black]
			32 -> 34 [label="cfg.get('openai_api_key')" color=green]
			36 [label="env_api_key = os.environ.get('OPENAI_API_KEY')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			36 -> 37 [label=calls style=dashed]
			subgraph cluster_36 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				37 [label="os.environ.get" color="#E552FF" shape=tab style=filled]
			}
			38 [label="if env_api_key:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			39 [label="self.openai_api_key = env_api_key" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			39 -> 35 [label="" color=black]
			38 -> 39 [label=env_api_key color=green]
			41 [label="print(\l    'Warning: No OpenAI API key found in config or environment. Using demo mode.'\l    )\lself.demo_mode = True" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			41 -> 42 [label=calls style=dashed]
			subgraph cluster_41 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				42 [label=print color="#E552FF" shape=tab style=filled]
			}
			41 -> 35 [label="" color=black]
			38 -> 41 [label="(not env_api_key)" color=red]
			36 -> 38 [label="" color=black]
			32 -> 36 [label="(not cfg.get('openai_api_key'))" color=red]
			30 -> 32 [label="" color=black]
		}
	}
	subgraph cluster0load_config {
		graph [compound=True fontname="DejaVu Sans Mono" label=load_config pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		5 [label="\"\"\"Load YAML configuration file if it exists.\l\l    Returns an empty dict if no config file present, which allows library to\l    fall back on defaults.\l    \"\"\"\lpath = Path(path or DEFAULT_CONFIG_PATH)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		5 -> 6 [label=calls style=dashed]
		subgraph cluster_5 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			6 [label=Path color="#E552FF" shape=tab style=filled]
		}
		7 [label="if not path.exists():" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		8 [label="print(f'Warning: Config file {path} not found. Using default values.')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		8 -> 10 [label=calls style=dashed]
		subgraph cluster_8 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			10 [label=print color="#E552FF" shape=tab style=filled]
		}
		11 [label="return {}" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		8 -> 11 [label="" color=black]
		7 -> 8 [label="not path.exists()" color=green]
		13 [label="config = yaml.safe_load(fh) or {}" fillcolor=orange shape=Mdiamond style="filled,solid"]
		15 [label="print(f'Error loading config file: {e}')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		15 -> 16 [label=calls style=dashed]
		subgraph cluster_15 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			16 [label=print color="#E552FF" shape=tab style=filled]
		}
		17 [label="return {}" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		15 -> 17 [label="" color=black]
		13 -> 19 [label=calls style=dashed]
		13 -> 20 [label=calls style=dashed]
		subgraph cluster_13 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			19 [label="path.open" color="#E552FF" shape=tab style=filled]
			20 [label="yaml.safe_load" color="#E552FF" shape=tab style=filled]
		}
		21 [label="return config" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		13 -> 21 [label="" color=black]
		7 -> 13 [label="(not not path.exists())" color=red]
		5 -> 7 [label="" color=black]
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
