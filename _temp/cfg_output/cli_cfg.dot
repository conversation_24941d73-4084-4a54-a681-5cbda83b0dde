digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/cli_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/cli_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"Command-line interface for prompt generator.\"\"\"\lfrom __future__ import annotations\limport json\limport sys\limport datetime\limport difflib\lfrom pathlib import Path\lfrom typing import Optional, Dict, Any, List, TextIO\limport typer\lfrom rich.console import Console\lfrom rich.syntax import Syntax\lfrom rich.panel import Panel\lfrom rich.columns import Columns\lfrom rich.text import Text\lfrom rich.markdown import Markdown\lfrom .core import run_orchestrator\lfrom .config import load_config\lapp = typer.Typer(add_help_option=True, pretty_exceptions_enable=False)\lconsole = Console()\lconfig = load_config()\lDEFAULT_TARGET_SCORE = config.get('target_score', 8.0)\lDEFAULT_MAX_TURNS = config.get('max_turns', 10)\ldef format_diff(old_text: str, new_text: str) ->Text:...\ldef display_side_by_side(version1: str, version2: str, title1: str='Before',...\<EMAIL>()...\ldef run_orchestrator_verbose(task_description: str, *, target_score: float=...\ldef _write_log(file_handle: TextIO, text: str) ->None:...\ldef generate_html_report(state: Dict[str, Any], history: List[Dict[str, Any...\ldef main():..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	1 -> 2 [label=calls style=dashed]
	1 -> 3 [label=calls style=dashed]
	1 -> 4 [label=calls style=dashed]
	1 -> 5 [label=calls style=dashed]
	1 -> 6 [label=calls style=dashed]
	subgraph cluster_1 {
		graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		2 [label="typer.Typer" color="#E552FF" shape=tab style=filled]
		3 [label=Console color="#E552FF" shape=tab style=filled]
		4 [label=load_config color="#E552FF" shape=tab style=filled]
		5 [label="config.get" color="#E552FF" shape=tab style=filled]
		6 [label="config.get" color="#E552FF" shape=tab style=filled]
	}
	293 [label="if __name__ == '__main__':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
	294 [label="main()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	294 -> 296 [label=calls style=dashed]
	subgraph cluster_294 {
		graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		296 [label=main color="#E552FF" shape=tab style=filled]
	}
	293 -> 294 [label="__name__ == '__main__'" color=green]
	1 -> 293 [label="" color=black]
	subgraph cluster0format_diff {
		graph [compound=True fontname="DejaVu Sans Mono" label=format_diff pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		8 [label="\"\"\"Generate rich-formatted text showing differences between old and new text.\"\"\"\ldiff_lines = []" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		9 [label="for line in difflib.unified_diff(old_text.splitlines(), new_text.splitlines" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		9 -> 10 [label=calls style=dashed]
		subgraph cluster_9 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			10 [label="difflib.unified_diff" color="#E552FF" shape=tab style=filled]
			11 [label="old_text.splitlines" color="#E552FF" shape=tab style=filled]
			10 -> 11 [color=black]
			12 [label="new_text.splitlines" color="#E552FF" shape=tab style=filled]
			10 -> 12 [color=black]
		}
		13 [label="if line.startswith('+'):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		13 -> 15 [label=calls style=dashed]
		subgraph cluster_13 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			15 [label="line.startswith" color="#E552FF" shape=tab style=filled]
		}
		16 [label="diff_lines.append(f'[green]{line}[/green]')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		16 -> 30 [label=calls style=dashed]
		subgraph cluster_16 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			30 [label="diff_lines.append" color="#E552FF" shape=tab style=filled]
		}
		16 -> 9 [label="" color=black]
		13 -> 16 [label="line.startswith('+')" color=green]
		18 [label="if line.startswith('-'):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		18 -> 19 [label=calls style=dashed]
		subgraph cluster_18 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			19 [label="line.startswith" color="#E552FF" shape=tab style=filled]
		}
		20 [label="diff_lines.append(f'[red]{line}[/red]')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		20 -> 29 [label=calls style=dashed]
		subgraph cluster_20 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			29 [label="diff_lines.append" color="#E552FF" shape=tab style=filled]
		}
		20 -> 9 [label="" color=black]
		18 -> 20 [label="line.startswith('-')" color=green]
		22 [label="if line.startswith('@@'):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		22 -> 23 [label=calls style=dashed]
		subgraph cluster_22 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			23 [label="line.startswith" color="#E552FF" shape=tab style=filled]
		}
		24 [label="diff_lines.append(f'[cyan]{line}[/cyan]')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		24 -> 28 [label=calls style=dashed]
		subgraph cluster_24 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			28 [label="diff_lines.append" color="#E552FF" shape=tab style=filled]
		}
		24 -> 9 [label="" color=black]
		22 -> 24 [label="line.startswith('@@')" color=green]
		26 [label="diff_lines.append(line)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		26 -> 27 [label=calls style=dashed]
		subgraph cluster_26 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			27 [label="diff_lines.append" color="#E552FF" shape=tab style=filled]
		}
		26 -> 9 [label="" color=black]
		22 -> 26 [label="(not line.startswith('@@'))" color=red]
		18 -> 22 [label="(not line.startswith('-'))" color=red]
		13 -> 18 [label="(not line.startswith('+'))" color=red]
		9 -> 13 [label="difflib.unified_diff(old_text.splitlines(), new_text.splitlines(), lineterm
    ='', n=2)" color=red]
		14 [label="return Text.from_markup('\n'.join(diff_lines))" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		9 -> 14 [label="" color=green]
		8 -> 9 [label="" color=black]
	}
	subgraph cluster0display_side_by_side {
		graph [compound=True fontname="DejaVu Sans Mono" label=display_side_by_side pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		34 [label="\"\"\"Display two versions of text side by side in the terminal.\"\"\"\lpanel1 = Panel(Syntax(version1, 'markdown', theme='monokai', line_numbers=\l    True), title=title1)\lpanel2 = Panel(Syntax(version2, 'markdown', theme='monokai', line_numbers=\l    True), title=title2)\lconsole.print(Columns([panel1, panel2]))" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		34 -> 35 [label=calls style=dashed]
		34 -> 37 [label=calls style=dashed]
		34 -> 39 [label=calls style=dashed]
		subgraph cluster_34 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			35 [label=Panel color="#E552FF" shape=tab style=filled]
			36 [label=Syntax color="#E552FF" shape=tab style=filled]
			35 -> 36 [color=black]
			37 [label=Panel color="#E552FF" shape=tab style=filled]
			38 [label=Syntax color="#E552FF" shape=tab style=filled]
			37 -> 38 [color=black]
			39 [label="console.print" color="#E552FF" shape=tab style=filled]
			40 [label=Columns color="#E552FF" shape=tab style=filled]
			39 -> 40 [color=black]
		}
	}
	subgraph cluster0orchestrate {
		graph [compound=True fontname="DejaVu Sans Mono" label=orchestrate pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		43 [label="\"\"\"Run the orchestrator interactively and display the evolution of the prompt.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		44 [label="if log_file is None and verbose:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		45 [label="timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')\llog_file = Path(f'prompt_gen_{timestamp}.log')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		45 -> 47 [label=calls style=dashed]
		45 -> 48 [label=calls style=dashed]
		subgraph cluster_45 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			47 [label="datetime.datetime.now.strftime" color="#E552FF" shape=tab style=filled]
			48 [label=Path color="#E552FF" shape=tab style=filled]
		}
		46 [label="state, history = run_orchestrator_verbose(task, target_score=target_score,\l    max_turns=max_turns, verbose=verbose, log_file=log_file, side_by_side=\l    side_by_side)\lconsole.rule('[bold green]Orchestration complete')\lfinal_prompt = state.get('draft', '<no prompt generated>')\lsyntax = Syntax(final_prompt, 'markdown', theme='monokai', line_numbers=False)\lconsole.print(syntax)\lconsole.print(\"\"\"\l[bold]Termination reason:[/bold]\"\"\", state.get(\l    'termination_reason'))" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		46 -> 49 [label=calls style=dashed]
		46 -> 50 [label=calls style=dashed]
		46 -> 51 [label=calls style=dashed]
		46 -> 52 [label=calls style=dashed]
		46 -> 53 [label=calls style=dashed]
		46 -> 54 [label=calls style=dashed]
		subgraph cluster_46 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			49 [label=run_orchestrator_verbose color="#E552FF" shape=tab style=filled]
			50 [label="console.rule" color="#E552FF" shape=tab style=filled]
			51 [label="state.get" color="#E552FF" shape=tab style=filled]
			52 [label=Syntax color="#E552FF" shape=tab style=filled]
			53 [label="console.print" color="#E552FF" shape=tab style=filled]
			54 [label="console.print" color="#E552FF" shape=tab style=filled]
			55 [label="state.get" color="#E552FF" shape=tab style=filled]
			54 -> 55 [color=black]
		}
		56 [label="if save:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		57 [label="save.write_text(final_prompt)\lconsole.print(f\"\"\"\l[green]Saved final prompt to {save.resolve()}[/green]\"\"\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		57 -> 59 [label=calls style=dashed]
		57 -> 60 [label=calls style=dashed]
		subgraph cluster_57 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			59 [label="save.write_text" color="#E552FF" shape=tab style=filled]
			60 [label="console.print" color="#E552FF" shape=tab style=filled]
			61 [label="save.resolve" color="#E552FF" shape=tab style=filled]
			60 -> 61 [color=black]
		}
		58 [label="if log_file:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		62 [label="console.print(\l    f\"\"\"\l[green]Detailed interaction log saved to {log_file.resolve()}[/green]\"\"\"\l    )" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		62 -> 64 [label=calls style=dashed]
		subgraph cluster_62 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			64 [label="console.print" color="#E552FF" shape=tab style=filled]
			65 [label="log_file.resolve" color="#E552FF" shape=tab style=filled]
			64 -> 65 [color=black]
		}
		63 [label="if html_report:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		66 [label="html_path = generate_html_report(state, history, html_report)\lconsole.print(\l    f\"\"\"\l[green]HTML evolution report saved to {html_path.resolve()}[/green]\"\"\"\l    )" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		66 -> 68 [label=calls style=dashed]
		66 -> 69 [label=calls style=dashed]
		subgraph cluster_66 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			68 [label=generate_html_report color="#E552FF" shape=tab style=filled]
			69 [label="console.print" color="#E552FF" shape=tab style=filled]
			70 [label="html_path.resolve" color="#E552FF" shape=tab style=filled]
			69 -> 70 [color=black]
		}
		67 [label="if console.is_terminal:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		71 [label="if typer.confirm('Show orchestration history?', default=False):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		71 -> 73 [label=calls style=dashed]
		subgraph cluster_71 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			73 [label="typer.confirm" color="#E552FF" shape=tab style=filled]
		}
		74 [label="console.print_json(json.dumps(history, indent=2))" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		74 -> 76 [label=calls style=dashed]
		subgraph cluster_74 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			76 [label="console.print_json" color="#E552FF" shape=tab style=filled]
			77 [label="json.dumps" color="#E552FF" shape=tab style=filled]
			76 -> 77 [color=black]
		}
		71 -> 74 [label="typer.confirm('Show orchestration history?', default=False)" color=green]
		67 -> 71 [label="console.is_terminal" color=green]
		66 -> 67 [label="" color=black]
		63 -> 66 [label=html_report color=green]
		63 -> 67 [label="(not html_report)" color=red]
		62 -> 63 [label="" color=black]
		58 -> 62 [label=log_file color=green]
		58 -> 63 [label="(not log_file)" color=red]
		57 -> 58 [label="" color=black]
		56 -> 57 [label=save color=green]
		56 -> 58 [label="(not save)" color=red]
		46 -> 56 [label="" color=black]
		45 -> 46 [label="" color=black]
		44 -> 45 [label="log_file is None and verbose" color=green]
		44 -> 46 [label="(not (log_file is None and verbose))" color=red]
		43 -> 44 [label="" color=black]
	}
	subgraph cluster0run_orchestrator_verbose {
		graph [compound=True fontname="DejaVu Sans Mono" label=run_orchestrator_verbose pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		80 [label="\"\"\"Run the orchestrator with optional verbose output and detailed logging.\"\"\"\lfrom .orchestrator import Orchestrator\lorchestrator = Orchestrator(target_score=target_score, max_turns=max_turns)\lstate = {'task': task_description, 'target_score': target_score}\lhistory = []" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		80 -> 81 [label=calls style=dashed]
		subgraph cluster_80 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			81 [label=Orchestrator color="#E552FF" shape=tab style=filled]
		}
		82 [label="if verbose:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		83 [label="console.print('[bold blue]Starting orchestration process...[/bold blue]')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		83 -> 85 [label=calls style=dashed]
		subgraph cluster_83 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			85 [label="console.print" color="#E552FF" shape=tab style=filled]
		}
		84 [label="log_handle = None" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		86 [label="if log_file:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		88 [label="previous_draft = ''" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		101 [label="for turn in range(1, max_turns + 1):" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		101 -> 102 [label=calls style=dashed]
		subgraph cluster_101 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			102 [label=range color="#E552FF" shape=tab style=filled]
		}
		103 [label="role_name, reason = orchestrator._policy(state)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		103 -> 105 [label=calls style=dashed]
		subgraph cluster_103 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			105 [label="orchestrator._policy" color="#E552FF" shape=tab style=filled]
		}
		106 [label="if role_name is None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		107 [label="state['termination_reason'] = reason" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		109 [label="if verbose:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		110 [label="console.print(f'[bold red]Terminating: {reason}[/bold red]')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		110 -> 112 [label=calls style=dashed]
		subgraph cluster_110 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			112 [label="console.print" color="#E552FF" shape=tab style=filled]
		}
		111 [label="if log_handle:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		113 [label="_write_log(log_handle, f\"\"\"\l--- TERMINATION: {reason} ---\l\"\"\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		113 -> 115 [label=calls style=dashed]
		subgraph cluster_113 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			115 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		114 [label=break fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		104 [label="if log_handle:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		197 [label="_write_log(log_handle, f\"\n{'=' * 50}\n\")\l_write_log(log_handle,\l    f\"\"\"FINAL GENERATED PROMPT:\l\l{state.get('draft', '<no prompt generated>')}\l\"\"\"\l    )\l_write_log(log_handle, f\"\n{'=' * 50}\n\")\llog_handle.close()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		197 -> 199 [label=calls style=dashed]
		197 -> 200 [label=calls style=dashed]
		197 -> 202 [label=calls style=dashed]
		197 -> 203 [label=calls style=dashed]
		subgraph cluster_197 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			199 [label=_write_log color="#E552FF" shape=tab style=filled]
			200 [label=_write_log color="#E552FF" shape=tab style=filled]
			201 [label="state.get" color="#E552FF" shape=tab style=filled]
			200 -> 201 [color=black]
			202 [label=_write_log color="#E552FF" shape=tab style=filled]
			203 [label="log_handle.close" color="#E552FF" shape=tab style=filled]
		}
		198 [label="return state, history" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		197 -> 198 [label="" color=black]
		104 -> 197 [label=log_handle color=green]
		104 -> 198 [label="(not log_handle)" color=red]
		114 -> 104 [label="" color=black]
		113 -> 114 [label="" color=black]
		111 -> 113 [label=log_handle color=green]
		111 -> 114 [label="(not log_handle)" color=red]
		110 -> 111 [label="" color=black]
		109 -> 110 [label=verbose color=green]
		109 -> 111 [label="(not verbose)" color=red]
		107 -> 109 [label="" color=black]
		106 -> 107 [label="role_name is None" color=green]
		108 [label="role = orchestrator.roles.get(role_name)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		108 -> 117 [label=calls style=dashed]
		subgraph cluster_108 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			117 [label="orchestrator.roles.get" color="#E552FF" shape=tab style=filled]
		}
		118 [label="if role is None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		119 [label="raise KeyError(\l    f\"Role '{role_name}' not found. Ensure it exists in roles package.\")" fillcolor="#98fb98" shape=house style="filled,solid"]
		118 -> 119 [label="role is None" color=green]
		120 [label="if verbose:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		122 [label="console.print(\l    f'[bold cyan]Turn {turn}:[/bold cyan] Running role [bold yellow]{role_name}[/bold yellow] - {reason}'\l    )" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		122 -> 124 [label=calls style=dashed]
		subgraph cluster_122 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			124 [label="console.print" color="#E552FF" shape=tab style=filled]
		}
		123 [label="if log_handle:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		125 [label="_write_log(log_handle, f\"\n{'=' * 80}\n\")\l_write_log(log_handle, f'TURN {turn}: Role {role_name} - {reason}\n')\l_write_log(log_handle, f\"{'=' * 80}\n\n\")\l_write_log(log_handle, f'--- INPUT STATE ---\n')\l_write_log(log_handle, f\"Task: {state.get('task', '')}\n\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		125 -> 127 [label=calls style=dashed]
		125 -> 128 [label=calls style=dashed]
		125 -> 129 [label=calls style=dashed]
		125 -> 130 [label=calls style=dashed]
		125 -> 131 [label=calls style=dashed]
		subgraph cluster_125 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			127 [label=_write_log color="#E552FF" shape=tab style=filled]
			128 [label=_write_log color="#E552FF" shape=tab style=filled]
			129 [label=_write_log color="#E552FF" shape=tab style=filled]
			130 [label=_write_log color="#E552FF" shape=tab style=filled]
			131 [label=_write_log color="#E552FF" shape=tab style=filled]
			132 [label="state.get" color="#E552FF" shape=tab style=filled]
			131 -> 132 [color=black]
		}
		133 [label="if 'draft' in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		134 [label="_write_log(log_handle, f\"\"\"Current Draft:\l{state['draft']}\l\l\"\"\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		134 -> 136 [label=calls style=dashed]
		subgraph cluster_134 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			136 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		135 [label="if 'critic_score' in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		137 [label="_write_log(log_handle, f\"Critic Score: {state['critic_score']}\n\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		137 -> 139 [label=calls style=dashed]
		subgraph cluster_137 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			139 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		140 [label="if 'critic_feedback' in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		141 [label="_write_log(log_handle, f\"Critic Feedback: {state['critic_feedback']}\n\n\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		141 -> 143 [label=calls style=dashed]
		subgraph cluster_141 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			143 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		138 [label="_write_log(log_handle, f'--- END INPUT STATE ---\n\n')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		138 -> 144 [label=calls style=dashed]
		subgraph cluster_138 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			144 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		126 [label="previous_draft = state.get('draft', '')\lstate_before = state.copy()\loutput = role.eval(state)\lstate.update(output.to_state_update())\lstate['_last_role'] = role_name\lcurrent_draft = state.get('draft', '')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		126 -> 145 [label=calls style=dashed]
		126 -> 146 [label=calls style=dashed]
		126 -> 147 [label=calls style=dashed]
		126 -> 148 [label=calls style=dashed]
		126 -> 150 [label=calls style=dashed]
		subgraph cluster_126 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			145 [label="state.get" color="#E552FF" shape=tab style=filled]
			146 [label="state.copy" color="#E552FF" shape=tab style=filled]
			147 [label="role.eval" color="#E552FF" shape=tab style=filled]
			148 [label="state.update" color="#E552FF" shape=tab style=filled]
			149 [label="output.to_state_update" color="#E552FF" shape=tab style=filled]
			148 -> 149 [color=black]
			150 [label="state.get" color="#E552FF" shape=tab style=filled]
		}
		151 [label="if side_by_side and 'draft' in state and previous_draft and current_draft != previous_draft:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		152 [label="console.rule(f'[bold green]Changes after {role_name}[/bold green]')\ldisplay_side_by_side(previous_draft, current_draft, 'Before', 'After')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		152 -> 154 [label=calls style=dashed]
		152 -> 155 [label=calls style=dashed]
		subgraph cluster_152 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			154 [label="console.rule" color="#E552FF" shape=tab style=filled]
			155 [label=display_side_by_side color="#E552FF" shape=tab style=filled]
		}
		156 [label="if 'critic_feedback' in state and role_name == 'Critic':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		157 [label="feedback_panel = Panel(Markdown(state['critic_feedback']), title=\l    f\"Critic Feedback (Score: {state.get('critic_score', 'N/A')})\",\l    border_style='yellow')\lconsole.print(feedback_panel)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		157 -> 165 [label=calls style=dashed]
		157 -> 167 [label=calls style=dashed]
		subgraph cluster_157 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			165 [label=Panel color="#E552FF" shape=tab style=filled]
			166 [label=Markdown color="#E552FF" shape=tab style=filled]
			165 -> 166 [color=black]
			167 [label="console.print" color="#E552FF" shape=tab style=filled]
		}
		153 [label="if log_handle:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		168 [label="_write_log(log_handle, f'--- OUTPUT FROM {role_name} ---\n')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		168 -> 170 [label=calls style=dashed]
		subgraph cluster_168 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			170 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		171 [label="for key, value in state.items():" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		171 -> 172 [label=calls style=dashed]
		subgraph cluster_171 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			172 [label="state.items" color="#E552FF" shape=tab style=filled]
		}
		173 [label="if key not in state_before or state_before[key] != value:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		175 [label="if key == 'draft':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		177 [label="_write_log(log_handle, f\"\"\"Updated Draft:\l{value}\l\l\"\"\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		177 -> 183 [label=calls style=dashed]
		subgraph cluster_177 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			183 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		177 -> 171 [label="" color=black]
		175 -> 177 [label="key == 'draft'" color=green]
		179 [label="if key != '_last_role' and key != 'log':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		180 [label="_write_log(log_handle, f'Updated {key}: {value}\n')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		180 -> 182 [label=calls style=dashed]
		subgraph cluster_180 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			182 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		180 -> 171 [label="" color=black]
		179 -> 180 [label="key != '_last_role' and key != 'log'" color=green]
		179 -> 171 [label="(not (key != '_last_role' and key != 'log'))" color=red]
		175 -> 179 [label="(key != 'draft')" color=red]
		173 -> 175 [label="key not in state_before or state_before[key] != value" color=green]
		173 -> 171 [label="(not (key not in state_before or state_before[key] != value))" color=red]
		171 -> 173 [label="state.items()" color=green]
		174 [label="if 'log' in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		184 [label="_write_log(log_handle, f\"Log: {state['log']}\n\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		184 -> 186 [label=calls style=dashed]
		subgraph cluster_184 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			186 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		185 [label="_write_log(log_handle, f'--- END OUTPUT FROM {role_name} ---\n\n')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		185 -> 187 [label=calls style=dashed]
		subgraph cluster_185 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			187 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		169 [label="if verbose and 'log' in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		188 [label="console.print(f\"  -> {state['log']}\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		188 -> 190 [label=calls style=dashed]
		subgraph cluster_188 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			190 [label="console.print" color="#E552FF" shape=tab style=filled]
		}
		189 [label="history.append({'turn': turn, 'role': role_name, 'reason': reason,\l    'output_log': state.get('log', ''), 'draft': state.get('draft', ''),\l    'critic_score': state.get('critic_score') if role_name == 'Critic' else\l    None, 'critic_feedback': state.get('critic_feedback') if role_name ==\l    'Critic' else None})\lstate.pop('log', None)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		189 -> 191 [label=calls style=dashed]
		189 -> 196 [label=calls style=dashed]
		subgraph cluster_189 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			191 [label="history.append" color="#E552FF" shape=tab style=filled]
			192 [label="state.get" color="#E552FF" shape=tab style=filled]
			191 -> 192 [color=black]
			193 [label="state.get" color="#E552FF" shape=tab style=filled]
			191 -> 193 [color=black]
			194 [label="state.get" color="#E552FF" shape=tab style=filled]
			191 -> 194 [color=black]
			195 [label="state.get" color="#E552FF" shape=tab style=filled]
			191 -> 195 [color=black]
			196 [label="state.pop" color="#E552FF" shape=tab style=filled]
		}
		189 -> 101 [label="" color=black]
		188 -> 189 [label="" color=black]
		169 -> 188 [label="verbose and 'log' in state" color=green]
		169 -> 189 [label="(not (verbose and 'log' in state))" color=red]
		185 -> 169 [label="" color=black]
		184 -> 185 [label="" color=black]
		174 -> 184 [label="'log' in state" color=green]
		174 -> 185 [label="('log' not in state)" color=red]
		171 -> 174 [label="" color=green]
		168 -> 171 [label="" color=black]
		153 -> 168 [label=log_handle color=green]
		153 -> 169 [label="(not log_handle)" color=red]
		157 -> 153 [label="" color=black]
		156 -> 157 [label="'critic_feedback' in state and role_name == 'Critic'" color=green]
		159 [label="if 'log' in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		160 [label="log_panel = Panel(Text(state['log']), title=f'{role_name} Output',\l    border_style='blue')\lconsole.print(log_panel)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		160 -> 162 [label=calls style=dashed]
		160 -> 164 [label=calls style=dashed]
		subgraph cluster_160 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			162 [label=Panel color="#E552FF" shape=tab style=filled]
			163 [label=Text color="#E552FF" shape=tab style=filled]
			162 -> 163 [color=black]
			164 [label="console.print" color="#E552FF" shape=tab style=filled]
		}
		160 -> 153 [label="" color=black]
		159 -> 160 [label="'log' in state" color=green]
		159 -> 153 [label="('log' not in state)" color=red]
		156 -> 159 [label="(not ('critic_feedback' in state and role_name == 'Critic'))" color=red]
		152 -> 156 [label="" color=black]
		151 -> 152 [label="side_by_side and 'draft' in state and previous_draft and current_draft != previous_draft" color=green]
		151 -> 153 [label="(not (side_by_side and 'draft' in state and previous_draft and 
    current_draft != previous_draft))" color=red]
		126 -> 151 [label="" color=black]
		138 -> 126 [label="" color=black]
		141 -> 138 [label="" color=black]
		140 -> 141 [label="'critic_feedback' in state" color=green]
		140 -> 138 [label="('critic_feedback' not in state)" color=red]
		137 -> 140 [label="" color=black]
		135 -> 137 [label="'critic_score' in state" color=green]
		135 -> 138 [label="('critic_score' not in state)" color=red]
		134 -> 135 [label="" color=black]
		133 -> 134 [label="'draft' in state" color=green]
		133 -> 135 [label="('draft' not in state)" color=red]
		125 -> 133 [label="" color=black]
		123 -> 125 [label=log_handle color=green]
		123 -> 126 [label="(not log_handle)" color=red]
		122 -> 123 [label="" color=black]
		120 -> 122 [label=verbose color=green]
		120 -> 123 [label="(not verbose)" color=red]
		118 -> 120 [label="(role is not None)" color=red]
		108 -> 118 [label="" color=black]
		106 -> 108 [label="(role_name is not None)" color=red]
		103 -> 106 [label="" color=black]
		101 -> 103 [label="range(1, max_turns + 1)" color=green]
		101 -> 104 [label="" color=green]
		88 -> 101 [label="" color=black]
		86 -> 88 [label="(not log_file)" color=red]
		89 [label="log_handle = open(log_file, 'w', encoding='utf-8')
_write_log(log_handle, f'=== PROMPT GENERATOR LOG ===\n')
_write_log(log_handle,
    f\"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\")
_write_log(log_handle, f'Task: {task_description}\n')
_write_log(log_handle, f'Target Score: {target_score}\n')
_write_log(log_handle, f'Max Turns: {max_turns}\n')
_write_log(log_handle, f\"{'=' * 50}\n\n\")" fillcolor=orange shape=Mdiamond style="filled,solid"]
		91 [label="console.print(f'[bold red]Error opening log file: {e}[/bold red]')\llog_handle = None" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		91 -> 92 [label=calls style=dashed]
		subgraph cluster_91 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			92 [label="console.print" color="#E552FF" shape=tab style=filled]
		}
		91 -> 88 [label="" color=black]
		89 -> 93 [label=calls style=dashed]
		89 -> 94 [label=calls style=dashed]
		89 -> 95 [label=calls style=dashed]
		89 -> 97 [label=calls style=dashed]
		89 -> 98 [label=calls style=dashed]
		89 -> 99 [label=calls style=dashed]
		89 -> 100 [label=calls style=dashed]
		subgraph cluster_89 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			93 [label=open color="#E552FF" shape=tab style=filled]
			94 [label=_write_log color="#E552FF" shape=tab style=filled]
			95 [label=_write_log color="#E552FF" shape=tab style=filled]
			96 [label="datetime.datetime.now.strftime" color="#E552FF" shape=tab style=filled]
			95 -> 96 [color=black]
			97 [label=_write_log color="#E552FF" shape=tab style=filled]
			98 [label=_write_log color="#E552FF" shape=tab style=filled]
			99 [label=_write_log color="#E552FF" shape=tab style=filled]
			100 [label=_write_log color="#E552FF" shape=tab style=filled]
		}
		89 -> 88 [label="" color=black]
		86 -> 89 [label=log_file color=green]
		84 -> 86 [label="" color=black]
		83 -> 84 [label="" color=black]
		82 -> 83 [label=verbose color=green]
		82 -> 84 [label="(not verbose)" color=red]
		80 -> 82 [label="" color=black]
	}
	subgraph cluster0_write_log {
		graph [compound=True fontname="DejaVu Sans Mono" label=_write_log pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		207 [label="\"\"\"Helper function to write to log file with error handling.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		208 [label="file_handle.write(text)
file_handle.flush()" fillcolor=orange shape=Mdiamond style="filled,solid"]
		210 [label="" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		208 -> 211 [label=calls style=dashed]
		208 -> 212 [label=calls style=dashed]
		subgraph cluster_208 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			211 [label="file_handle.write" color="#E552FF" shape=tab style=filled]
			212 [label="file_handle.flush" color="#E552FF" shape=tab style=filled]
		}
		207 -> 208 [label="" color=black]
	}
	subgraph cluster0generate_html_report {
		graph [compound=True fontname="DejaVu Sans Mono" label=generate_html_report pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		215 [label="\"\"\"Generate an HTML report visualizing the evolution of the prompt through turns.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		216 [label="if output_path is None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		217 [label="timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')\loutput_path = Path(f'prompt_evolution_{timestamp}.html')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		217 -> 219 [label=calls style=dashed]
		217 -> 220 [label=calls style=dashed]
		subgraph cluster_217 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			219 [label="datetime.datetime.now.strftime" color="#E552FF" shape=tab style=filled]
			220 [label=Path color="#E552FF" shape=tab style=filled]
		}
		218 [label="html = ['<!DOCTYPE html>', '<html>', '<head>',\l    '    <title>Prompt Evolution Report</title>', '    <style>',\l    '        body { font-family: Arial, sans-serif; margin: 20px; }',\l    '        .turn { margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; border-radius: 5px; }'\l    , '        .role { font-weight: bold; color: #2c3e50; }',\l    '        .feedback { background-color: #f8f9fa; padding: 10px; border-left: 4px solid #ffc107; margin: 10px 0; }'\l    , '        .score { font-weight: bold; color: #e74c3c; }',\l    '        .diff { overflow: auto; max-height: 500px; }',\l    '        table.diff { font-family: monospace; border-collapse: collapse; }'\l    , '        .diff_header { background-color: #e0e0e0; }',\l    '        td.diff_header { text-align: right; }',\l    '        .diff_add { background-color: #aaffaa; }',\l    '        .diff_chg { background-color: #ffff77; }',\l    '        .diff_sub { background-color: #ffaaaa; }',\l    '        .summary { margin-top: 20px; padding: 15px; background-color: #f0f7fb; border-left: 5px solid #3498db; }'\l    , '        .side-by-side { display: flex; }',\l    '        .version { flex: 1; margin: 10px; padding: 10px; border: 1px solid #ddd; }'\l    , '        pre { white-space: pre-wrap; word-wrap: break-word; }',\l    '    </style>', '</head>', '<body>',\l    f'    <h1>Prompt Evolution Report</h1>',\l    f\"    <p>Task: {state.get('task', 'No task specified')}</p>\",\l    f\"    <p>Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>\"\l    , '    <div class=\"summary\">', f'        <h2>Summary</h2>',\l    f'        <p>Total turns: {len(history)}</p>',\l    f\"        <p>Target score: {state.get('target_score', 'Not specified')}</p>\"\l    ,\l    f\"        <p>Final score: {state.get('critic_score', 'Not available')}</p>\"\l    ,\l    f\"        <p>Termination reason: {state.get('termination_reason', 'Not specified')}</p>\"\l    , '    </div>']\lprev_draft = ''" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		218 -> 221 [label=calls style=dashed]
		218 -> 222 [label=calls style=dashed]
		218 -> 223 [label=calls style=dashed]
		218 -> 224 [label=calls style=dashed]
		218 -> 225 [label=calls style=dashed]
		218 -> 226 [label=calls style=dashed]
		subgraph cluster_218 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			221 [label="state.get" color="#E552FF" shape=tab style=filled]
			222 [label="datetime.datetime.now.strftime" color="#E552FF" shape=tab style=filled]
			223 [label=len color="#E552FF" shape=tab style=filled]
			224 [label="state.get" color="#E552FF" shape=tab style=filled]
			225 [label="state.get" color="#E552FF" shape=tab style=filled]
			226 [label="state.get" color="#E552FF" shape=tab style=filled]
		}
		227 [label="for turn_data in history:" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		228 [label="turn_num = turn_data['turn']\lrole = turn_data['role']\lcurrent_draft = turn_data.get('draft', '')\lhtml.append(f'    <div class=\"turn\">')\lhtml.append(f'        <h2>Turn {turn_num}: {role}</h2>')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		228 -> 230 [label=calls style=dashed]
		228 -> 231 [label=calls style=dashed]
		228 -> 232 [label=calls style=dashed]
		subgraph cluster_228 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			230 [label="turn_data.get" color="#E552FF" shape=tab style=filled]
			231 [label="html.append" color="#E552FF" shape=tab style=filled]
			232 [label="html.append" color="#E552FF" shape=tab style=filled]
		}
		233 [label="if turn_data.get('critic_feedback') or turn_data.get('critic_score'" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		234 [label="html.append(f'        <div class=\"feedback\">')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		234 -> 243 [label=calls style=dashed]
		subgraph cluster_234 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			243 [label="html.append" color="#E552FF" shape=tab style=filled]
		}
		244 [label="if turn_data.get('critic_feedback'):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		244 -> 245 [label=calls style=dashed]
		subgraph cluster_244 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			245 [label="turn_data.get" color="#E552FF" shape=tab style=filled]
		}
		246 [label="html.append(f\"            <p>{turn_data['critic_feedback']}</p>\")" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		246 -> 248 [label=calls style=dashed]
		subgraph cluster_246 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			248 [label="html.append" color="#E552FF" shape=tab style=filled]
		}
		247 [label="if turn_data.get('critic_score') is not None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		247 -> 249 [label=calls style=dashed]
		subgraph cluster_247 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			249 [label="turn_data.get" color="#E552FF" shape=tab style=filled]
		}
		250 [label="html.append(\l    f'            <p class=\"score\">Score: {turn_data[\'critic_score\']}</p>')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		250 -> 252 [label=calls style=dashed]
		subgraph cluster_250 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			252 [label="html.append" color="#E552FF" shape=tab style=filled]
		}
		251 [label="html.append(f'        </div>')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		251 -> 253 [label=calls style=dashed]
		subgraph cluster_251 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			253 [label="html.append" color="#E552FF" shape=tab style=filled]
		}
		235 [label="if current_draft and current_draft != prev_draft:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		254 [label="if prev_draft:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		256 [label="html.append(f'        <h3>Prompt Changes</h3>')\lhtml.append(f'        <div class=\"side-by-side\">')\lhtml.append(f'            <div class=\"version\">')\lhtml.append(f'                <h4>Previous Version</h4>')\lhtml.append(f'                <pre>{prev_draft}</pre>')\lhtml.append(f'            </div>')\lhtml.append(f'            <div class=\"version\">')\lhtml.append(f'                <h4>New Version</h4>')\lhtml.append(f'                <pre>{current_draft}</pre>')\lhtml.append(f'            </div>')\lhtml.append(f'        </div>')\ldiffer = difflib.HtmlDiff(wrapcolumn=80)\ldiff_html = differ.make_file(prev_draft.splitlines(), current_draft.\l    splitlines(), 'Previous Version', 'New Version', context=True)\lhtml.append(f'        <h3>Detailed Changes</h3>')\lhtml.append(f'        <div class=\"diff\">{diff_html}</div>')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		256 -> 261 [label=calls style=dashed]
		256 -> 262 [label=calls style=dashed]
		256 -> 263 [label=calls style=dashed]
		256 -> 264 [label=calls style=dashed]
		256 -> 265 [label=calls style=dashed]
		256 -> 266 [label=calls style=dashed]
		256 -> 267 [label=calls style=dashed]
		256 -> 268 [label=calls style=dashed]
		256 -> 269 [label=calls style=dashed]
		256 -> 270 [label=calls style=dashed]
		256 -> 271 [label=calls style=dashed]
		256 -> 272 [label=calls style=dashed]
		256 -> 273 [label=calls style=dashed]
		256 -> 276 [label=calls style=dashed]
		256 -> 277 [label=calls style=dashed]
		subgraph cluster_256 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			261 [label="html.append" color="#E552FF" shape=tab style=filled]
			262 [label="html.append" color="#E552FF" shape=tab style=filled]
			263 [label="html.append" color="#E552FF" shape=tab style=filled]
			264 [label="html.append" color="#E552FF" shape=tab style=filled]
			265 [label="html.append" color="#E552FF" shape=tab style=filled]
			266 [label="html.append" color="#E552FF" shape=tab style=filled]
			267 [label="html.append" color="#E552FF" shape=tab style=filled]
			268 [label="html.append" color="#E552FF" shape=tab style=filled]
			269 [label="html.append" color="#E552FF" shape=tab style=filled]
			270 [label="html.append" color="#E552FF" shape=tab style=filled]
			271 [label="html.append" color="#E552FF" shape=tab style=filled]
			272 [label="difflib.HtmlDiff" color="#E552FF" shape=tab style=filled]
			273 [label="differ.make_file" color="#E552FF" shape=tab style=filled]
			274 [label="prev_draft.splitlines" color="#E552FF" shape=tab style=filled]
			273 -> 274 [color=black]
			275 [label="current_draft.splitlines" color="#E552FF" shape=tab style=filled]
			273 -> 275 [color=black]
			276 [label="html.append" color="#E552FF" shape=tab style=filled]
			277 [label="html.append" color="#E552FF" shape=tab style=filled]
		}
		257 [label="prev_draft = current_draft" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		255 [label="html.append(f'    </div>')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		255 -> 278 [label=calls style=dashed]
		subgraph cluster_255 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			278 [label="html.append" color="#E552FF" shape=tab style=filled]
		}
		255 -> 227 [label="" color=black]
		257 -> 255 [label="" color=black]
		256 -> 257 [label="" color=black]
		254 -> 256 [label=prev_draft color=green]
		258 [label="html.append(f'        <h3>Initial Draft</h3>')\lhtml.append(f'        <pre>{current_draft}</pre>')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		258 -> 259 [label=calls style=dashed]
		258 -> 260 [label=calls style=dashed]
		subgraph cluster_258 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			259 [label="html.append" color="#E552FF" shape=tab style=filled]
			260 [label="html.append" color="#E552FF" shape=tab style=filled]
		}
		258 -> 257 [label="" color=black]
		254 -> 258 [label="(not prev_draft)" color=red]
		235 -> 254 [label="current_draft and current_draft != prev_draft" color=green]
		235 -> 255 [label="(not (current_draft and current_draft != prev_draft))" color=red]
		251 -> 235 [label="" color=black]
		250 -> 251 [label="" color=black]
		247 -> 250 [label="turn_data.get('critic_score') is not None" color=green]
		247 -> 251 [label="(turn_data.get('critic_score') is None)" color=red]
		246 -> 247 [label="" color=black]
		244 -> 246 [label="turn_data.get('critic_feedback')" color=green]
		244 -> 247 [label="(not turn_data.get('critic_feedback'))" color=red]
		234 -> 244 [label="" color=black]
		233 -> 234 [label="turn_data.get('critic_feedback') or turn_data.get('critic_score') is not None" color=red]
		236 [label="if turn_data.get('output_log'):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		236 -> 237 [label=calls style=dashed]
		subgraph cluster_236 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			237 [label="turn_data.get" color="#E552FF" shape=tab style=filled]
		}
		238 [label="html.append(f'        <div class=\"feedback\">')\lhtml.append(f\"            <p>{turn_data['output_log']}</p>\")\lhtml.append(f'        </div>')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		238 -> 240 [label=calls style=dashed]
		238 -> 241 [label=calls style=dashed]
		238 -> 242 [label=calls style=dashed]
		subgraph cluster_238 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			240 [label="html.append" color="#E552FF" shape=tab style=filled]
			241 [label="html.append" color="#E552FF" shape=tab style=filled]
			242 [label="html.append" color="#E552FF" shape=tab style=filled]
		}
		238 -> 235 [label="" color=black]
		236 -> 238 [label="turn_data.get('output_log')" color=green]
		236 -> 235 [label="(not turn_data.get('output_log'))" color=red]
		233 -> 236 [label="(not (turn_data.get('critic_feedback') or turn_data.get('critic_score') is not
    None))" color=red]
		228 -> 233 [label="" color=black]
		227 -> 228 [label=history color=green]
		229 [label="html.append('    <h2>Final Prompt</h2>')\lhtml.append(f\"    <pre>{state.get('draft', 'No final draft available')}</pre>\")\lhtml.append('</body>')\lhtml.append('</html>')\loutput_path.write_text('\n'.join(html))" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		229 -> 279 [label=calls style=dashed]
		229 -> 280 [label=calls style=dashed]
		229 -> 282 [label=calls style=dashed]
		229 -> 283 [label=calls style=dashed]
		229 -> 284 [label=calls style=dashed]
		subgraph cluster_229 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			279 [label="html.append" color="#E552FF" shape=tab style=filled]
			280 [label="html.append" color="#E552FF" shape=tab style=filled]
			281 [label="state.get" color="#E552FF" shape=tab style=filled]
			280 -> 281 [color=black]
			282 [label="html.append" color="#E552FF" shape=tab style=filled]
			283 [label="html.append" color="#E552FF" shape=tab style=filled]
			284 [label="output_path.write_text" color="#E552FF" shape=tab style=filled]
			285 [label="
.join" color="#E552FF" shape=tab style=filled]
			284 -> 285 [color=black]
		}
		286 [label="return output_path" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		229 -> 286 [label="" color=black]
		227 -> 229 [label="" color=green]
		218 -> 227 [label="" color=black]
		217 -> 218 [label="" color=black]
		216 -> 217 [label="output_path is None" color=green]
		216 -> 218 [label="(output_path is not None)" color=red]
		215 -> 216 [label="" color=black]
	}
	subgraph cluster0main {
		graph [compound=True fontname="DejaVu Sans Mono" label=main pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		290 [label="\"\"\"Entry point function for the CLI.\"\"\"\lapp()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		290 -> 291 [label=calls style=dashed]
		subgraph cluster_290 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			291 [label=app color="#E552FF" shape=tab style=filled]
		}
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
