digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/editor_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/editor_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"Editor role – revises the draft based on Critic feedback.\"\"\"\lfrom typing import Any, Dict\limport json\lfrom .base import BaseRole, RoleOutput\lclass Editor(BaseRole):\l    system_prompt = \"\"\"You are **Prompt-Reviser-PE-v1**, a senior prompt editor.\l=============  TASK  =============\lYou will receive:\l1. Draft – the current prompt pair (system + user).\l2. Feedback – a JSON object with fields:\l   • score (float 0-10)\l   • subscores      // clarity, structure, domain_safety, completeness, actionability\l   • strengths[]    // bullet strings\l   • weaknesses[]   // bullet strings (concrete fixes)\l   • suggestions[]  // bullet strings (concrete fixes)\lRevise the draft so that **every weakness is fixed and every suggestion is applied**, while preserving its strengths and overall intent.\l=============  OUTPUT  =============\lReturn **only** the fully-updated prompt pair, with the same wrappers used\lin the draft (e.g., `<s>…</s><user>…</user>`).  \lDo **NOT** add commentary, JSON, or markdown.\l=============  RULES  =============\l• Retain role names and placeholders exactly as they appear unless the suggestion says to rename them.  \l• Do not invent data – if the draft lacks Info X and no suggestion provides it, leave a clear placeholder.  \l• Ensure PE guard-rails remain intact: confidentiality, GAAP accuracy, \"Insufficient data\" rule.  \l• Keep citation style default to Markdown footnotes unless otherwise specified by the user\"\"\"\l    output_format_prompt = \"\"\"You are **Prompt-Format-Fixer**, a specialized prompt editor.\l=============  TASK  =============\lYou have been given a draft prompt that is missing required format markers.\lYour job is to modify the prompt to include the appropriate format marker.\l=============  REQUIRED FORMAT  =============\lThe prompt MUST include one of the following markers:\l1. JSON_OUTPUT: - Use when the prompt requires structured JSON output\l2. MARKDOWN_OUTPUT: - Use when the prompt requires formatted markdown output\lAdd the appropriate marker based on the content and requirements in the prompt.\lPlace the marker at the beginning of the 'Output Format' section.\l=============  OUTPUT  =============\lReturn the complete revised prompt with the format marker included.\lDo not add any additional commentary.\l\"\"\"\l\l    def eval(self, state: Dict[str, Any]) ->RoleOutput:\l        draft = state.get('draft')\l        if draft and not state.get('output_guardian_pass', False\l            ) and state.get('output_guardian_feedback'):\l            return self._fix_output_format(draft, state[\l                'output_guardian_feedback'])\l        critique = state.get('critic_feedback')\l        if not draft or not critique:\l            return RoleOutput({'log':\l                'Editor skipped – missing draft or feedback.'})\l        feedback_json = json.dumps(critique, indent=2) if isinstance(critique,\l            dict) else str(critique)\l        messages = [{'role': 'system', 'content': self.system_prompt}, {\l            'role': 'user', 'content': '<<<DRAFT>>>\n' + draft +\l            '\n<<<END>>>\n\n' + \"\"\"<<<FEEDBACK_JSON>>>\l\"\"\" + str(critique) +\l            '\n<<<END>>>\n\n' +\l            'Revise the draft so every weakness is fixed and every suggestion applied, '\l             + \"\"\"while preserving its strengths.\l\l\"\"\" +\l            'Return only the updated prompt pair with the same wrapper tags—no extra text.'\l            }]\l        new_draft = self._call_llm(messages)\l        return RoleOutput({'draft': new_draft, 'log':\l            'Editor produced revised draft.'})\l\l    def _fix_output_format(self, draft: str, guardian_feedback: str\l        ) ->RoleOutput:\l        \"\"\"Fix format issues reported by OutputGuardian.\"\"\"\l        messages = [{'role': 'system', 'content': self.output_format_prompt\l            }, {'role': 'user', 'content':\l            f\"\"\"The OutputGuardian reported: {guardian_feedback}\l\lHere is the draft that needs to have a format marker added:\l\l<<<DRAFT>>>\l{draft}\l<<<END>>>\l\lPlease add either JSON_OUTPUT: or MARKDOWN_OUTPUT: based on what's most appropriate for this prompt.\"\"\"\l            }]\l        fixed_draft = self._call_llm(messages)\l        return RoleOutput({'draft': fixed_draft, 'log':\l            'Editor added required output format marker.', 'format_fixed': \l            True})" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	subgraph cluster0Editor {
		graph [compound=True fontname="DejaVu Sans Mono" label=Editor pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		3 [label="system_prompt = \"\"\"You are **Prompt-Reviser-PE-v1**, a senior prompt editor.\l=============  TASK  =============\lYou will receive:\l1. Draft – the current prompt pair (system + user).\l2. Feedback – a JSON object with fields:\l   • score (float 0-10)\l   • subscores      // clarity, structure, domain_safety, completeness, actionability\l   • strengths[]    // bullet strings\l   • weaknesses[]   // bullet strings (concrete fixes)\l   • suggestions[]  // bullet strings (concrete fixes)\lRevise the draft so that **every weakness is fixed and every suggestion is applied**, while preserving its strengths and overall intent.\l=============  OUTPUT  =============\lReturn **only** the fully-updated prompt pair, with the same wrappers used\lin the draft (e.g., `<s>…</s><user>…</user>`).  \lDo **NOT** add commentary, JSON, or markdown.\l=============  RULES  =============\l• Retain role names and placeholders exactly as they appear unless the suggestion says to rename them.  \l• Do not invent data – if the draft lacks Info X and no suggestion provides it, leave a clear placeholder.  \l• Ensure PE guard-rails remain intact: confidentiality, GAAP accuracy, \"Insufficient data\" rule.  \l• Keep citation style default to Markdown footnotes unless otherwise specified by the user\"\"\"\loutput_format_prompt = \"\"\"You are **Prompt-Format-Fixer**, a specialized prompt editor.\l=============  TASK  =============\lYou have been given a draft prompt that is missing required format markers.\lYour job is to modify the prompt to include the appropriate format marker.\l=============  REQUIRED FORMAT  =============\lThe prompt MUST include one of the following markers:\l1. JSON_OUTPUT: - Use when the prompt requires structured JSON output\l2. MARKDOWN_OUTPUT: - Use when the prompt requires formatted markdown output\lAdd the appropriate marker based on the content and requirements in the prompt.\lPlace the marker at the beginning of the 'Output Format' section.\l=============  OUTPUT  =============\lReturn the complete revised prompt with the format marker included.\lDo not add any additional commentary.\l\"\"\"\ldef eval(self, state: Dict[str, Any]) ->RoleOutput:...\ldef _fix_output_format(self, draft: str, guardian_feedback: str) ->RoleOutput:..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0eval {
			graph [compound=True fontname="DejaVu Sans Mono" label=eval pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			5 [label="draft = state.get('draft')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			5 -> 6 [label=calls style=dashed]
			subgraph cluster_5 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				6 [label="state.get" color="#E552FF" shape=tab style=filled]
			}
			7 [label="if draft and not state.get('output_guardian_pass', False) and state.get(" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			8 [label="return self._fix_output_format(draft, state['output_guardian_feedback'])" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			7 -> 8 [label="draft and not state.get('output_guardian_pass', False) and state.get(
    'output_guardian_feedback')" color=red]
			9 [label="critique = state.get('critic_feedback')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			9 -> 11 [label=calls style=dashed]
			subgraph cluster_9 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				11 [label="state.get" color="#E552FF" shape=tab style=filled]
			}
			12 [label="if not draft or not critique:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			13 [label="return RoleOutput({'log': 'Editor skipped – missing draft or feedback.'})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			12 -> 13 [label="not draft or not critique" color=green]
			14 [label="feedback_json = json.dumps(critique, indent=2) if isinstance(critique, dict\l    ) else str(critique)\lmessages = [{'role': 'system', 'content': self.system_prompt}, {'role':\l    'user', 'content': '<<<DRAFT>>>\n' + draft + '\n<<<END>>>\n\n' +\l    \"\"\"<<<FEEDBACK_JSON>>>\l\"\"\" + str(critique) + '\n<<<END>>>\n\n' +\l    'Revise the draft so every weakness is fixed and every suggestion applied, '\l     + \"\"\"while preserving its strengths.\l\l\"\"\" +\l    'Return only the updated prompt pair with the same wrapper tags—no extra text.'\l    }]\lnew_draft = self._call_llm(messages)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			14 -> 16 [label=calls style=dashed]
			14 -> 17 [label=calls style=dashed]
			14 -> 18 [label=calls style=dashed]
			14 -> 19 [label=calls style=dashed]
			14 -> 20 [label=calls style=dashed]
			subgraph cluster_14 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				16 [label=isinstance color="#E552FF" shape=tab style=filled]
				17 [label="json.dumps" color="#E552FF" shape=tab style=filled]
				18 [label=str color="#E552FF" shape=tab style=filled]
				19 [label=str color="#E552FF" shape=tab style=filled]
				20 [label="self._call_llm" color="#E552FF" shape=tab style=filled]
			}
			21 [label="return RoleOutput({'draft': new_draft, 'log': 'Editor produced revised draft.'}\l    )" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			14 -> 21 [label="" color=black]
			12 -> 14 [label="(not (not draft or not critique))" color=red]
			9 -> 12 [label="" color=black]
			7 -> 9 [label="(not (draft and not state.get('output_guardian_pass', False) and state.get(
    'output_guardian_feedback')))" color=red]
			5 -> 7 [label="" color=black]
		}
		subgraph cluster0_fix_output_format {
			graph [compound=True fontname="DejaVu Sans Mono" label=_fix_output_format pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			25 [label="\"\"\"Fix format issues reported by OutputGuardian.\"\"\"\lmessages = [{'role': 'system', 'content': self.output_format_prompt}, {\l    'role': 'user', 'content':\l    f\"\"\"The OutputGuardian reported: {guardian_feedback}\l\lHere is the draft that needs to have a format marker added:\l\l<<<DRAFT>>>\l{draft}\l<<<END>>>\l\lPlease add either JSON_OUTPUT: or MARKDOWN_OUTPUT: based on what's most appropriate for this prompt.\"\"\"\l    }]\lfixed_draft = self._call_llm(messages)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			25 -> 26 [label=calls style=dashed]
			subgraph cluster_25 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				26 [label="self._call_llm" color="#E552FF" shape=tab style=filled]
			}
			27 [label="return RoleOutput({'draft': fixed_draft, 'log':\l    'Editor added required output format marker.', 'format_fixed': True})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			25 -> 27 [label="" color=black]
		}
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
