digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/app_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/app_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="from flask import Flask, render_template, request, jsonify, Response\lfrom flask_socketio import SocketIO, emit\limport json\limport threading\limport time\lfrom prompt_generator.orchestrator import Orchestrator\lfrom prompt_generator.config import load_config\limport queue\limport sys\lfrom io import StringIO\limport os\limport concurrent.futures\limport re\lfrom openai import OpenAI\lapp = Flask(__name__)\lapp.config['SECRET_KEY'] = 'your-secret-key-here'\lsocketio = SocketIO(app, cors_allowed_origins='*')\lmessage_queue = queue.Queue()\lconfig = load_config()\lapi_key = os.environ.get('OPENAI_API_KEY') or config.get('openai_api_key')\lopenai_client = OpenAI(api_key=api_key)\lclass WebSocketHandler:\l    \"\"\"Custom handler to capture output and send to WebSocket\"\"\"\l\l    def __init__(self, socketio, room):\l        self.socketio = socketio\l        self.room = room\l        self.buffer = ''\l\l    def write(self, text):\l        self.buffer += text\l        if '\n' in text:\l            lines = self.buffer.split('\n')\l            self.buffer = lines[-1]\l            for line in lines[:-1]:\l                if line.strip():\l                    self.socketio.emit('log_message', {'message': line.\l                        strip(), 'timestamp': time.strftime('%H:%M:%S')},\l                        room=self.room)\l\l    def flush(self):\l        if self.buffer.strip():\l            self.socketio.emit('log_message', {'message': self.buffer.strip\l                (), 'timestamp': time.strftime('%H:%M:%S')}, room=self.room)\l            self.buffer = ''\ldef run_orchestrator_with_websocket(task, target_score, max_turns, room):...\<EMAIL>('/')...\<EMAIL>('/api/generate', methods=['POST'])...\<EMAIL>('join')...\<EMAIL>('disconnect')...\ldef call_llm_for_test_data(prompt):...\ldef flatten_test_data(test_data):...\ldef extract_system_user_with_llm(prompt):...\l_extraction_cache = {}\ldef call_llm_with_prompt_and_input(prompt, input_str):...\ldef decide_test_case_types(prompt, test_case_count):...\ldef clean_test_case(text):...\ldef generate_single_test_case(prompt, typ, max_retries=2):...\ldef generate_test_data_parallel(prompt, test_case_count):...\<EMAIL>('/api/generate_test_data', methods=['POST'])...\<EMAIL>('/api/run_prompt_on_test_data', methods=['POST'])..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	1 -> 2 [label=calls style=dashed]
	1 -> 3 [label=calls style=dashed]
	1 -> 4 [label=calls style=dashed]
	1 -> 5 [label=calls style=dashed]
	1 -> 6 [label=calls style=dashed]
	1 -> 7 [label=calls style=dashed]
	1 -> 8 [label=calls style=dashed]
	subgraph cluster_1 {
		graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		2 [label=Flask color="#E552FF" shape=tab style=filled]
		3 [label=SocketIO color="#E552FF" shape=tab style=filled]
		4 [label="queue.Queue" color="#E552FF" shape=tab style=filled]
		5 [label=load_config color="#E552FF" shape=tab style=filled]
		6 [label="os.environ.get" color="#E552FF" shape=tab style=filled]
		7 [label="config.get" color="#E552FF" shape=tab style=filled]
		8 [label=OpenAI color="#E552FF" shape=tab style=filled]
	}
	298 [label="if __name__ == '__main__':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
	299 [label="socketio.run(app, debug=True, host='0.0.0.0', port=5001)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	299 -> 301 [label=calls style=dashed]
	subgraph cluster_299 {
		graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		301 [label="socketio.run" color="#E552FF" shape=tab style=filled]
	}
	298 -> 299 [label="__name__ == '__main__'" color=green]
	1 -> 298 [label="" color=black]
	subgraph cluster0WebSocketHandler {
		graph [compound=True fontname="DejaVu Sans Mono" label=WebSocketHandler pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		10 [label="\"\"\"Custom handler to capture output and send to WebSocket\"\"\"\ldef __init__(self, socketio, room):...\ldef write(self, text):...\ldef flush(self):..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0__init__ {
			graph [compound=True fontname="DejaVu Sans Mono" label=__init__ pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			12 [label="self.socketio = socketio\lself.room = room\lself.buffer = ''" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		}
		subgraph cluster0write {
			graph [compound=True fontname="DejaVu Sans Mono" label=write pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			15 [label="self.buffer += text" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			16 [label="if '\n' in text:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			17 [label="lines = self.buffer.split('\n')\lself.buffer = lines[-1]" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			17 -> 19 [label=calls style=dashed]
			subgraph cluster_17 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				19 [label="self.buffer.split" color="#E552FF" shape=tab style=filled]
			}
			20 [label="for line in lines[:-1]:" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
			21 [label="if line.strip():" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			21 -> 23 [label=calls style=dashed]
			subgraph cluster_21 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				23 [label="line.strip" color="#E552FF" shape=tab style=filled]
			}
			24 [label="self.socketio.emit('log_message', {'message': line.strip(), 'timestamp':\l    time.strftime('%H:%M:%S')}, room=self.room)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			24 -> 26 [label=calls style=dashed]
			subgraph cluster_24 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				26 [label="self.socketio.emit" color="#E552FF" shape=tab style=filled]
				27 [label="line.strip" color="#E552FF" shape=tab style=filled]
				26 -> 27 [color=black]
				28 [label="time.strftime" color="#E552FF" shape=tab style=filled]
				26 -> 28 [color=black]
			}
			24 -> 20 [label="" color=black]
			21 -> 24 [label="line.strip()" color=green]
			21 -> 20 [label="(not line.strip())" color=red]
			20 -> 21 [label="lines[:-1]" color=green]
			17 -> 20 [label="" color=black]
			16 -> 17 [label="'\n' in text" color=green]
			15 -> 16 [label="" color=black]
		}
		subgraph cluster0flush {
			graph [compound=True fontname="DejaVu Sans Mono" label=flush pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			31 [label="if self.buffer.strip():" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			31 -> 32 [label=calls style=dashed]
			subgraph cluster_31 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				32 [label="self.buffer.strip" color="#E552FF" shape=tab style=filled]
			}
			33 [label="self.socketio.emit('log_message', {'message': self.buffer.strip(),\l    'timestamp': time.strftime('%H:%M:%S')}, room=self.room)\lself.buffer = ''" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			33 -> 35 [label=calls style=dashed]
			subgraph cluster_33 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				35 [label="self.socketio.emit" color="#E552FF" shape=tab style=filled]
				36 [label="self.buffer.strip" color="#E552FF" shape=tab style=filled]
				35 -> 36 [color=black]
				37 [label="time.strftime" color="#E552FF" shape=tab style=filled]
				35 -> 37 [color=black]
			}
			31 -> 33 [label="self.buffer.strip()" color=green]
		}
	}
	subgraph cluster0run_orchestrator_with_websocket {
		graph [compound=True fontname="DejaVu Sans Mono" label=run_orchestrator_with_websocket pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		41 [label="\"\"\"Run the orchestrator and send updates via WebSocket\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		42 [label="old_stdout = sys.stdout
web_handler = WebSocketHandler(socketio, room)
sys.stdout = web_handler
socketio.emit('status_update', {'status': 'starting', 'message':
    f'Starting prompt generation for: \"{task}\"'}, room=room)
orchestrator = Orchestrator(target_score=target_score, max_turns=max_turns)
state = {'task': task, 'target_score': target_score}
socketio.emit('status_update', {'status': 'running', 'message':
    'Orchestrator initialized, beginning prompt generation...'}, room=room)" fillcolor=orange shape=Mdiamond style="filled,solid"]
		44 [label="socketio.emit('error', {'message': f'Error: {str(e)}'}, room=room)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		44 -> 45 [label=calls style=dashed]
		subgraph cluster_44 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			45 [label="socketio.emit" color="#E552FF" shape=tab style=filled]
			46 [label=str color="#E552FF" shape=tab style=filled]
			45 -> 46 [color=black]
		}
		43 [label="sys.stdout = old_stdout" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		44 -> 43 [label="" color=black]
		42 -> 47 [label=calls style=dashed]
		42 -> 48 [label=calls style=dashed]
		42 -> 49 [label=calls style=dashed]
		42 -> 50 [label=calls style=dashed]
		subgraph cluster_42 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			47 [label=WebSocketHandler color="#E552FF" shape=tab style=filled]
			48 [label="socketio.emit" color="#E552FF" shape=tab style=filled]
			49 [label=Orchestrator color="#E552FF" shape=tab style=filled]
			50 [label="socketio.emit" color="#E552FF" shape=tab style=filled]
		}
		51 [label="for turn in range(1, max_turns + 1):" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		51 -> 52 [label=calls style=dashed]
		subgraph cluster_51 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			52 [label=range color="#E552FF" shape=tab style=filled]
		}
		53 [label="role_name, reason = orchestrator._policy(state)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		53 -> 55 [label=calls style=dashed]
		subgraph cluster_53 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			55 [label="orchestrator._policy" color="#E552FF" shape=tab style=filled]
		}
		56 [label="if role_name is None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		57 [label="state['termination_reason'] = reason\lsocketio.emit('status_update', {'status': 'completed', 'message':\l    f'Termination: {reason}'}, room=room)\lbreak" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		57 -> 59 [label=calls style=dashed]
		subgraph cluster_57 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			59 [label="socketio.emit" color="#E552FF" shape=tab style=filled]
		}
		54 [label="final_prompt = state.get('draft', '<no prompt generated>')\lsocketio.emit('final_result', {'prompt': final_prompt, 'termination_reason':\l    state.get('termination_reason', 'Unknown'), 'total_turns': turn - 1 if \l    'turn' in locals() else 0}, room=room)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		54 -> 82 [label=calls style=dashed]
		54 -> 83 [label=calls style=dashed]
		subgraph cluster_54 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			82 [label="state.get" color="#E552FF" shape=tab style=filled]
			83 [label="socketio.emit" color="#E552FF" shape=tab style=filled]
			84 [label="state.get" color="#E552FF" shape=tab style=filled]
			83 -> 84 [color=black]
			85 [label=locals color="#E552FF" shape=tab style=filled]
			83 -> 85 [color=black]
		}
		54 -> 43 [label="" color=black]
		57 -> 54 [label="" color=black]
		56 -> 57 [label="role_name is None" color=green]
		58 [label="role = orchestrator.roles.get(role_name)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		58 -> 61 [label=calls style=dashed]
		subgraph cluster_58 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			61 [label="orchestrator.roles.get" color="#E552FF" shape=tab style=filled]
		}
		62 [label="if role is None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		63 [label="raise KeyError(f\"Role '{role_name}' not found\")" fillcolor="#98fb98" shape=house style="filled,solid"]
		65 [label="sys.stdout = old_stdout" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		63 -> 65 [label="" color=black]
		62 -> 63 [label="role is None" color=green]
		64 [label="socketio.emit('turn_update', {'turn': turn, 'role': role_name, 'reason':\l    reason}, room=room)\lprevious_draft = state.get('draft', '')\loutput = role.eval(state)\lstate.update(output.to_state_update())\lstate['_last_role'] = role_name\lcurrent_draft = state.get('draft', '')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		64 -> 67 [label=calls style=dashed]
		64 -> 68 [label=calls style=dashed]
		64 -> 69 [label=calls style=dashed]
		64 -> 70 [label=calls style=dashed]
		64 -> 72 [label=calls style=dashed]
		subgraph cluster_64 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			67 [label="socketio.emit" color="#E552FF" shape=tab style=filled]
			68 [label="state.get" color="#E552FF" shape=tab style=filled]
			69 [label="role.eval" color="#E552FF" shape=tab style=filled]
			70 [label="state.update" color="#E552FF" shape=tab style=filled]
			71 [label="output.to_state_update" color="#E552FF" shape=tab style=filled]
			70 -> 71 [color=black]
			72 [label="state.get" color="#E552FF" shape=tab style=filled]
		}
		73 [label="if current_draft != previous_draft:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		74 [label="socketio.emit('draft_update', {'draft': current_draft, 'role': role_name,\l    'turn': turn}, room=room)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		74 -> 76 [label=calls style=dashed]
		subgraph cluster_74 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			76 [label="socketio.emit" color="#E552FF" shape=tab style=filled]
		}
		75 [label="if 'critic_feedback' in state and role_name == 'Critic':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		77 [label="socketio.emit('critic_feedback', {'score': state.get('critic_score', 'N/A'),\l    'feedback': state['critic_feedback'], 'turn': turn}, room=room)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		77 -> 79 [label=calls style=dashed]
		subgraph cluster_77 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			79 [label="socketio.emit" color="#E552FF" shape=tab style=filled]
			80 [label="state.get" color="#E552FF" shape=tab style=filled]
			79 -> 80 [color=black]
		}
		78 [label="time.sleep(0.5)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		78 -> 81 [label=calls style=dashed]
		subgraph cluster_78 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			81 [label="time.sleep" color="#E552FF" shape=tab style=filled]
		}
		78 -> 51 [label="" color=black]
		77 -> 78 [label="" color=black]
		75 -> 77 [label="'critic_feedback' in state and role_name == 'Critic'" color=green]
		75 -> 78 [label="(not ('critic_feedback' in state and role_name == 'Critic'))" color=red]
		74 -> 75 [label="" color=black]
		73 -> 74 [label="current_draft != previous_draft" color=green]
		73 -> 75 [label="(current_draft == previous_draft)" color=red]
		64 -> 73 [label="" color=black]
		62 -> 64 [label="(role is not None)" color=red]
		58 -> 62 [label="" color=black]
		56 -> 58 [label="(role_name is not None)" color=red]
		53 -> 56 [label="" color=black]
		51 -> 53 [label="range(1, max_turns + 1)" color=green]
		51 -> 54 [label="" color=green]
		42 -> 51 [label="" color=black]
		41 -> 42 [label="" color=black]
	}
	subgraph cluster0index {
		graph [compound=True fontname="DejaVu Sans Mono" label=index pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		89 [label="return render_template('index.html')" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
	}
	subgraph cluster0generate_prompt {
		graph [compound=True fontname="DejaVu Sans Mono" label=generate_prompt pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		93 [label="data = request.json\ltask = data.get('task', '')\ltarget_score = float(data.get('target_score', 8.5))\lmax_turns = int(data.get('max_turns', 10))" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		93 -> 94 [label=calls style=dashed]
		93 -> 95 [label=calls style=dashed]
		93 -> 97 [label=calls style=dashed]
		subgraph cluster_93 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			94 [label="data.get" color="#E552FF" shape=tab style=filled]
			95 [label=float color="#E552FF" shape=tab style=filled]
			96 [label="data.get" color="#E552FF" shape=tab style=filled]
			95 -> 96 [color=black]
			97 [label=int color="#E552FF" shape=tab style=filled]
			98 [label="data.get" color="#E552FF" shape=tab style=filled]
			97 -> 98 [color=black]
		}
		99 [label="if not task:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		100 [label="return jsonify({'error': 'Task description is required'}), 400" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		99 -> 100 [label="not task" color=green]
		101 [label="room = f'session_{int(time.time())}'\lthread = threading.Thread(target=run_orchestrator_with_websocket, args=(\l    task, target_score, max_turns, room))\lthread.daemon = True\lthread.start()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		101 -> 103 [label=calls style=dashed]
		101 -> 105 [label=calls style=dashed]
		101 -> 106 [label=calls style=dashed]
		subgraph cluster_101 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			103 [label=int color="#E552FF" shape=tab style=filled]
			104 [label="time.time" color="#E552FF" shape=tab style=filled]
			103 -> 104 [color=black]
			105 [label="threading.Thread" color="#E552FF" shape=tab style=filled]
			106 [label="thread.start" color="#E552FF" shape=tab style=filled]
		}
		107 [label="return jsonify({'room': room, 'message': 'Generation started'})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		101 -> 107 [label="" color=black]
		99 -> 101 [label="(not not task)" color=red]
		93 -> 99 [label="" color=black]
	}
	subgraph cluster0on_join {
		graph [compound=True fontname="DejaVu Sans Mono" label=on_join pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		111 [label="room = data['room']\lfrom flask_socketio import join_room\ljoin_room(room)\lemit('status', {'msg': f'Joined room: {room}'})" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		111 -> 112 [label=calls style=dashed]
		111 -> 113 [label=calls style=dashed]
		subgraph cluster_111 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			112 [label=join_room color="#E552FF" shape=tab style=filled]
			113 [label=emit color="#E552FF" shape=tab style=filled]
		}
	}
	subgraph cluster0on_disconnect {
		graph [compound=True fontname="DejaVu Sans Mono" label=on_disconnect pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		116 [label="print('Client disconnected')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		116 -> 117 [label=calls style=dashed]
		subgraph cluster_116 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			117 [label=print color="#E552FF" shape=tab style=filled]
		}
	}
	subgraph cluster0call_llm_for_test_data {
		graph [compound=True fontname="DejaVu Sans Mono" label=call_llm_for_test_data pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		120 [label="\"\"\"Call LLM to generate context-aware test data as a JSON object.\"\"\"\lsystem_prompt = \"\"\"You are a test data generator for prompt engineering.\lGiven the following prompt:\l\"\"\" + prompt + \"\"\"\l\lDecide what types of input data (emails, business documents, web search results, website snippets, etc.) would be most appropriate to test this prompt.\lGenerate a realistic set of test inputs (at least 3, up to 10), using the types you think are best.\lOutput your test data as a JSON object, with keys for each type (e.g., 'emails', 'documents', 'web_search_results', etc.).\"\"\"\lresponse = openai_client.chat.completions.create(model='gpt-4o', messages=[\l    {'role': 'system', 'content': system_prompt}, {'role': 'user',\l    'content': 'Generate the test data now.'}], temperature=0.7, max_tokens=800\l    )\lcontent = response.choices[0].message.content\lmatch = re.search('\\{[\\s\\S]*\\}', content)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		120 -> 121 [label=calls style=dashed]
		120 -> 122 [label=calls style=dashed]
		subgraph cluster_120 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			121 [label="openai_client.chat.completions.create" color="#E552FF" shape=tab style=filled]
			122 [label="re.search" color="#E552FF" shape=tab style=filled]
		}
		123 [label="if match:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		124 [label="json_str = match.group(0)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		124 -> 128 [label=calls style=dashed]
		subgraph cluster_124 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			128 [label="match.group" color="#E552FF" shape=tab style=filled]
		}
		129 [label="try:" fillcolor=orange shape=Mdiamond style="filled,solid"]
		131 [label="" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		132 [label="try:" fillcolor=orange shape=Mdiamond style="filled,solid"]
		134 [label="return {'raw': content}" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		136 [label="return ast.literal_eval(json_str)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		132 -> 136 [label="" color=black]
		131 -> 132 [label="" color=black]
		138 [label="return json.loads(json_str)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		129 -> 138 [label="" color=black]
		124 -> 129 [label="" color=black]
		123 -> 124 [label=match color=green]
		126 [label="return {'raw': content}" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		123 -> 126 [label="(not match)" color=red]
		120 -> 123 [label="" color=black]
	}
	subgraph cluster0flatten_test_data {
		graph [compound=True fontname="DejaVu Sans Mono" label=flatten_test_data pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		142 [label="\"\"\"Yield (type, input_str) for each test input in the test_data dict.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		143 [label="for key, items in test_data.items():" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		143 -> 144 [label=calls style=dashed]
		subgraph cluster_143 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			144 [label="test_data.items" color="#E552FF" shape=tab style=filled]
		}
		145 [label="if isinstance(items, list):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		145 -> 147 [label=calls style=dashed]
		subgraph cluster_145 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			147 [label=isinstance color="#E552FF" shape=tab style=filled]
		}
		148 [label="for item in items:" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		150 [label="if isinstance(item, dict):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		150 -> 152 [label=calls style=dashed]
		subgraph cluster_150 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			152 [label=isinstance color="#E552FF" shape=tab style=filled]
		}
		153 [label="input_str = json.dumps(item, indent=2)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		153 -> 157 [label=calls style=dashed]
		subgraph cluster_153 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			157 [label="json.dumps" color="#E552FF" shape=tab style=filled]
		}
		154 [label="yield key, input_str" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		154 -> 148 [label="" color=black]
		153 -> 154 [label="" color=black]
		150 -> 153 [label="isinstance(item, dict)" color=green]
		155 [label="input_str = str(item)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		155 -> 156 [label=calls style=dashed]
		subgraph cluster_155 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			156 [label=str color="#E552FF" shape=tab style=filled]
		}
		155 -> 154 [label="" color=black]
		150 -> 155 [label="(not isinstance(item, dict))" color=red]
		148 -> 150 [label=items color=green]
		148 -> 143 [label="" color=green]
		145 -> 148 [label="isinstance(items, list)" color=green]
		145 -> 143 [label="(not isinstance(items, list))" color=red]
		143 -> 145 [label="test_data.items()" color=green]
		142 -> 143 [label="" color=black]
	}
	subgraph cluster0extract_system_user_with_llm {
		graph [compound=True fontname="DejaVu Sans Mono" label=extract_system_user_with_llm pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		161 [label="\"\"\"Use LLM to extract or transform prompt into system/user messages.\"\"\"\lextraction_prompt = (\l    \"\"\"You are a prompt parser.\lGiven the following prompt, extract the system message and the user message as they would be sent to an LLM API (e.g., OpenAI Chat API).\lOutput a JSON object with two fields: system and user. If you can't find one, leave it as an empty string.\lPROMPT:\l\"\"\"\l     + prompt)\lresponse = openai_client.chat.completions.create(model='gpt-4o', messages=[\l    {'role': 'system', 'content': extraction_prompt}], temperature=0.0,\l    max_tokens=600)\limport json\limport re\lcontent = response.choices[0].message.content\lmatch = re.search('\\{[\\s\\S]*\\}', content)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		161 -> 162 [label=calls style=dashed]
		161 -> 163 [label=calls style=dashed]
		subgraph cluster_161 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			162 [label="openai_client.chat.completions.create" color="#E552FF" shape=tab style=filled]
			163 [label="re.search" color="#E552FF" shape=tab style=filled]
		}
		164 [label="if match:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		165 [label="json_str = match.group(0)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		165 -> 167 [label=calls style=dashed]
		subgraph cluster_165 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			167 [label="match.group" color="#E552FF" shape=tab style=filled]
		}
		168 [label="result = json.loads(json_str)" fillcolor=orange shape=Mdiamond style="filled,solid"]
		170 [label="" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		169 [label="" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		166 [label="return prompt, ''" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		169 -> 166 [label="" color=black]
		170 -> 169 [label="" color=black]
		168 -> 171 [label=calls style=dashed]
		subgraph cluster_168 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			171 [label="json.loads" color="#E552FF" shape=tab style=filled]
		}
		172 [label="return result.get('system', '').strip(), result.get('user', '').strip()" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		168 -> 172 [label="" color=black]
		165 -> 168 [label="" color=black]
		164 -> 165 [label=match color=green]
		164 -> 166 [label="(not match)" color=red]
		161 -> 164 [label="" color=black]
	}
	subgraph cluster0call_llm_with_prompt_and_input {
		graph [compound=True fontname="DejaVu Sans Mono" label=call_llm_with_prompt_and_input pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		177 [label="if prompt in _extraction_cache:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		178 [label="system_msg, user_msg = _extraction_cache[prompt]" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		179 [label="if system_msg and user_msg:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		182 [label="messages = [{'role': 'system', 'content': system_msg}, {'role': 'user',\l    'content': user_msg + '\n' + input_str}]" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		183 [label="response = openai_client.chat.completions.create(model='gpt-4o', messages=\l    messages, temperature=0.7, max_tokens=800)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		183 -> 185 [label=calls style=dashed]
		subgraph cluster_183 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			185 [label="openai_client.chat.completions.create" color="#E552FF" shape=tab style=filled]
		}
		186 [label="return response.choices[0].message.content.strip()" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		183 -> 186 [label="" color=black]
		182 -> 183 [label="" color=black]
		179 -> 182 [label="system_msg and user_msg" color=green]
		184 [label="messages = [{'role': 'system', 'content': prompt}, {'role': 'user',\l    'content': input_str}]" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		184 -> 183 [label="" color=black]
		179 -> 184 [label="(not (system_msg and user_msg))" color=red]
		178 -> 179 [label="" color=black]
		177 -> 178 [label="prompt in _extraction_cache" color=green]
		180 [label="system_msg, user_msg = extract_system_user_with_llm(prompt)\l_extraction_cache[prompt] = system_msg, user_msg" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		180 -> 181 [label=calls style=dashed]
		subgraph cluster_180 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			181 [label=extract_system_user_with_llm color="#E552FF" shape=tab style=filled]
		}
		180 -> 179 [label="" color=black]
		177 -> 180 [label="(prompt not in _extraction_cache)" color=red]
	}
	subgraph cluster0decide_test_case_types {
		graph [compound=True fontname="DejaVu Sans Mono" label=decide_test_case_types pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		190 [label="\"\"\"Call LLM to decide what types of test cases to generate and how many of each.\"\"\"\lsystem_prompt = \"\"\"You are a test data planner for prompt engineering.\lGiven the following prompt:\l\"\"\" + prompt + f\"\"\"\l\lThe user wants to generate {test_case_count} test cases.\lDecide what types of input data (emails, business documents, web search results, website snippets, etc.) would be most appropriate to test this prompt.\lDistribute the test cases among the types you choose.\lOutput a JSON object with keys as types and values as the number of test cases for each type.\"\"\"\lresponse = openai_client.chat.completions.create(model='gpt-4o', messages=[\l    {'role': 'system', 'content': system_prompt}, {'role': 'user',\l    'content': 'Plan the test case types now.'}], temperature=0.3,\l    max_tokens=300)\lcontent = response.choices[0].message.content\lmatch = re.search('\\{[\\s\\S]*\\}', content)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		190 -> 191 [label=calls style=dashed]
		190 -> 192 [label=calls style=dashed]
		subgraph cluster_190 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			191 [label="openai_client.chat.completions.create" color="#E552FF" shape=tab style=filled]
			192 [label="re.search" color="#E552FF" shape=tab style=filled]
		}
		193 [label="if match:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		194 [label="json_str = match.group(0)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		194 -> 198 [label=calls style=dashed]
		subgraph cluster_194 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			198 [label="match.group" color="#E552FF" shape=tab style=filled]
		}
		199 [label="try:" fillcolor=orange shape=Mdiamond style="filled,solid"]
		201 [label="" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		202 [label="try:" fillcolor=orange shape=Mdiamond style="filled,solid"]
		204 [label="return {'raw': content}" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		206 [label="return ast.literal_eval(json_str)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		202 -> 206 [label="" color=black]
		201 -> 202 [label="" color=black]
		208 [label="return json.loads(json_str)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		199 -> 208 [label="" color=black]
		194 -> 199 [label="" color=black]
		193 -> 194 [label=match color=green]
		196 [label="return {'raw': content}" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		193 -> 196 [label="(not match)" color=red]
		190 -> 193 [label="" color=black]
	}
	subgraph cluster0clean_test_case {
		graph [compound=True fontname="DejaVu Sans Mono" label=clean_test_case pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		212 [label="import re\ltext = re.sub('^```[a-zA-Z]*\\n?', '', text)\ltext = re.sub('```$', '', text)\ltext = text.strip()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		212 -> 213 [label=calls style=dashed]
		212 -> 214 [label=calls style=dashed]
		212 -> 215 [label=calls style=dashed]
		subgraph cluster_212 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			213 [label="re.sub" color="#E552FF" shape=tab style=filled]
			214 [label="re.sub" color="#E552FF" shape=tab style=filled]
			215 [label="text.strip" color="#E552FF" shape=tab style=filled]
		}
		216 [label="if text.lower().startswith(\"i'm sorry\") or \"can't assist\" in text.lower(" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		217 [label="return None" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		216 -> 217 [label="text.lower().startswith(\"i'm sorry\") or \"can't assist\" in text.lower(
    ) or not text" color=red]
		218 [label="return text" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		216 -> 218 [label="(not (text.lower().startswith(\"i'm sorry\") or \"can't assist\" in text.lower(
    ) or not text))" color=red]
		212 -> 216 [label="" color=black]
	}
	subgraph cluster0generate_single_test_case {
		graph [compound=True fontname="DejaVu Sans Mono" label=generate_single_test_case pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		223 [label="\"\"\"Call LLM to generate a single test case of the given type, with cleaning, retry, and more detailed, filled-out input.\"\"\"\luser_prompt = f\"\"\"Given the following system prompt: {prompt}\lGenerate a realistic {typ.replace('_', ' ')} as test input for this prompt.\lCome up with plausible, detailed values for all variables or placeholders (e.g., [[COMPANY_NAME]], [[REGION_OR_SEGMENT]], etc.) as if you were a real user or a real-world scenario.\lMake the input substantial and realistic—at least 10-15 sentences or 2-3 full paragraphs, not just a short phrase.\lInclude rich context, specific details, realistic scenarios, and comprehensive information that would challenge the prompt's capabilities.\lDo not wrap your response in code fences or markdown.\lDo not apologize or refuse; always generate a realistic input.\lOnly return the raw input data, nothing else.\"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		223 -> 224 [label=calls style=dashed]
		subgraph cluster_223 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			224 [label="typ.replace" color="#E552FF" shape=tab style=filled]
		}
		225 [label="for attempt in range(max_retries + 1):" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		225 -> 226 [label=calls style=dashed]
		subgraph cluster_225 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			226 [label=range color="#E552FF" shape=tab style=filled]
		}
		227 [label="response = openai_client.chat.completions.create(model='gpt-4o', messages=[\l    {'role': 'system', 'content': user_prompt}], temperature=0.8,\l    max_tokens=1500)\lcontent = response.choices[0].message.content.strip()\lcleaned = clean_test_case(content)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		227 -> 229 [label=calls style=dashed]
		227 -> 230 [label=calls style=dashed]
		227 -> 231 [label=calls style=dashed]
		subgraph cluster_227 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			229 [label="openai_client.chat.completions.create" color="#E552FF" shape=tab style=filled]
			230 [label="response.choices.message.content.strip" color="#E552FF" shape=tab style=filled]
			231 [label=clean_test_case color="#E552FF" shape=tab style=filled]
		}
		232 [label="if cleaned and (cleaned.count('.') + cleaned.count('!') + cleaned.count('?'" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		233 [label="return typ, cleaned" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		232 -> 233 [label="cleaned and (cleaned.count('.') + cleaned.count('!') + cleaned.count('?') >=
    10 or len(cleaned) >= 1000)" color=red]
		232 -> 225 [label="(not (cleaned and (cleaned.count('.') + cleaned.count('!') + cleaned.count(
    '?') >= 10 or len(cleaned) >= 1000)))" color=red]
		227 -> 232 [label="" color=black]
		225 -> 227 [label="range(max_retries + 1)" color=green]
		228 [label="return typ, cleaned if cleaned else content" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		225 -> 228 [label="" color=green]
		223 -> 225 [label="" color=black]
	}
	subgraph cluster0generate_test_data_parallel {
		graph [compound=True fontname="DejaVu Sans Mono" label=generate_test_data_parallel pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		239 [label="plan = decide_test_case_types(prompt, test_case_count)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		239 -> 240 [label=calls style=dashed]
		subgraph cluster_239 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			240 [label=decide_test_case_types color="#E552FF" shape=tab style=filled]
		}
		241 [label="if not isinstance(plan, dict):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		242 [label="return {'error': 'Could not determine test case types.', 'raw': plan}" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		241 -> 242 [label="not isinstance(plan, dict)" color=green]
		243 [label="results = {}\lfutures = []" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		243 -> 245 [label=calls style=dashed]
		subgraph cluster_243 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			245 [label="concurrent.futures.ThreadPoolExecutor" color="#E552FF" shape=tab style=filled]
		}
		246 [label="for typ, count in plan.items():" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		246 -> 247 [label=calls style=dashed]
		subgraph cluster_246 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			247 [label="plan.items" color="#E552FF" shape=tab style=filled]
		}
		249 [label="for future in concurrent.futures.as_completed(futures):" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		249 -> 259 [label=calls style=dashed]
		subgraph cluster_249 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			259 [label="concurrent.futures.as_completed" color="#E552FF" shape=tab style=filled]
		}
		260 [label="typ, test_case = future.result()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		260 -> 262 [label=calls style=dashed]
		subgraph cluster_260 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			262 [label="future.result" color="#E552FF" shape=tab style=filled]
		}
		263 [label="if test_case:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		264 [label="results.setdefault(typ, []).append(test_case)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		264 -> 266 [label=calls style=dashed]
		subgraph cluster_264 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			266 [label="results.setdefault.append" color="#E552FF" shape=tab style=filled]
		}
		265 [label="" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		265 -> 249 [label="" color=black]
		264 -> 265 [label="" color=black]
		263 -> 264 [label=test_case color=green]
		263 -> 265 [label="(not test_case)" color=red]
		260 -> 263 [label="" color=black]
		249 -> 260 [label="concurrent.futures.as_completed(futures)" color=green]
		261 [label="return results" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		249 -> 261 [label="" color=green]
		246 -> 249 [label="" color=green]
		250 [label="n = int(count)" fillcolor=orange shape=Mdiamond style="filled,solid"]
		252 [label="n = 1" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		251 [label="for _ in range(n):" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		251 -> 254 [label=calls style=dashed]
		subgraph cluster_251 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			254 [label=range color="#E552FF" shape=tab style=filled]
		}
		255 [label="futures.append(executor.submit(generate_single_test_case, prompt, typ))" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		255 -> 257 [label=calls style=dashed]
		subgraph cluster_255 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			257 [label="futures.append" color="#E552FF" shape=tab style=filled]
			258 [label="executor.submit" color="#E552FF" shape=tab style=filled]
			257 -> 258 [color=black]
		}
		255 -> 251 [label="" color=black]
		251 -> 255 [label="range(n)" color=green]
		251 -> 246 [label="" color=green]
		252 -> 251 [label="" color=black]
		250 -> 253 [label=calls style=dashed]
		subgraph cluster_250 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			253 [label=int color="#E552FF" shape=tab style=filled]
		}
		250 -> 251 [label="" color=black]
		246 -> 250 [label="plan.items()" color=green]
		243 -> 246 [label="" color=black]
		241 -> 243 [label="(not not isinstance(plan, dict))" color=red]
		239 -> 241 [label="" color=black]
	}
	subgraph cluster0api_generate_test_data {
		graph [compound=True fontname="DejaVu Sans Mono" label=api_generate_test_data pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		270 [label="data = request.json\lprompt = data.get('prompt', '')\ltest_case_count = int(data.get('test_case_count', 5))" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		270 -> 271 [label=calls style=dashed]
		270 -> 272 [label=calls style=dashed]
		subgraph cluster_270 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			271 [label="data.get" color="#E552FF" shape=tab style=filled]
			272 [label=int color="#E552FF" shape=tab style=filled]
			273 [label="data.get" color="#E552FF" shape=tab style=filled]
			272 -> 273 [color=black]
		}
		274 [label="if not prompt:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		275 [label="return jsonify({'error': 'Prompt is required'}), 400" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		274 -> 275 [label="not prompt" color=green]
		276 [label="test_data = generate_test_data_parallel(prompt, test_case_count)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		276 -> 278 [label=calls style=dashed]
		subgraph cluster_276 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			278 [label=generate_test_data_parallel color="#E552FF" shape=tab style=filled]
		}
		279 [label="return jsonify(test_data)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		276 -> 279 [label="" color=black]
		274 -> 276 [label="(not not prompt)" color=red]
		270 -> 274 [label="" color=black]
	}
	subgraph cluster0api_run_prompt_on_test_data {
		graph [compound=True fontname="DejaVu Sans Mono" label=api_run_prompt_on_test_data pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		283 [label="data = request.json\lprompt = data.get('prompt', '')\ltest_data = data.get('test_data', {})" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		283 -> 284 [label=calls style=dashed]
		283 -> 285 [label=calls style=dashed]
		subgraph cluster_283 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			284 [label="data.get" color="#E552FF" shape=tab style=filled]
			285 [label="data.get" color="#E552FF" shape=tab style=filled]
		}
		286 [label="if not prompt or not test_data:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
		287 [label="return jsonify({'error': 'Prompt and test_data are required'}), 400" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		286 -> 287 [label="not prompt or not test_data" color=green]
		288 [label="results = []" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		290 [label="for typ, input_str in flatten_test_data(test_data):" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
		290 -> 291 [label=calls style=dashed]
		subgraph cluster_290 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			291 [label=flatten_test_data color="#E552FF" shape=tab style=filled]
		}
		292 [label="output = call_llm_with_prompt_and_input(prompt, input_str)\lresults.append({'input': f\"\"\"[{typ}]\l{input_str}\"\"\", 'output': output})" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		292 -> 294 [label=calls style=dashed]
		292 -> 295 [label=calls style=dashed]
		subgraph cluster_292 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			294 [label=call_llm_with_prompt_and_input color="#E552FF" shape=tab style=filled]
			295 [label="results.append" color="#E552FF" shape=tab style=filled]
		}
		"292_input" [label=call_llm_with_prompt_and_input fillcolor="#afeeee" shape=parallelogram style="filled,solid"]
		"292_input" -> 292
		292 -> 290 [label="" color=black]
		290 -> 292 [label="flatten_test_data(test_data)" color=green]
		293 [label="return jsonify(results)" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
		290 -> 293 [label="" color=green]
		288 -> 290 [label="" color=black]
		286 -> 288 [label="(not (not prompt or not test_data))" color=red]
		283 -> 286 [label="" color=black]
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
