digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/output_guardian_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/output_guardian_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"OutputGuardian – validates that the prompt instructs the model to reply in a specific format.\l\lCurrently we support two formats:\l1. JSON_OUTPUT: signals that the model should respond with a JSON object\l2. MARKDOWN_OUTPUT: signals that the model should respond with Markdown format\l\lIn production you would load an external JSON Schema and run deterministic validation, \lbut this keeps the demo light.\l\"\"\"\lfrom typing import Any, Dict, List\lfrom .base import BaseRole, RoleOutput\lclass OutputGuardian(BaseRole):\l    allowed_markers: List[str] = ['JSON_OUTPUT:', 'MARKDOWN_OUTPUT:']\l\l    def eval(self, state: Dict[str, Any]) ->RoleOutput:\l        draft = state.get('draft', '')\l        found_markers = [marker for marker in self.allowed_markers if \l            marker in draft]\l        passes = len(found_markers) > 0\l        if passes:\l            marker_used = found_markers[0]\l            feedback = f\"✔ Draft contains required marker '{marker_used}'.\"\l        else:\l            feedback = (\l                f\"✖ Draft is missing required marker. Must include one of: {', '.join(self.allowed_markers)}.\"\l                )\l        return RoleOutput({'output_guardian_pass': passes,\l            'output_guardian_feedback': feedback, 'log':\l            f'OutputGuardian check -> {feedback}'})" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	subgraph cluster0OutputGuardian {
		graph [compound=True fontname="DejaVu Sans Mono" label=OutputGuardian pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		3 [label="allowed_markers: List[str] = ['JSON_OUTPUT:', 'MARKDOWN_OUTPUT:']\ldef eval(self, state: Dict[str, Any]) ->RoleOutput:..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0eval {
			graph [compound=True fontname="DejaVu Sans Mono" label=eval pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			5 [label="draft = state.get('draft', '')\lfound_markers = [marker for marker in self.allowed_markers if marker in draft]\lpasses = len(found_markers) > 0" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			5 -> 6 [label=calls style=dashed]
			5 -> 7 [label=calls style=dashed]
			subgraph cluster_5 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				6 [label="state.get" color="#E552FF" shape=tab style=filled]
				7 [label=len color="#E552FF" shape=tab style=filled]
			}
			8 [label="if passes:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			9 [label="marker_used = found_markers[0]\lfeedback = f\"✔ Draft contains required marker '{marker_used}'.\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			10 [label="return RoleOutput({'output_guardian_pass': passes,\l    'output_guardian_feedback': feedback, 'log':\l    f'OutputGuardian check -> {feedback}'})" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			9 -> 10 [label="" color=black]
			8 -> 9 [label=passes color=green]
			11 [label="feedback = (\l    f\"✖ Draft is missing required marker. Must include one of: {', '.join(self.allowed_markers)}.\"\l    )" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			11 -> 12 [label=calls style=dashed]
			subgraph cluster_11 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				12 [label=", .join" color="#E552FF" shape=tab style=filled]
			}
			11 -> 10 [label="" color=black]
			8 -> 11 [label="(not passes)" color=red]
			5 -> 8 [label="" color=black]
		}
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
