digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/test_server_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/test_server_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"Simple HTTP server to test the interactive documentation.\"\"\"\limport http.server\limport socketserver\limport os\limport sys\lPORT = 8000\lclass MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):\l\l    def end_headers(self):\l        self.send_header('Cache-Control', 'no-store, no-cache, must-revalidate'\l            )\l        self.send_header('Pragma', 'no-cache')\l        self.send_header('Expires', '0')\l        super().end_headers()\ldef main():..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	27 [label="if __name__ == '__main__':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
	28 [label="main()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	28 -> 30 [label=calls style=dashed]
	subgraph cluster_28 {
		graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		30 [label=main color="#E552FF" shape=tab style=filled]
	}
	27 -> 28 [label="__name__ == '__main__'" color=green]
	1 -> 27 [label="" color=black]
	subgraph cluster0MyHTTPRequestHandler {
		graph [compound=True fontname="DejaVu Sans Mono" label=MyHTTPRequestHandler pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		3 [label="def end_headers(self):..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0end_headers {
			graph [compound=True fontname="DejaVu Sans Mono" label=end_headers pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			5 [label="self.send_header('Cache-Control', 'no-store, no-cache, must-revalidate')\lself.send_header('Pragma', 'no-cache')\lself.send_header('Expires', '0')\lsuper().end_headers()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			5 -> 6 [label=calls style=dashed]
			5 -> 7 [label=calls style=dashed]
			5 -> 8 [label=calls style=dashed]
			5 -> 9 [label=calls style=dashed]
			subgraph cluster_5 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				6 [label="self.send_header" color="#E552FF" shape=tab style=filled]
				7 [label="self.send_header" color="#E552FF" shape=tab style=filled]
				8 [label="self.send_header" color="#E552FF" shape=tab style=filled]
				9 [label="super.end_headers" color="#E552FF" shape=tab style=filled]
			}
		}
	}
	subgraph cluster0main {
		graph [compound=True fontname="DejaVu Sans Mono" label=main pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		13 [label="os.chdir(os.path.dirname(os.path.abspath(__file__)))\lprint(f'Server running at http://localhost:{PORT}/')\lprint('Press Ctrl+C to stop the server')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		13 -> 14 [label=calls style=dashed]
		13 -> 17 [label=calls style=dashed]
		13 -> 18 [label=calls style=dashed]
		13 -> 19 [label=calls style=dashed]
		subgraph cluster_13 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			14 [label="os.chdir" color="#E552FF" shape=tab style=filled]
			15 [label="os.path.dirname" color="#E552FF" shape=tab style=filled]
			14 -> 15 [color=black]
			16 [label="os.path.abspath" color="#E552FF" shape=tab style=filled]
			15 -> 16 [color=black]
			17 [label="socketserver.TCPServer" color="#E552FF" shape=tab style=filled]
			18 [label=print color="#E552FF" shape=tab style=filled]
			19 [label=print color="#E552FF" shape=tab style=filled]
		}
		20 [label="httpd.serve_forever()" fillcolor=orange shape=Mdiamond style="filled,solid"]
		22 [label="print('\nServer stopped.')\lsys.exit(0)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		22 -> 23 [label=calls style=dashed]
		22 -> 24 [label=calls style=dashed]
		subgraph cluster_22 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			23 [label=print color="#E552FF" shape=tab style=filled]
			24 [label="sys.exit" color="#E552FF" shape=tab style=filled]
		}
		20 -> 25 [label=calls style=dashed]
		subgraph cluster_20 {
			graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			25 [label="httpd.serve_forever" color="#E552FF" shape=tab style=filled]
		}
		13 -> 20 [label="" color=black]
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
