digraph "cluster0/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/orchestrator_cfg.dot" {
	graph [compound=True fontname="DejaVu Sans Mono" label="/Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/_temp/cfg_output/orchestrator_cfg.dot" pack=False rankdir=TB ranksep=0.02]
	node [fontname="DejaVu Sans Mono"]
	edge [fontname="DejaVu Sans Mono"]
	1 [label="\"\"\"Orchestrator – decides which role should act next based on current state.\"\"\"\lfrom __future__ import annotations\lfrom typing import Any, Dict, Tuple\lfrom .config import load_config\lfrom .roles import discover_roles\lconfig = load_config()\lDEFAULT_TARGET_SCORE = config.get('target_score', 8.0)\lDEFAULT_MAX_TURNS = config.get('max_turns', 10)\lclass Orchestrator:\l    \"\"\"Manages conversational turns between virtual teammates.\"\"\"\l\l    def __init__(self, target_score: float=DEFAULT_TARGET_SCORE, max_turns:\l        int=DEFAULT_MAX_TURNS):\l        self.target_score = target_score\l        self.max_turns = max_turns\l        self.roles = discover_roles()\l\l    def _policy(self, state: Dict[str, Any]) ->Tuple[str | None, str]:\l        \"\"\"Deterministic policy function.\l\l        Returns (next_role_name | None, reason)\l        \"\"\"\l        if 'draft' not in state:\l            return 'Writer', 'Need initial draft'\l        if 'output_guardian_pass' in state:\l            if not state['output_guardian_pass'] and state.get('_last_role'\l                ) == 'OutputGuardian':\l                if state.get('format_fixed'):\l                    return ('Critic',\l                        'Proceed with quality evaluation despite format issues'\l                        )\l                else:\l                    return ('Editor',\l                        'Fix output format based on guardian feedback')\l        if not state.get('output_guardian_pass', False) and state.get(\l            '_last_role') != 'OutputGuardian':\l            return 'OutputGuardian', 'Check output format compliance'\l        if 'critic_score' not in state:\l            return 'Critic', 'First quality assessment'\l        if state.get('critic_score', 0) < self.target_score:\l            if state.get('_last_role') == 'Editor':\l                return 'Critic', 'Re-evaluate after editing'\l            return 'Editor', 'Improve draft based on critique'\l        token_pct = state.get('token_saving_pct')\l        if token_pct is None:\l            return 'TokenOptimizer', 'Attempt token optimisation'\l        if 'tokens_saved' in state and state.get('log', '').find('exceeds'\l            ) != -1:\l            return (None,\l                'All criteria met - skipping token optimization due to excessive savings'\l                )\l        if token_pct > 15:\l            return 'TokenOptimizer', 'Attempt token optimisation'\l        return None, 'All criteria met'\l\l    def run(self, task_description: str) ->Tuple[Dict[str, Any], list[Dict[\l        str, Any]]]:\l        \"\"\"Execute orchestration loop.\l\l        Returns final *state* and *history* list.\l        \"\"\"\l        state: Dict[str, Any] = {'task': task_description, 'target_score':\l            self.target_score}\l        history: list[Dict[str, Any]] = []\l        for turn in range(1, self.max_turns + 1):\l            role_name, reason = self._policy(state)\l            if role_name is None:\l                state['termination_reason'] = reason\l                break\l            role = self.roles.get(role_name)\l            if role is None:\l                raise KeyError(\l                    f\"Role '{role_name}' not found. Ensure it exists in roles package.\"\l                    )\l            output = role.eval(state)\l            state.update(output.to_state_update())\l            state['_last_role'] = role_name\l            history.append({'turn': turn, 'role': role_name, 'reason':\l                reason, 'output_log': state.get('log', '')})\l            state.pop('log', None)\l        return state, history" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
	1 -> 2 [label=calls style=dashed]
	1 -> 3 [label=calls style=dashed]
	1 -> 4 [label=calls style=dashed]
	subgraph cluster_1 {
		graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		2 [label=load_config color="#E552FF" shape=tab style=filled]
		3 [label="config.get" color="#E552FF" shape=tab style=filled]
		4 [label="config.get" color="#E552FF" shape=tab style=filled]
	}
	subgraph cluster0Orchestrator {
		graph [compound=True fontname="DejaVu Sans Mono" label=Orchestrator pack=False rankdir=TB ranksep=0.02]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		6 [label="\"\"\"Manages conversational turns between virtual teammates.\"\"\"\ldef __init__(self, target_score: float=DEFAULT_TARGET_SCORE, max_turns: int...\ldef _policy(self, state: Dict[str, Any]) ->Tuple[str | None, str]:...\ldef run(self, task_description: str) ->Tuple[Dict[str, Any], list[Dict[str,..." fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
		subgraph cluster0__init__ {
			graph [compound=True fontname="DejaVu Sans Mono" label=__init__ pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			8 [label="self.target_score = target_score\lself.max_turns = max_turns\lself.roles = discover_roles()" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			8 -> 9 [label=calls style=dashed]
			subgraph cluster_8 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				9 [label=discover_roles color="#E552FF" shape=tab style=filled]
			}
		}
		subgraph cluster0_policy {
			graph [compound=True fontname="DejaVu Sans Mono" label=_policy pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			12 [label="\"\"\"Deterministic policy function.\l\l        Returns (next_role_name | None, reason)\l        \"\"\"" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			13 [label="if 'draft' not in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			14 [label="return 'Writer', 'Need initial draft'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			13 -> 14 [label="'draft' not in state" color=green]
			15 [label="if 'output_guardian_pass' in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			17 [label="if not state['output_guardian_pass'] and state.get('_last_role'" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			19 [label="if state.get('format_fixed'):" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			19 -> 21 [label=calls style=dashed]
			subgraph cluster_19 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				21 [label="state.get" color="#E552FF" shape=tab style=filled]
			}
			22 [label="return 'Critic', 'Proceed with quality evaluation despite format issues'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			19 -> 22 [label="state.get('format_fixed')" color=green]
			24 [label="return 'Editor', 'Fix output format based on guardian feedback'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			19 -> 24 [label="(not state.get('format_fixed'))" color=red]
			17 -> 19 [label="not state['output_guardian_pass'] and state.get('_last_role'
    ) == 'OutputGuardian'" color=red]
			18 [label="if not state.get('output_guardian_pass', False) and state.get('_last_role'" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			27 [label="return 'OutputGuardian', 'Check output format compliance'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			18 -> 27 [label="not state.get('output_guardian_pass', False) and state.get('_last_role'
    ) != 'OutputGuardian'" color=red]
			28 [label="if 'critic_score' not in state:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			30 [label="return 'Critic', 'First quality assessment'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			28 -> 30 [label="'critic_score' not in state" color=green]
			31 [label="if state.get('critic_score', 0) < self.target_score:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			31 -> 33 [label=calls style=dashed]
			subgraph cluster_31 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				33 [label="state.get" color="#E552FF" shape=tab style=filled]
			}
			34 [label="if state.get('_last_role') == 'Editor':" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			34 -> 36 [label=calls style=dashed]
			subgraph cluster_34 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				36 [label="state.get" color="#E552FF" shape=tab style=filled]
			}
			37 [label="return 'Critic', 'Re-evaluate after editing'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			34 -> 37 [label="state.get('_last_role') == 'Editor'" color=green]
			38 [label="return 'Editor', 'Improve draft based on critique'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			34 -> 38 [label="(state.get('_last_role') != 'Editor')" color=red]
			31 -> 34 [label="state.get('critic_score', 0) < self.target_score" color=green]
			35 [label="token_pct = state.get('token_saving_pct')" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			35 -> 41 [label=calls style=dashed]
			subgraph cluster_35 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				41 [label="state.get" color="#E552FF" shape=tab style=filled]
			}
			42 [label="if token_pct is None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			43 [label="return 'TokenOptimizer', 'Attempt token optimisation'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			42 -> 43 [label="token_pct is None" color=green]
			44 [label="if 'tokens_saved' in state and state.get('log', '').find('exceeds') != -1:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			46 [label="return None, 'All criteria met - skipping token optimization due to excessive savings'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			44 -> 46 [label="'tokens_saved' in state and state.get('log', '').find('exceeds') != -1" color=green]
			47 [label="if token_pct > 15:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			49 [label="return 'TokenOptimizer', 'Attempt token optimisation'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			47 -> 49 [label="token_pct > 15" color=green]
			50 [label="return None, 'All criteria met'" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			47 -> 50 [label="(token_pct <= 15)" color=red]
			44 -> 47 [label="(not ('tokens_saved' in state and state.get('log', '').find('exceeds') != -1))" color=red]
			42 -> 44 [label="(token_pct is not None)" color=red]
			35 -> 42 [label="" color=black]
			31 -> 35 [label="(state.get('critic_score', 0) >= self.target_score)" color=red]
			28 -> 31 [label="('critic_score' in state)" color=red]
			18 -> 28 [label="(not (not state.get('output_guardian_pass', False) and state.get(
    '_last_role') != 'OutputGuardian'))" color=red]
			17 -> 18 [label="(not (not state['output_guardian_pass'] and state.get('_last_role') ==
    'OutputGuardian'))" color=red]
			15 -> 17 [label="'output_guardian_pass' in state" color=green]
			15 -> 18 [label="('output_guardian_pass' not in state)" color=red]
			13 -> 15 [label="('draft' in state)" color=red]
			12 -> 13 [label="" color=black]
		}
		subgraph cluster0run {
			graph [compound=True fontname="DejaVu Sans Mono" label=run pack=False rankdir=TB ranksep=0.02]
			node [fontname="DejaVu Sans Mono"]
			edge [fontname="DejaVu Sans Mono"]
			55 [label="\"\"\"Execute orchestration loop.\l\l        Returns final *state* and *history* list.\l        \"\"\"\lstate: Dict[str, Any] = {'task': task_description, 'target_score': self.\l    target_score}\lhistory: list[Dict[str, Any]] = []" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			56 [label="for turn in range(1, self.max_turns + 1):" fillcolor="#FFBE52" shape=hexagon style="filled,solid"]
			56 -> 57 [label=calls style=dashed]
			subgraph cluster_56 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				57 [label=range color="#E552FF" shape=tab style=filled]
			}
			58 [label="role_name, reason = self._policy(state)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			58 -> 60 [label=calls style=dashed]
			subgraph cluster_58 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				60 [label="self._policy" color="#E552FF" shape=tab style=filled]
			}
			61 [label="if role_name is None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			62 [label="state['termination_reason'] = reason\lbreak" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			59 [label="return state, history" fillcolor="#98fb98" shape=parallelogram style="filled,solid"]
			62 -> 59 [label="" color=black]
			61 -> 62 [label="role_name is None" color=green]
			63 [label="role = self.roles.get(role_name)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			63 -> 65 [label=calls style=dashed]
			subgraph cluster_63 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				65 [label="self.roles.get" color="#E552FF" shape=tab style=filled]
			}
			66 [label="if role is None:" fillcolor="#FF6752" shape=diamond style="filled,solid"]
			67 [label="raise KeyError(\l    f\"Role '{role_name}' not found. Ensure it exists in roles package.\")" fillcolor="#98fb98" shape=house style="filled,solid"]
			66 -> 67 [label="role is None" color=green]
			68 [label="output = role.eval(state)\lstate.update(output.to_state_update())\lstate['_last_role'] = role_name\lhistory.append({'turn': turn, 'role': role_name, 'reason': reason,\l    'output_log': state.get('log', '')})\lstate.pop('log', None)" fillcolor="#FFFB81" shape=rectangle style="filled,solid"]
			68 -> 70 [label=calls style=dashed]
			68 -> 71 [label=calls style=dashed]
			68 -> 73 [label=calls style=dashed]
			68 -> 75 [label=calls style=dashed]
			subgraph cluster_68 {
				graph [color=purple compound=true fontname="DejaVu Sans Mono" label="" rankdir=TB ranksep=0.02 shape=tab style=filled]
				node [fontname="DejaVu Sans Mono"]
				edge [fontname="DejaVu Sans Mono"]
				70 [label="role.eval" color="#E552FF" shape=tab style=filled]
				71 [label="state.update" color="#E552FF" shape=tab style=filled]
				72 [label="output.to_state_update" color="#E552FF" shape=tab style=filled]
				71 -> 72 [color=black]
				73 [label="history.append" color="#E552FF" shape=tab style=filled]
				74 [label="state.get" color="#E552FF" shape=tab style=filled]
				73 -> 74 [color=black]
				75 [label="state.pop" color="#E552FF" shape=tab style=filled]
			}
			68 -> 56 [label="" color=black]
			66 -> 68 [label="(role is not None)" color=red]
			63 -> 66 [label="" color=black]
			61 -> 63 [label="(role_name is not None)" color=red]
			58 -> 61 [label="" color=black]
			56 -> 58 [label="range(1, self.max_turns + 1)" color=green]
			56 -> 59 [label="" color=green]
			55 -> 56 [label="" color=black]
		}
	}
	subgraph cluster_KEY {
		graph [fontname="DejaVu Sans Mono" label=KEY]
		node [fontname="DejaVu Sans Mono"]
		edge [fontname="DejaVu Sans Mono"]
		input [fillcolor="#afeeee" shape=parallelogram style=filled]
		default [fillcolor="#FFFB81" shape=rectangle style=filled]
		if [fillcolor="#FF6752" shape=diamond style=filled]
		for [fillcolor="#FFBE52" shape=hexagon style=filled]
		while [fillcolor="#FFBE52" shape=hexagon style=filled]
		call [fillcolor="#E552FF" shape=tab style=filled]
		return [fillcolor="#98fb98" shape=parallelogram style=filled]
		try [fillcolor=orange shape=Mdiamond style=filled]
		raise [fillcolor="#98fb98" shape=house style=filled]
		if -> input [style=invis]
		input -> call [style=invis]
		for -> return [style=invis]
		return -> default [style=invis]
		try -> raise [style=invis]
	}
}
