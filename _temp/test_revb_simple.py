#!/usr/bin/env python3
"""Simple test of Rev-B features without the problematic async pattern"""

import sys
sys.path.append('.')

from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from pydantic import BaseModel, ConfigDict, Field

load_dotenv()

# Test the PROMPTS singleton pattern
class _Prompt(BaseModel):
    micro: str
    sys: str
    usr: str

class PROMPTS:
    gen_prompt = _Prompt(
        micro="oner: craft-initial-prompt-terse",
        sys="You are a prompt engineer. Output a terse prompt only.",
        usr="{problem_desc}",
    )

# Test basic LLM call
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

def call_llm(micro: str, sys_prompt: str, user_prompt: str) -> str:
    """LLM wrapper; micro is readability only."""
    print(f"[{micro}] Calling LLM...")
    tmpl = ChatPromptTemplate.from_messages([("system", sys_prompt), ("user", "{u}")])
    return llm.invoke(tmpl.format_prompt(u=user_prompt).to_messages()).content

# Test improved models
class ScenarioDoc(BaseModel):
    scenarios: list[str]
    
    def _display(self) -> str:
        return "## Scenarios\n" + "\n".join(f"- {s}" for s in self.scenarios)

class ReqDoc(BaseModel):
    requirements: list[str]
    
    def _display(self) -> str:
        return "## Requirements\n" + "\n".join(f"- {r}" for r in self.requirements)

if __name__ == "__main__":
    print("=== Testing Rev-B Improvements ===")
    
    # Test PROMPTS singleton
    print("✅ PROMPTS singleton working")
    print(f"   Micro: {PROMPTS.gen_prompt.micro}")
    
    # Test LLM call with new signature
    result = call_llm(
        PROMPTS.gen_prompt.micro,
        PROMPTS.gen_prompt.sys,
        PROMPTS.gen_prompt.usr.format(problem_desc="Test legal contract analysis")
    )
    print(f"✅ LLM call successful: {result[:100]}...")
    
    # Test enhanced display methods
    scen_doc = ScenarioDoc(scenarios=["breach of contract", "liability claim", "termination"])
    req_doc = ReqDoc(requirements=["accuracy>90%", "explain clearly", "under 500 words"])
    
    print("✅ Enhanced display methods:")
    print(scen_doc._display())
    print(req_doc._display())
    
    print("\n=== Rev-B Features Validated ===")