<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 13.1.0 (20250701.0955)
 -->
<!-- Title: cluster0/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/test_revb_simple_cfg.dot Pages: 1 -->
<svg width="4143pt" height="1046pt"
 viewBox="0.00 0.00 4143.00 1046.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1042.25)">
<title>cluster0/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/test_revb_simple_cfg.dot</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-1042.25 4139,-1042.25 4139,4 -4,4"/>
<text xml:space="preserve" text-anchor="middle" x="2067.5" y="-5.7" font-family="DejaVu Sans Mono" font-size="14.00">/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/test_revb_simple_cfg.dot</text>
<g id="clust1" class="cluster">
<title>cluster_1</title>
<polygon fill="purple" stroke="purple" points="8,-450.25 8,-502.25 356,-502.25 356,-450.25 8,-450.25"/>
</g>
<g id="clust2" class="cluster">
<title>cluster_34</title>
<polygon fill="purple" stroke="purple" points="112,-31 112,-136 960,-136 960,-31 112,-31"/>
</g>
<g id="clust3" class="cluster">
<title>cluster0_Prompt</title>
<polygon fill="none" stroke="black" points="576,-751.25 576,-843.25 664,-843.25 664,-751.25 576,-751.25"/>
<text xml:space="preserve" text-anchor="middle" x="620" y="-825.95" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<g id="clust4" class="cluster">
<title>cluster0PROMPTS</title>
<polygon fill="none" stroke="black" points="713,-442.25 713,-843.25 1151,-843.25 1151,-442.25 713,-442.25"/>
<text xml:space="preserve" text-anchor="middle" x="932" y="-825.95" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_8</title>
<polygon fill="purple" stroke="purple" points="890,-450.25 890,-502.25 974,-502.25 974,-450.25 890,-450.25"/>
</g>
<g id="clust6" class="cluster">
<title>cluster0ScenarioDoc</title>
<polygon fill="none" stroke="black" points="1159,-716.75 1159,-900.75 1815,-900.75 1815,-716.75 1159,-716.75"/>
<text xml:space="preserve" text-anchor="middle" x="1487" y="-883.45" font-family="DejaVu Sans Mono" font-size="14.00">ScenarioDoc</text>
</g>
<g id="clust7" class="cluster">
<title>cluster0_display</title>
<polygon fill="none" stroke="black" points="1167,-724.75 1167,-869.75 1633,-869.75 1633,-724.75 1167,-724.75"/>
<text xml:space="preserve" text-anchor="middle" x="1400" y="-852.45" font-family="DejaVu Sans Mono" font-size="14.00">_display</text>
</g>
<g id="clust8" class="cluster">
<title>cluster0ReqDoc</title>
<polygon fill="none" stroke="black" points="1823,-716.75 1823,-900.75 2515,-900.75 2515,-716.75 1823,-716.75"/>
<text xml:space="preserve" text-anchor="middle" x="2169" y="-883.45" font-family="DejaVu Sans Mono" font-size="14.00">ReqDoc</text>
</g>
<g id="clust9" class="cluster">
<title>cluster1_display</title>
<polygon fill="none" stroke="black" points="1831,-724.75 1831,-869.75 2333,-869.75 2333,-724.75 1831,-724.75"/>
<text xml:space="preserve" text-anchor="middle" x="2082" y="-852.45" font-family="DejaVu Sans Mono" font-size="14.00">_display</text>
</g>
<g id="clust10" class="cluster">
<title>cluster0call_llm</title>
<polygon fill="none" stroke="black" points="2523,-442.25 2523,-850.75 3811,-850.75 3811,-442.25 2523,-442.25"/>
<text xml:space="preserve" text-anchor="middle" x="3167" y="-833.45" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<g id="clust11" class="cluster">
<title>cluster_13</title>
<polygon fill="purple" stroke="purple" points="2531,-450.25 2531,-502.25 2865,-502.25 2865,-450.25 2531,-450.25"/>
</g>
<g id="clust12" class="cluster">
<title>cluster_KEY</title>
<polygon fill="none" stroke="black" points="3819,-263.12 3819,-834.75 4127,-834.75 4127,-263.12 3819,-263.12"/>
<text xml:space="preserve" text-anchor="middle" x="3973" y="-817.45" font-family="DejaVu Sans Mono" font-size="14.00">KEY</text>
</g>
<!-- 1 -->
<g id="node1" class="node">
<title>1</title>
<polygon fill="#fffb81" stroke="black" points="472.88,-1038.25 21.12,-1038.25 21.12,-533.25 472.88,-533.25 472.88,-1038.25"/>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-1020.95" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Simple test of Rev&#45;B features without the problematic async pattern&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-1005.95" font-family="DejaVu Sans Mono" font-size="14.00">import sys</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-990.95" font-family="DejaVu Sans Mono" font-size="14.00">sys.path.append(&#39;.&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-975.95" font-family="DejaVu Sans Mono" font-size="14.00">from dotenv import load_dotenv</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-960.95" font-family="DejaVu Sans Mono" font-size="14.00">from langchain_openai import ChatOpenAI</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-945.95" font-family="DejaVu Sans Mono" font-size="14.00">from langchain.prompts import ChatPromptTemplate</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-930.95" font-family="DejaVu Sans Mono" font-size="14.00">from pydantic import BaseModel, ConfigDict, Field</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-915.95" font-family="DejaVu Sans Mono" font-size="14.00">load_dotenv()</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-900.95" font-family="DejaVu Sans Mono" font-size="14.00">class _Prompt(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-885.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;micro: str</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-870.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;sys: str</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-855.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;usr: str</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-840.95" font-family="DejaVu Sans Mono" font-size="14.00">class PROMPTS:</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-825.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;gen_prompt = _Prompt(micro=&#39;oner: craft&#45;initial&#45;prompt&#45;terse&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-810.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;You are a prompt engineer. Output a terse prompt only.&#39;, usr=</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-795.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;{problem_desc}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-780.95" font-family="DejaVu Sans Mono" font-size="14.00">llm = ChatOpenAI(model=&#39;gpt&#45;4o&#45;mini&#39;, temperature=0)</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-765.95" font-family="DejaVu Sans Mono" font-size="14.00">def call_llm(micro: str, sys_prompt: str, user_prompt: str) &#45;&gt;str:...</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-750.95" font-family="DejaVu Sans Mono" font-size="14.00">class ScenarioDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-735.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;scenarios: list[str]</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-704.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def _display(self) &#45;&gt;str:</text>
<text xml:space="preserve" text-anchor="middle" x="247" y="-689.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return &#39;## Scenarios</text>
<text xml:space="preserve" text-anchor="middle" x="247" y="-674.95" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-659.95" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {s}&#39; for s in self.scenarios)</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-644.95" font-family="DejaVu Sans Mono" font-size="14.00">class ReqDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-629.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;requirements: list[str]</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-598.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def _display(self) &#45;&gt;str:</text>
<text xml:space="preserve" text-anchor="middle" x="247" y="-583.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return &#39;## Requirements</text>
<text xml:space="preserve" text-anchor="middle" x="247" y="-568.95" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="start" x="29.12" y="-553.95" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {r}&#39; for r in self.</text>
<text xml:space="preserve" text-anchor="middle" x="247" y="-538.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;requirements)</text>
</g>
<!-- 2 -->
<g id="node2" class="node">
<title>2</title>
<polygon fill="#e552ff" stroke="#e552ff" points="131.5,-494.25 28.5,-494.25 28.5,-498.25 16.5,-498.25 16.5,-458.25 131.5,-458.25 131.5,-494.25"/>
<polyline fill="none" stroke="#e552ff" points="16.5,-494.25 28.5,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="74" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">sys.path.append</text>
</g>
<!-- 1&#45;&gt;2 -->
<g id="edge1" class="edge">
<title>1&#45;&gt;2</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M105.33,-532.94C99.27,-522.17 93.84,-512.51 89.26,-504.37"/>
<polygon fill="black" stroke="black" points="92.45,-502.91 84.5,-495.91 86.35,-506.34 92.45,-502.91"/>
<text xml:space="preserve" text-anchor="middle" x="112.61" y="-511.95" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 3 -->
<g id="node3" class="node">
<title>3</title>
<polygon fill="#e552ff" stroke="#e552ff" points="238.75,-494.25 161.25,-494.25 161.25,-498.25 149.25,-498.25 149.25,-458.25 238.75,-458.25 238.75,-494.25"/>
<polyline fill="none" stroke="#e552ff" points="149.25,-494.25 161.25,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="194" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">load_dotenv</text>
</g>
<!-- 1&#45;&gt;3 -->
<g id="edge2" class="edge">
<title>1&#45;&gt;3</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M203.6,-532.94C201.85,-522.78 200.27,-513.6 198.91,-505.76"/>
<polygon fill="black" stroke="black" points="202.39,-505.35 197.25,-496.09 195.5,-506.53 202.39,-505.35"/>
<text xml:space="preserve" text-anchor="middle" x="215.19" y="-511.95" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 11 -->
<g id="node4" class="node">
<title>11</title>
<polygon fill="#e552ff" stroke="#e552ff" points="347.5,-494.25 268.5,-494.25 268.5,-498.25 256.5,-498.25 256.5,-458.25 347.5,-458.25 347.5,-494.25"/>
<polyline fill="none" stroke="#e552ff" points="256.5,-494.25 268.5,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="302" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">ChatOpenAI</text>
</g>
<!-- 1&#45;&gt;11 -->
<g id="edge3" class="edge">
<title>1&#45;&gt;11</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M292.04,-532.94C293.86,-522.78 295.5,-513.6 296.9,-505.76"/>
<polygon fill="black" stroke="black" points="300.32,-506.54 298.63,-496.08 293.43,-505.31 300.32,-506.54"/>
<text xml:space="preserve" text-anchor="middle" x="308.69" y="-511.95" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 33 -->
<g id="node5" class="node">
<title>33</title>
<polygon fill="#ff6752" stroke="black" points="535,-494.25 365.19,-476.25 535,-458.25 704.81,-476.25 535,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="535" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">if __name__ == &#39;__main__&#39;:</text>
</g>
<!-- 1&#45;&gt;33 -->
<g id="edge19" class="edge">
<title>1&#45;&gt;33</title>
<path fill="none" stroke="black" d="M473.22,-543.22C488.56,-526.84 502.03,-512.46 512.44,-501.34"/>
<polygon fill="black" stroke="black" points="514.8,-503.94 519.08,-494.25 509.69,-499.16 514.8,-503.94"/>
</g>
<!-- 34 -->
<g id="node6" class="node">
<title>34</title>
<polygon fill="#fffb81" stroke="black" points="809.62,-411.25 260.38,-411.25 260.38,-167 809.62,-167 809.62,-411.25"/>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-393.95" font-family="DejaVu Sans Mono" font-size="14.00">print(&#39;=== Testing Rev&#45;B Improvements ===&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-378.95" font-family="DejaVu Sans Mono" font-size="14.00">print(&#39;✅ PROMPTS singleton working&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-360.2" font-family="DejaVu Sans Mono" font-size="14.00">print(f&#39; &#160;&#160;Micro: {PROMPTS.gen_prompt.micro}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-345.2" font-family="DejaVu Sans Mono" font-size="14.00">result = call_llm(PROMPTS.gen_prompt.micro, PROMPTS.gen_prompt.sys, PROMPTS</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-330.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;.gen_prompt.usr.format(problem_desc=&#39;Test legal contract analysis&#39;))</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-315.2" font-family="DejaVu Sans Mono" font-size="14.00">print(f&#39;✅ LLM call successful: {result[:100]}...&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-296.45" font-family="DejaVu Sans Mono" font-size="14.00">scen_doc = ScenarioDoc(scenarios=[&#39;breach of contract&#39;, &#39;liability claim&#39;,</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-281.45" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;termination&#39;])</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-266.45" font-family="DejaVu Sans Mono" font-size="14.00">req_doc = ReqDoc(requirements=[&#39;accuracy&gt;90%&#39;, &#39;explain clearly&#39;,</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-251.45" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;under 500 words&#39;])</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-236.45" font-family="DejaVu Sans Mono" font-size="14.00">print(&#39;✅ Enhanced display methods:&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-217.7" font-family="DejaVu Sans Mono" font-size="14.00">print(scen_doc._display())</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-202.7" font-family="DejaVu Sans Mono" font-size="14.00">print(req_doc._display())</text>
<text xml:space="preserve" text-anchor="start" x="268.38" y="-187.7" font-family="DejaVu Sans Mono" font-size="14.00">print(&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="middle" x="535" y="-172.7" font-family="DejaVu Sans Mono" font-size="14.00">=== Rev&#45;B Features Validated ===&quot;&quot;&quot;)</text>
</g>
<!-- 33&#45;&gt;34 -->
<g id="edge18" class="edge">
<title>33&#45;&gt;34</title>
<path fill="none" stroke="green" d="M535,-457.83C535,-448.6 535,-436.34 535,-422.62"/>
<polygon fill="green" stroke="green" points="538.5,-422.86 535,-412.86 531.5,-422.86 538.5,-422.86"/>
<text xml:space="preserve" text-anchor="middle" x="612.25" y="-420.95" font-family="DejaVu Sans Mono" font-size="14.00">__name__ == &#39;__main__&#39;</text>
</g>
<!-- 36 -->
<g id="node7" class="node">
<title>36</title>
<polygon fill="#e552ff" stroke="#e552ff" points="174,-128 132,-128 132,-132 120,-132 120,-92 174,-92 174,-128"/>
<polyline fill="none" stroke="#e552ff" points="120,-128 132,-128"/>
<text xml:space="preserve" text-anchor="middle" x="147" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 34&#45;&gt;36 -->
<g id="edge4" class="edge">
<title>34&#45;&gt;36</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M260.01,-175.65C233.64,-162.97 207.58,-149.67 183,-136 182.1,-135.5 181.19,-134.98 180.28,-134.45"/>
<polygon fill="black" stroke="black" points="182.2,-131.52 171.87,-129.17 178.48,-137.45 182.2,-131.52"/>
<text xml:space="preserve" text-anchor="middle" x="233.16" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 37 -->
<g id="node8" class="node">
<title>37</title>
<polygon fill="#e552ff" stroke="#e552ff" points="246,-128 204,-128 204,-132 192,-132 192,-92 246,-92 246,-128"/>
<polyline fill="none" stroke="#e552ff" points="192,-128 204,-128"/>
<text xml:space="preserve" text-anchor="middle" x="219" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 34&#45;&gt;37 -->
<g id="edge5" class="edge">
<title>34&#45;&gt;37</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M306.5,-166.68C288.94,-156.53 271.6,-146.23 255,-136 254.15,-135.48 253.29,-134.94 252.43,-134.39"/>
<polygon fill="black" stroke="black" points="254.71,-131.7 244.42,-129.14 250.87,-137.55 254.71,-131.7"/>
<text xml:space="preserve" text-anchor="middle" x="305.64" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 38 -->
<g id="node9" class="node">
<title>38</title>
<polygon fill="#e552ff" stroke="#e552ff" points="318,-128 276,-128 276,-132 264,-132 264,-92 318,-92 318,-128"/>
<polyline fill="none" stroke="#e552ff" points="264,-128 276,-128"/>
<text xml:space="preserve" text-anchor="middle" x="291" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 34&#45;&gt;38 -->
<g id="edge6" class="edge">
<title>34&#45;&gt;38</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M367.53,-166.56C351.39,-154.84 336.49,-144.02 324.21,-135.1"/>
<polygon fill="black" stroke="black" points="326.32,-132.31 316.17,-129.27 322.2,-137.98 326.32,-132.31"/>
<text xml:space="preserve" text-anchor="middle" x="369.94" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 39 -->
<g id="node10" class="node">
<title>39</title>
<polygon fill="#e552ff" stroke="#e552ff" points="397.88,-128 348.12,-128 348.12,-132 336.12,-132 336.12,-92 397.88,-92 397.88,-128"/>
<polyline fill="none" stroke="#e552ff" points="336.12,-128 348.12,-128"/>
<text xml:space="preserve" text-anchor="middle" x="367" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 34&#45;&gt;39 -->
<g id="edge7" class="edge">
<title>34&#45;&gt;39</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M419.7,-166.56C409.05,-155.33 399.18,-144.93 390.93,-136.23"/>
<polygon fill="black" stroke="black" points="393.72,-134.08 384.3,-129.24 388.64,-138.9 393.72,-134.08"/>
<text xml:space="preserve" text-anchor="middle" x="425.56" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 41 -->
<g id="node11" class="node">
<title>41</title>
<polygon fill="#e552ff" stroke="#e552ff" points="470,-128 428,-128 428,-132 416,-132 416,-92 470,-92 470,-128"/>
<polyline fill="none" stroke="#e552ff" points="416,-128 428,-128"/>
<text xml:space="preserve" text-anchor="middle" x="443" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 34&#45;&gt;41 -->
<g id="edge8" class="edge">
<title>34&#45;&gt;41</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M471.86,-166.56C466.45,-156.15 461.41,-146.45 457.11,-138.16"/>
<polygon fill="black" stroke="black" points="460.32,-136.74 452.6,-129.48 454.1,-139.97 460.32,-136.74"/>
<text xml:space="preserve" text-anchor="middle" x="481.17" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 42 -->
<g id="node12" class="node">
<title>42</title>
<polygon fill="#e552ff" stroke="#e552ff" points="581.62,-128 500.38,-128 500.38,-132 488.38,-132 488.38,-92 581.62,-92 581.62,-128"/>
<polyline fill="none" stroke="#e552ff" points="488.38,-128 500.38,-128"/>
<text xml:space="preserve" text-anchor="middle" x="535" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">ScenarioDoc</text>
</g>
<!-- 34&#45;&gt;42 -->
<g id="edge9" class="edge">
<title>34&#45;&gt;42</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M535,-166.56C535,-156.73 535,-147.54 535,-139.57"/>
<polygon fill="black" stroke="black" points="538.5,-139.65 535,-129.65 531.5,-139.65 538.5,-139.65"/>
<text xml:space="preserve" text-anchor="middle" x="548.5" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 43 -->
<g id="node13" class="node">
<title>43</title>
<polygon fill="#e552ff" stroke="#e552ff" points="664.38,-128 611.62,-128 611.62,-132 599.62,-132 599.62,-92 664.38,-92 664.38,-128"/>
<polyline fill="none" stroke="#e552ff" points="599.62,-128 611.62,-128"/>
<text xml:space="preserve" text-anchor="middle" x="632" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">ReqDoc</text>
</g>
<!-- 34&#45;&gt;43 -->
<g id="edge10" class="edge">
<title>34&#45;&gt;43</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M601.57,-166.56C607.28,-156.15 612.59,-146.45 617.13,-138.16"/>
<polygon fill="black" stroke="black" points="620.15,-139.92 621.89,-129.47 614.01,-136.56 620.15,-139.92"/>
<text xml:space="preserve" text-anchor="middle" x="626.94" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 44 -->
<g id="node14" class="node">
<title>44</title>
<polygon fill="#e552ff" stroke="#e552ff" points="736,-128 694,-128 694,-132 682,-132 682,-92 736,-92 736,-128"/>
<polyline fill="none" stroke="#e552ff" points="682,-128 694,-128"/>
<text xml:space="preserve" text-anchor="middle" x="709" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 34&#45;&gt;44 -->
<g id="edge11" class="edge">
<title>34&#45;&gt;44</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M654.42,-166.56C665.45,-155.33 675.67,-144.93 684.21,-136.23"/>
<polygon fill="black" stroke="black" points="686.59,-138.81 691.1,-129.22 681.6,-133.9 686.59,-138.81"/>
<text xml:space="preserve" text-anchor="middle" x="689.21" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 45 -->
<g id="node15" class="node">
<title>45</title>
<polygon fill="#e552ff" stroke="#e552ff" points="808,-128 766,-128 766,-132 754,-132 754,-92 808,-92 808,-128"/>
<polyline fill="none" stroke="#e552ff" points="754,-128 766,-128"/>
<text xml:space="preserve" text-anchor="middle" x="781" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 34&#45;&gt;45 -->
<g id="edge12" class="edge">
<title>34&#45;&gt;45</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M703.84,-166.56C720.11,-154.84 735.14,-144.02 747.52,-135.1"/>
<polygon fill="black" stroke="black" points="749.56,-137.95 755.63,-129.27 745.47,-132.27 749.56,-137.95"/>
<text xml:space="preserve" text-anchor="middle" x="747.44" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 47 -->
<g id="node16" class="node">
<title>47</title>
<polygon fill="#e552ff" stroke="#e552ff" points="880,-128 838,-128 838,-132 826,-132 826,-92 880,-92 880,-128"/>
<polyline fill="none" stroke="#e552ff" points="826,-128 838,-128"/>
<text xml:space="preserve" text-anchor="middle" x="853" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 34&#45;&gt;47 -->
<g id="edge13" class="edge">
<title>34&#45;&gt;47</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M765.54,-166.54C783.09,-156.44 800.41,-146.19 817,-136 817.85,-135.48 818.71,-134.94 819.57,-134.4"/>
<polygon fill="black" stroke="black" points="821.13,-137.56 827.58,-129.15 817.3,-131.7 821.13,-137.56"/>
<text xml:space="preserve" text-anchor="middle" x="815.76" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 49 -->
<g id="node17" class="node">
<title>49</title>
<polygon fill="#e552ff" stroke="#e552ff" points="952,-128 910,-128 910,-132 898,-132 898,-92 952,-92 952,-128"/>
<polyline fill="none" stroke="#e552ff" points="898,-128 910,-128"/>
<text xml:space="preserve" text-anchor="middle" x="925" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 34&#45;&gt;49 -->
<g id="edge14" class="edge">
<title>34&#45;&gt;49</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M809.68,-176.64C836.84,-163.67 863.7,-150.03 889,-136 889.9,-135.5 890.81,-134.98 891.72,-134.45"/>
<polygon fill="black" stroke="black" points="893.52,-137.45 900.14,-129.17 889.81,-131.52 893.52,-137.45"/>
<text xml:space="preserve" text-anchor="middle" x="884.28" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 40 -->
<g id="node18" class="node">
<title>40</title>
<polygon fill="#e552ff" stroke="#e552ff" points="480,-75 266,-75 266,-79 254,-79 254,-39 480,-39 480,-75"/>
<polyline fill="none" stroke="#e552ff" points="254,-75 266,-75"/>
<text xml:space="preserve" text-anchor="middle" x="367" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS.gen_prompt.usr.format</text>
</g>
<!-- 39&#45;&gt;40 -->
<g id="edge15" class="edge">
<title>39&#45;&gt;40</title>
<path fill="none" stroke="black" d="M367,-91.73C367,-90.07 367,-88.36 367,-86.62"/>
<polygon fill="black" stroke="black" points="370.5,-86.76 367,-76.76 363.5,-86.76 370.5,-86.76"/>
</g>
<!-- 46 -->
<g id="node19" class="node">
<title>46</title>
<polygon fill="#e552ff" stroke="#e552ff" points="805.5,-75 690.5,-75 690.5,-79 678.5,-79 678.5,-39 805.5,-39 805.5,-75"/>
<polyline fill="none" stroke="#e552ff" points="678.5,-75 690.5,-75"/>
<text xml:space="preserve" text-anchor="middle" x="742" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">scen_doc._display</text>
</g>
<!-- 45&#45;&gt;46 -->
<g id="edge16" class="edge">
<title>45&#45;&gt;46</title>
<path fill="none" stroke="black" d="M767.8,-91.73C765.95,-89.32 764.03,-86.81 762.09,-84.28"/>
<polygon fill="black" stroke="black" points="764.96,-82.27 756.11,-76.45 759.4,-86.52 764.96,-82.27"/>
</g>
<!-- 48 -->
<g id="node20" class="node">
<title>48</title>
<polygon fill="#e552ff" stroke="#e552ff" points="942,-75 836,-75 836,-79 824,-79 824,-39 942,-39 942,-75"/>
<polyline fill="none" stroke="#e552ff" points="824,-75 836,-75"/>
<text xml:space="preserve" text-anchor="middle" x="883" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">req_doc._display</text>
</g>
<!-- 47&#45;&gt;48 -->
<g id="edge17" class="edge">
<title>47&#45;&gt;48</title>
<path fill="none" stroke="black" d="M863.16,-91.73C864.43,-89.57 865.75,-87.33 867.08,-85.06"/>
<polygon fill="black" stroke="black" points="870.03,-86.95 872.09,-76.55 864,-83.4 870.03,-86.95"/>
</g>
<!-- 5 -->
<g id="node21" class="node">
<title>5</title>
<polygon fill="#fffb81" stroke="black" points="656.12,-812.25 583.88,-812.25 583.88,-759.25 656.12,-759.25 656.12,-812.25"/>
<text xml:space="preserve" text-anchor="start" x="591.88" y="-794.95" font-family="DejaVu Sans Mono" font-size="14.00">micro: str</text>
<text xml:space="preserve" text-anchor="start" x="591.88" y="-779.95" font-family="DejaVu Sans Mono" font-size="14.00">sys: str</text>
<text xml:space="preserve" text-anchor="middle" x="620" y="-764.95" font-family="DejaVu Sans Mono" font-size="14.00">usr: str</text>
</g>
<!-- 8 -->
<g id="node22" class="node">
<title>8</title>
<polygon fill="#fffb81" stroke="black" points="1143.25,-812.25 720.75,-812.25 720.75,-759.25 1143.25,-759.25 1143.25,-812.25"/>
<text xml:space="preserve" text-anchor="start" x="728.75" y="-794.95" font-family="DejaVu Sans Mono" font-size="14.00">gen_prompt = _Prompt(micro=&#39;oner: craft&#45;initial&#45;prompt&#45;terse&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="728.75" y="-779.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;You are a prompt engineer. Output a terse prompt only.&#39;, usr=</text>
<text xml:space="preserve" text-anchor="middle" x="932" y="-764.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;{problem_desc}&#39;)</text>
</g>
<!-- 9 -->
<g id="node23" class="node">
<title>9</title>
<polygon fill="#e552ff" stroke="#e552ff" points="965.88,-494.25 910.12,-494.25 910.12,-498.25 898.12,-498.25 898.12,-458.25 965.88,-458.25 965.88,-494.25"/>
<polyline fill="none" stroke="#e552ff" points="898.12,-494.25 910.12,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="932" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<!-- 8&#45;&gt;9 -->
<g id="edge20" class="edge">
<title>8&#45;&gt;9</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M932,-758.8C932,-701.63 932,-565.22 932,-505.77"/>
<polygon fill="black" stroke="black" points="935.5,-506.14 932,-496.14 928.5,-506.14 935.5,-506.14"/>
<text xml:space="preserve" text-anchor="middle" x="945.5" y="-511.95" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 20 -->
<g id="node24" class="node">
<title>20</title>
<polygon fill="#fffb81" stroke="black" points="1807.25,-804.75 1642.75,-804.75 1642.75,-766.75 1807.25,-766.75 1807.25,-804.75"/>
<text xml:space="preserve" text-anchor="start" x="1650.75" y="-787.45" font-family="DejaVu Sans Mono" font-size="14.00">scenarios: list[str]</text>
<text xml:space="preserve" text-anchor="middle" x="1725" y="-772.45" font-family="DejaVu Sans Mono" font-size="14.00">def _display(self) &#45;&gt;str:...</text>
</g>
<!-- 22 -->
<g id="node25" class="node">
<title>22</title>
<polygon fill="#98fb98" stroke="black" points="1624.98,-838.75 1267.09,-838.75 1175.02,-732.75 1532.91,-732.75 1624.98,-838.75"/>
<text xml:space="preserve" text-anchor="middle" x="1400" y="-794.95" font-family="DejaVu Sans Mono" font-size="14.00">return &#39;## Scenarios</text>
<text xml:space="preserve" text-anchor="middle" x="1400" y="-779.95" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="middle" x="1400" y="-764.95" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {s}&#39; for s in self.scenarios)</text>
</g>
<!-- 27 -->
<g id="node26" class="node">
<title>27</title>
<polygon fill="#fffb81" stroke="black" points="2507.25,-804.75 2342.75,-804.75 2342.75,-766.75 2507.25,-766.75 2507.25,-804.75"/>
<text xml:space="preserve" text-anchor="start" x="2350.75" y="-787.45" font-family="DejaVu Sans Mono" font-size="14.00">requirements: list[str]</text>
<text xml:space="preserve" text-anchor="middle" x="2425" y="-772.45" font-family="DejaVu Sans Mono" font-size="14.00">def _display(self) &#45;&gt;str:...</text>
</g>
<!-- 29 -->
<g id="node27" class="node">
<title>29</title>
<polygon fill="#98fb98" stroke="black" points="2324.86,-838.75 1938.52,-838.75 1839.14,-732.75 2225.48,-732.75 2324.86,-838.75"/>
<text xml:space="preserve" text-anchor="middle" x="2082" y="-794.95" font-family="DejaVu Sans Mono" font-size="14.00">return &#39;## Requirements</text>
<text xml:space="preserve" text-anchor="middle" x="2082" y="-779.95" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="middle" x="2082" y="-764.95" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {r}&#39; for r in self.requirements)</text>
</g>
<!-- 13 -->
<g id="node28" class="node">
<title>13</title>
<polygon fill="#fffb81" stroke="black" points="3011.12,-819.75 2530.88,-819.75 2530.88,-751.75 3011.12,-751.75 3011.12,-819.75"/>
<text xml:space="preserve" text-anchor="start" x="2538.88" y="-802.45" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;LLM wrapper; micro is readability only.&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="2538.88" y="-787.45" font-family="DejaVu Sans Mono" font-size="14.00">print(f&#39;[{micro}] Calling LLM...&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="2538.88" y="-772.45" font-family="DejaVu Sans Mono" font-size="14.00">tmpl = ChatPromptTemplate.from_messages([(&#39;system&#39;, sys_prompt), (&#39;user&#39;,</text>
<text xml:space="preserve" text-anchor="middle" x="2771" y="-757.45" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;{u}&#39;)])</text>
</g>
<!-- 14 -->
<g id="node29" class="node">
<title>14</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2593,-494.25 2551,-494.25 2551,-498.25 2539,-498.25 2539,-458.25 2593,-458.25 2593,-494.25"/>
<polyline fill="none" stroke="#e552ff" points="2539,-494.25 2551,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="2566" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 13&#45;&gt;14 -->
<g id="edge21" class="edge">
<title>13&#45;&gt;14</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2748.67,-751.26C2707.65,-689.73 2621.1,-559.89 2583.76,-503.89"/>
<polygon fill="black" stroke="black" points="2586.89,-502.27 2578.43,-495.89 2581.06,-506.15 2586.89,-502.27"/>
<text xml:space="preserve" text-anchor="middle" x="2609.26" y="-511.95" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 15 -->
<g id="node30" class="node">
<title>15</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2856.75,-494.25 2623.25,-494.25 2623.25,-498.25 2611.25,-498.25 2611.25,-458.25 2856.75,-458.25 2856.75,-494.25"/>
<polyline fill="none" stroke="#e552ff" points="2611.25,-494.25 2623.25,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="2734" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">ChatPromptTemplate.from_messages</text>
</g>
<!-- 13&#45;&gt;15 -->
<g id="edge22" class="edge">
<title>13&#45;&gt;15</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2766.97,-751.26C2759.66,-690.49 2744.33,-563.11 2737.46,-506.01"/>
<polygon fill="black" stroke="black" points="2740.94,-505.64 2736.27,-496.13 2733.99,-506.48 2740.94,-505.64"/>
<text xml:space="preserve" text-anchor="middle" x="2752.87" y="-511.95" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 16 -->
<g id="node31" class="node">
<title>16</title>
<polygon fill="#98fb98" stroke="black" points="3803.08,-494.25 3064.83,-494.25 2874.92,-458.25 3613.17,-458.25 3803.08,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="3339" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">return llm.invoke(tmpl.format_prompt(u=user_prompt).to_messages()).content</text>
</g>
<!-- 13&#45;&gt;16 -->
<g id="edge23" class="edge">
<title>13&#45;&gt;16</title>
<path fill="none" stroke="black" d="M2832.87,-751.26C2949.22,-688.26 3197.83,-553.67 3296.95,-500.02"/>
<polygon fill="black" stroke="black" points="3298.44,-503.19 3305.56,-495.35 3295.1,-497.03 3298.44,-503.19"/>
</g>
<!-- input -->
<g id="node32" class="node">
<title>input</title>
<polygon fill="#afeeee" stroke="black" points="3913.12,-494.25 3844.53,-494.25 3826.88,-458.25 3895.47,-458.25 3913.12,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="3870" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">input</text>
</g>
<!-- call -->
<g id="node37" class="node">
<title>call</title>
<polygon fill="#e552ff" stroke="black" points="3897,-307.12 3855,-307.12 3855,-311.12 3843,-311.12 3843,-271.12 3897,-271.12 3897,-307.12"/>
<polyline fill="none" stroke="black" points="3843,-307.12 3855,-307.12"/>
<text xml:space="preserve" text-anchor="middle" x="3870" y="-283.32" font-family="DejaVu Sans Mono" font-size="14.00">call</text>
</g>
<!-- input&#45;&gt;call -->
<!-- default -->
<g id="node33" class="node">
<title>default</title>
<polygon fill="#fffb81" stroke="black" points="4008.25,-307.12 3951.75,-307.12 3951.75,-271.12 4008.25,-271.12 4008.25,-307.12"/>
<text xml:space="preserve" text-anchor="middle" x="3980" y="-283.32" font-family="DejaVu Sans Mono" font-size="14.00">default</text>
</g>
<!-- if -->
<g id="node34" class="node">
<title>if</title>
<polygon fill="#ff6752" stroke="black" points="3857,-803.75 3830,-785.75 3857,-767.75 3884,-785.75 3857,-803.75"/>
<text xml:space="preserve" text-anchor="middle" x="3857" y="-779.95" font-family="DejaVu Sans Mono" font-size="14.00">if</text>
</g>
<!-- if&#45;&gt;input -->
<!-- for -->
<g id="node35" class="node">
<title>for</title>
<polygon fill="#ffbe52" stroke="black" points="3956,-785.75 3942.5,-803.75 3915.5,-803.75 3902,-785.75 3915.5,-767.75 3942.5,-767.75 3956,-785.75"/>
<text xml:space="preserve" text-anchor="middle" x="3929" y="-779.95" font-family="DejaVu Sans Mono" font-size="14.00">for</text>
</g>
<!-- return -->
<g id="node38" class="node">
<title>return</title>
<polygon fill="#98fb98" stroke="black" points="4028.84,-494.25 3951.15,-494.25 3931.16,-458.25 4008.85,-458.25 4028.84,-494.25"/>
<text xml:space="preserve" text-anchor="middle" x="3980" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">return</text>
</g>
<!-- for&#45;&gt;return -->
<!-- while -->
<g id="node36" class="node">
<title>while</title>
<polygon fill="#ffbe52" stroke="black" points="4044.09,-785.75 4026.54,-803.75 3991.46,-803.75 3973.91,-785.75 3991.46,-767.75 4026.54,-767.75 4044.09,-785.75"/>
<text xml:space="preserve" text-anchor="middle" x="4009" y="-779.95" font-family="DejaVu Sans Mono" font-size="14.00">while</text>
</g>
<!-- return&#45;&gt;default -->
<!-- try -->
<g id="node39" class="node">
<title>try</title>
<polygon fill="orange" stroke="black" points="4091,-803.75 4062.51,-785.75 4091,-767.75 4119.49,-785.75 4091,-803.75"/>
<polyline fill="none" stroke="black" points="4072,-791.75 4072,-779.75"/>
<polyline fill="none" stroke="black" points="4081.5,-773.75 4100.5,-773.75"/>
<polyline fill="none" stroke="black" points="4110,-779.75 4110,-791.75"/>
<polyline fill="none" stroke="black" points="4100.5,-797.75 4081.5,-797.75"/>
<text xml:space="preserve" text-anchor="middle" x="4091" y="-779.95" font-family="DejaVu Sans Mono" font-size="14.00">try</text>
</g>
<!-- raise -->
<g id="node40" class="node">
<title>raise</title>
<polygon fill="#98fb98" stroke="black" points="4119.35,-481.81 4083,-494.25 4046.65,-481.81 4046.68,-461.69 4119.32,-461.69 4119.35,-481.81"/>
<text xml:space="preserve" text-anchor="middle" x="4083" y="-470.45" font-family="DejaVu Sans Mono" font-size="14.00">raise</text>
</g>
<!-- try&#45;&gt;raise -->
</g>
</svg>
