<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 13.1.0 (20250701.0955)
 -->
<!-- Title: cluster0/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/test_revb_minimal_cfg.dot Pages: 1 -->
<svg width="8271pt" height="1549pt"
 viewBox="0.00 0.00 8271.00 1549.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1545)">
<title>cluster0/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/test_revb_minimal_cfg.dot</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-1545 8267,-1545 8267,4 -4,4"/>
<text xml:space="preserve" text-anchor="middle" x="4131.5" y="-5.7" font-family="DejaVu Sans Mono" font-size="14.00">/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/test_revb_minimal_cfg.dot</text>
<g id="clust1" class="cluster">
<title>cluster_1</title>
<polygon fill="purple" stroke="purple" points="69,-473 69,-525 284,-525 284,-473 69,-473"/>
</g>
<g id="clust2" class="cluster">
<title>cluster_82</title>
<polygon fill="purple" stroke="purple" points="8,-31 8,-136 878,-136 878,-31 8,-31"/>
</g>
<g id="clust3" class="cluster">
<title>cluster0_Prompt</title>
<polygon fill="none" stroke="black" points="504,-1027.5 504,-1119.5 592,-1119.5 592,-1027.5 504,-1027.5"/>
<text xml:space="preserve" text-anchor="middle" x="548" y="-1102.2" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<g id="clust4" class="cluster">
<title>cluster0PROMPTS</title>
<polygon fill="none" stroke="black" points="641,-465 641,-1134.5 1089,-1134.5 1089,-465 641,-465"/>
<text xml:space="preserve" text-anchor="middle" x="865" y="-1117.2" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_7</title>
<polygon fill="purple" stroke="purple" points="780,-473 780,-525 950,-525 950,-473 780,-473"/>
</g>
<g id="clust6" class="cluster">
<title>cluster0ProblemDesc</title>
<polygon fill="none" stroke="black" points="1097,-465 1097,-1134.5 1447,-1134.5 1447,-465 1097,-465"/>
<text xml:space="preserve" text-anchor="middle" x="1272" y="-1117.2" font-family="DejaVu Sans Mono" font-size="14.00">ProblemDesc</text>
</g>
<g id="clust7" class="cluster">
<title>cluster_12</title>
<polygon fill="purple" stroke="purple" points="1225,-473 1225,-525 1319,-525 1319,-473 1225,-473"/>
</g>
<g id="clust8" class="cluster">
<title>cluster0MasterPrompt</title>
<polygon fill="none" stroke="black" points="1455,-457 1455,-1142 2489,-1142 2489,-457 1455,-457"/>
<text xml:space="preserve" text-anchor="middle" x="1972" y="-1124.7" font-family="DejaVu Sans Mono" font-size="14.00">MasterPrompt</text>
</g>
<g id="clust9" class="cluster">
<title>cluster_16</title>
<polygon fill="purple" stroke="purple" points="2304,-473 2304,-525 2374,-525 2374,-473 2304,-473"/>
</g>
<g id="clust10" class="cluster">
<title>cluster0current</title>
<polygon fill="none" stroke="black" points="1463,-1036 1463,-1111 2007,-1111 2007,-1036 1463,-1036"/>
<text xml:space="preserve" text-anchor="middle" x="1735" y="-1093.7" font-family="DejaVu Sans Mono" font-size="14.00">current</text>
</g>
<g id="clust11" class="cluster">
<title>cluster0add</title>
<polygon fill="none" stroke="black" points="2015,-465 2015,-1111 2187,-1111 2187,-465 2015,-465"/>
<text xml:space="preserve" text-anchor="middle" x="2101" y="-1093.7" font-family="DejaVu Sans Mono" font-size="14.00">add</text>
</g>
<g id="clust12" class="cluster">
<title>cluster_23</title>
<polygon fill="purple" stroke="purple" points="2023,-473 2023,-525 2179,-525 2179,-473 2023,-473"/>
</g>
<g id="clust13" class="cluster">
<title>cluster0PromptDoc</title>
<polygon fill="none" stroke="black" points="2497,-465 2497,-1142 3147,-1142 3147,-465 2497,-465"/>
<text xml:space="preserve" text-anchor="middle" x="2822" y="-1124.7" font-family="DejaVu Sans Mono" font-size="14.00">PromptDoc</text>
</g>
<g id="clust14" class="cluster">
<title>cluster_28</title>
<polygon fill="purple" stroke="purple" points="2841,-473 2841,-525 3007,-525 3007,-473 2841,-473"/>
</g>
<g id="clust15" class="cluster">
<title>cluster0_budget</title>
<polygon fill="none" stroke="black" points="2505,-473 2505,-1111 2713,-1111 2713,-473 2505,-473"/>
<text xml:space="preserve" text-anchor="middle" x="2609" y="-1093.7" font-family="DejaVu Sans Mono" font-size="14.00">_budget</text>
</g>
<g id="clust16" class="cluster">
<title>cluster0ScenarioDoc</title>
<polygon fill="none" stroke="black" points="3155,-993 3155,-1177 3811,-1177 3811,-993 3155,-993"/>
<text xml:space="preserve" text-anchor="middle" x="3483" y="-1159.7" font-family="DejaVu Sans Mono" font-size="14.00">ScenarioDoc</text>
</g>
<g id="clust17" class="cluster">
<title>cluster0_display</title>
<polygon fill="none" stroke="black" points="3163,-1001 3163,-1146 3629,-1146 3629,-1001 3163,-1001"/>
<text xml:space="preserve" text-anchor="middle" x="3396" y="-1128.7" font-family="DejaVu Sans Mono" font-size="14.00">_display</text>
</g>
<g id="clust18" class="cluster">
<title>cluster0ReqDoc</title>
<polygon fill="none" stroke="black" points="3819,-993 3819,-1177 4511,-1177 4511,-993 3819,-993"/>
<text xml:space="preserve" text-anchor="middle" x="4165" y="-1159.7" font-family="DejaVu Sans Mono" font-size="14.00">ReqDoc</text>
</g>
<g id="clust19" class="cluster">
<title>cluster1_display</title>
<polygon fill="none" stroke="black" points="3827,-1001 3827,-1146 4329,-1146 4329,-1001 3827,-1001"/>
<text xml:space="preserve" text-anchor="middle" x="4078" y="-1128.7" font-family="DejaVu Sans Mono" font-size="14.00">_display</text>
</g>
<g id="clust20" class="cluster">
<title>cluster0FinalBundle</title>
<polygon fill="none" stroke="black" points="4519,-1027.5 4519,-1119.5 4695,-1119.5 4695,-1027.5 4519,-1027.5"/>
<text xml:space="preserve" text-anchor="middle" x="4607" y="-1102.2" font-family="DejaVu Sans Mono" font-size="14.00">FinalBundle</text>
</g>
<g id="clust21" class="cluster">
<title>cluster0call_llm</title>
<polygon fill="none" stroke="black" points="4703,-465 4703,-1119.5 5919,-1119.5 5919,-465 4703,-465"/>
<text xml:space="preserve" text-anchor="middle" x="5311" y="-1102.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<g id="clust22" class="cluster">
<title>cluster_57</title>
<polygon fill="purple" stroke="purple" points="4711,-473 4711,-525 4973,-525 4973,-473 4711,-473"/>
</g>
<g id="clust23" class="cluster">
<title>cluster0run_revb_demo</title>
<polygon fill="none" stroke="black" points="5927,-257 5927,-1232 7939,-1232 7939,-257 5927,-257"/>
<text xml:space="preserve" text-anchor="middle" x="6933" y="-1214.7" font-family="DejaVu Sans Mono" font-size="14.00">run_revb_demo</text>
</g>
<g id="clust24" class="cluster">
<title>cluster_63</title>
<polygon fill="purple" stroke="purple" points="5935,-265 5935,-525 6991,-525 6991,-265 5935,-265"/>
</g>
<g id="clust25" class="cluster">
<title>cluster_KEY</title>
<polygon fill="none" stroke="black" points="7947,-265 7947,-1111 8255,-1111 8255,-265 7947,-265"/>
<text xml:space="preserve" text-anchor="middle" x="8101" y="-1093.7" font-family="DejaVu Sans Mono" font-size="14.00">KEY</text>
</g>
<!-- 1 -->
<g id="node1" class="node">
<title>1</title>
<polygon fill="#fffb81" stroke="black" points="453.62,-1541 6.38,-1541 6.38,-583 453.62,-583 453.62,-1541"/>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1523.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Minimal working version of Rev&#45;B with all improvements&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1508.7" font-family="DejaVu Sans Mono" font-size="14.00">from dotenv import load_dotenv</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1493.7" font-family="DejaVu Sans Mono" font-size="14.00">from langchain_openai import ChatOpenAI</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1478.7" font-family="DejaVu Sans Mono" font-size="14.00">from langchain.prompts import ChatPromptTemplate</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1463.7" font-family="DejaVu Sans Mono" font-size="14.00">from pydantic import BaseModel, ConfigDict, Field, field_validator</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1448.7" font-family="DejaVu Sans Mono" font-size="14.00">from typing import List</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1433.7" font-family="DejaVu Sans Mono" font-size="14.00">load_dotenv()</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1418.7" font-family="DejaVu Sans Mono" font-size="14.00">class _Prompt(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1403.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;micro: str</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1388.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;sys: str</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1373.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;usr: str</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1358.7" font-family="DejaVu Sans Mono" font-size="14.00">class PROMPTS:</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1343.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;gen_prompt = _Prompt(micro=&#39;oner: craft&#45;initial&#45;prompt&#45;terse&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1328.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;You are a prompt engineer. Output a terse prompt only.&#39;, usr=</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1313.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;{problem_desc}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1298.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;refine = _Prompt(micro=&#39;oner: refine&#45;prompt&#45;using&#45;weak&#45;areas&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1283.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;Improve prompt given weak areas.&#39;, usr=&#39;{payload}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1268.7" font-family="DejaVu Sans Mono" font-size="14.00">class ProblemDesc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1253.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;model_config = ConfigDict(strict=True, extra=&#39;forbid&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1238.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;problem_desc: str</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1223.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;target_model: str</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1208.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;token_limits: dict[str, int]</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1193.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;constraints: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1178.7" font-family="DejaVu Sans Mono" font-size="14.00">class MasterPrompt(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1163.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;versions: List[str] = Field(default_factory=list)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1132.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def current(self) &#45;&gt;str:</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1117.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return self.versions[&#45;1] if self.versions else &#39;&#39;</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1086.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def add(self, p: str):</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1071.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;self.versions.append(p)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1056.7" font-family="DejaVu Sans Mono" font-size="14.00">class PromptDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1041.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;model_config = ConfigDict(strict=True, validate_assignment=True)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1026.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;prompt: str = Field(..., min_length=10)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-1011.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;token_count: int</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-996.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;version: int = 0</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-965.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;@field_validator(&#39;token_count&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-950.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def _budget(cls, v):</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-935.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;assert v &gt; 0, &#39;empty prompt?&#39;</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-920.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return v</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-905.7" font-family="DejaVu Sans Mono" font-size="14.00">class ScenarioDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-890.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;scenarios: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-859.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def _display(self) &#45;&gt;str:</text>
<text xml:space="preserve" text-anchor="middle" x="230" y="-844.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return &#39;## Scenarios</text>
<text xml:space="preserve" text-anchor="middle" x="230" y="-829.7" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-814.7" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {s}&#39; for s in self.scenarios)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-799.7" font-family="DejaVu Sans Mono" font-size="14.00">class ReqDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-784.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;requirements: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-753.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def _display(self) &#45;&gt;str:</text>
<text xml:space="preserve" text-anchor="middle" x="230" y="-738.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return &#39;## Requirements</text>
<text xml:space="preserve" text-anchor="middle" x="230" y="-723.7" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-708.7" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {r}&#39; for r in self.</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-693.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;requirements)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-678.7" font-family="DejaVu Sans Mono" font-size="14.00">class FinalBundle(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-663.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;final_prompt: str</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-648.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;test_generation_doc: str</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-633.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;requirements_doc: str</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-618.7" font-family="DejaVu Sans Mono" font-size="14.00">llm = ChatOpenAI(model=&#39;gpt&#45;4o&#45;mini&#39;, temperature=0)</text>
<text xml:space="preserve" text-anchor="start" x="14.38" y="-603.7" font-family="DejaVu Sans Mono" font-size="14.00">def call_llm(micro: str, sys_prompt: str, user_prompt: str) &#45;&gt;str:...</text>
<text xml:space="preserve" text-anchor="middle" x="230" y="-588.7" font-family="DejaVu Sans Mono" font-size="14.00">def run_revb_demo(prob: ProblemDesc) &#45;&gt;FinalBundle:...</text>
</g>
<!-- 2 -->
<g id="node2" class="node">
<title>2</title>
<polygon fill="#e552ff" stroke="#e552ff" points="166.75,-517 89.25,-517 89.25,-521 77.25,-521 77.25,-481 166.75,-481 166.75,-517"/>
<polyline fill="none" stroke="#e552ff" points="77.25,-517 89.25,-517"/>
<text xml:space="preserve" text-anchor="middle" x="122" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">load_dotenv</text>
</g>
<!-- 1&#45;&gt;2 -->
<g id="edge1" class="edge">
<title>1&#45;&gt;2</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M137.94,-582.78C133.73,-560.93 130.15,-542.34 127.46,-528.34"/>
<polygon fill="black" stroke="black" points="130.94,-527.9 125.61,-518.75 124.06,-529.23 130.94,-527.9"/>
<text xml:space="preserve" text-anchor="middle" x="148.88" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 55 -->
<g id="node3" class="node">
<title>55</title>
<polygon fill="#e552ff" stroke="#e552ff" points="275.5,-517 196.5,-517 196.5,-521 184.5,-521 184.5,-481 275.5,-481 275.5,-517"/>
<polyline fill="none" stroke="#e552ff" points="184.5,-517 196.5,-517"/>
<text xml:space="preserve" text-anchor="middle" x="230" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">ChatOpenAI</text>
</g>
<!-- 1&#45;&gt;55 -->
<g id="edge2" class="edge">
<title>1&#45;&gt;55</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M230,-582.78C230,-561.15 230,-542.71 230,-528.77"/>
<polygon fill="black" stroke="black" points="233.5,-528.77 230,-518.77 226.5,-528.77 233.5,-528.77"/>
<text xml:space="preserve" text-anchor="middle" x="243.5" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 81 -->
<g id="node4" class="node">
<title>81</title>
<polygon fill="#ff6752" stroke="black" points="463,-517 293.19,-499 463,-481 632.81,-499 463,-517"/>
<text xml:space="preserve" text-anchor="middle" x="463" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">if __name__ == &#39;__main__&#39;:</text>
</g>
<!-- 1&#45;&gt;81 -->
<g id="edge16" class="edge">
<title>1&#45;&gt;81</title>
<path fill="none" stroke="black" d="M428.62,-582.78C438.01,-560.18 445.94,-541.07 451.82,-526.92"/>
<polygon fill="black" stroke="black" points="454.89,-528.64 455.5,-518.06 448.43,-525.96 454.89,-528.64"/>
</g>
<!-- 82 -->
<g id="node5" class="node">
<title>82</title>
<polygon fill="#fffb81" stroke="black" points="699.75,-415 226.25,-415 226.25,-167 699.75,-167 699.75,-415"/>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-397.7" font-family="DejaVu Sans Mono" font-size="14.00">prob = ProblemDesc(problem_desc=</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-382.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;Summarise legal contract clauses for risk.&#39;, target_model=</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-367.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;gpt&#45;4o&#45;mini&#39;, token_limits={&#39;hard&#39;: 4096}, constraints=[</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-352.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;no hallucinations&#39;, &#39;&lt;=2000 tokens&#39;])</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-337.7" font-family="DejaVu Sans Mono" font-size="14.00">print(&#39;=== Rev&#45;B Demo ===&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-322.7" font-family="DejaVu Sans Mono" font-size="14.00">bundle = run_revb_demo(prob)</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-307.7" font-family="DejaVu Sans Mono" font-size="14.00">print(&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-292.7" font-family="DejaVu Sans Mono" font-size="14.00">===== FINAL PROMPT =====&quot;&quot;&quot;)</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-277.7" font-family="DejaVu Sans Mono" font-size="14.00">print(bundle.final_prompt)</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-262.7" font-family="DejaVu Sans Mono" font-size="14.00">print(&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-247.7" font-family="DejaVu Sans Mono" font-size="14.00">=== Enhanced Display Methods ===&quot;&quot;&quot;)</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-232.7" font-family="DejaVu Sans Mono" font-size="14.00">scen_doc = ScenarioDoc(scenarios=[&#39;sue for breach&#39;, &#39;transfer risk&#39;,</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-217.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;terminate early&#39;])</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-202.7" font-family="DejaVu Sans Mono" font-size="14.00">req_doc = ReqDoc(requirements=[&#39;accuracy&gt;90%&#39;, &#39;explain liability&#39;, &#39;short&#39;])</text>
<text xml:space="preserve" text-anchor="start" x="234.25" y="-187.7" font-family="DejaVu Sans Mono" font-size="14.00">print(scen_doc._display())</text>
<text xml:space="preserve" text-anchor="middle" x="463" y="-172.7" font-family="DejaVu Sans Mono" font-size="14.00">print(req_doc._display())</text>
</g>
<!-- 81&#45;&gt;82 -->
<g id="edge15" class="edge">
<title>81&#45;&gt;82</title>
<path fill="none" stroke="green" d="M463,-480.79C463,-467.62 463,-448.08 463,-426.51"/>
<polygon fill="green" stroke="green" points="466.5,-426.58 463,-416.58 459.5,-426.58 466.5,-426.58"/>
<text xml:space="preserve" text-anchor="middle" x="540.25" y="-424.7" font-family="DejaVu Sans Mono" font-size="14.00">__name__ == &#39;__main__&#39;</text>
</g>
<!-- 84 -->
<g id="node6" class="node">
<title>84</title>
<polygon fill="#e552ff" stroke="#e552ff" points="113.88,-128 28.12,-128 28.12,-132 16.12,-132 16.12,-92 113.88,-92 113.88,-128"/>
<polyline fill="none" stroke="#e552ff" points="16.12,-128 28.12,-128"/>
<text xml:space="preserve" text-anchor="middle" x="65" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">ProblemDesc</text>
</g>
<!-- 82&#45;&gt;84 -->
<g id="edge3" class="edge">
<title>82&#45;&gt;84</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M226.02,-183.42C183.71,-164.39 143.77,-146.43 114.26,-133.15"/>
<polygon fill="black" stroke="black" points="115.81,-130.01 105.25,-129.1 112.94,-136.4 115.81,-130.01"/>
<text xml:space="preserve" text-anchor="middle" x="174.68" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 85 -->
<g id="node7" class="node">
<title>85</title>
<polygon fill="#e552ff" stroke="#e552ff" points="186,-128 144,-128 144,-132 132,-132 132,-92 186,-92 186,-128"/>
<polyline fill="none" stroke="#e552ff" points="132,-128 144,-128"/>
<text xml:space="preserve" text-anchor="middle" x="159" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 82&#45;&gt;85 -->
<g id="edge4" class="edge">
<title>82&#45;&gt;85</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M244.5,-166.52C227.65,-156.37 210.98,-146.12 195,-136 194.3,-135.56 193.6,-135.11 192.89,-134.65"/>
<polygon fill="black" stroke="black" points="195.13,-131.94 184.86,-129.33 191.26,-137.77 195.13,-131.94"/>
<text xml:space="preserve" text-anchor="middle" x="244.21" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 86 -->
<g id="node8" class="node">
<title>86</title>
<polygon fill="#e552ff" stroke="#e552ff" points="315.62,-128 216.38,-128 216.38,-132 204.38,-132 204.38,-92 315.62,-92 315.62,-128"/>
<polyline fill="none" stroke="#e552ff" points="204.38,-128 216.38,-128"/>
<text xml:space="preserve" text-anchor="middle" x="260" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">run_revb_demo</text>
</g>
<!-- 82&#45;&gt;86 -->
<g id="edge5" class="edge">
<title>82&#45;&gt;86</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M323.12,-166.66C310.18,-155.25 298.22,-144.7 288.27,-135.93"/>
<polygon fill="black" stroke="black" points="290.68,-133.38 280.86,-129.4 286.05,-138.63 290.68,-133.38"/>
<text xml:space="preserve" text-anchor="middle" x="322.56" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 87 -->
<g id="node9" class="node">
<title>87</title>
<polygon fill="#e552ff" stroke="#e552ff" points="388,-128 346,-128 346,-132 334,-132 334,-92 388,-92 388,-128"/>
<polyline fill="none" stroke="#e552ff" points="334,-128 346,-128"/>
<text xml:space="preserve" text-anchor="middle" x="361" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 82&#45;&gt;87 -->
<g id="edge6" class="edge">
<title>82&#45;&gt;87</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M392.71,-166.66C386.7,-156.1 381.1,-146.27 376.33,-137.91"/>
<polygon fill="black" stroke="black" points="379.52,-136.42 371.52,-129.47 373.43,-139.89 379.52,-136.42"/>
<text xml:space="preserve" text-anchor="middle" x="399.15" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 88 -->
<g id="node10" class="node">
<title>88</title>
<polygon fill="#e552ff" stroke="#e552ff" points="460,-128 418,-128 418,-132 406,-132 406,-92 460,-92 460,-128"/>
<polyline fill="none" stroke="#e552ff" points="406,-128 418,-128"/>
<text xml:space="preserve" text-anchor="middle" x="433" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 82&#45;&gt;88 -->
<g id="edge7" class="edge">
<title>82&#45;&gt;88</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M442.33,-166.66C440.66,-156.68 439.1,-147.37 437.75,-139.32"/>
<polygon fill="black" stroke="black" points="441.23,-138.93 436.13,-129.65 434.33,-140.09 441.23,-138.93"/>
<text xml:space="preserve" text-anchor="middle" x="453.75" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 89 -->
<g id="node11" class="node">
<title>89</title>
<polygon fill="#e552ff" stroke="#e552ff" points="532,-128 490,-128 490,-132 478,-132 478,-92 532,-92 532,-128"/>
<polyline fill="none" stroke="#e552ff" points="478,-128 490,-128"/>
<text xml:space="preserve" text-anchor="middle" x="505" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 82&#45;&gt;89 -->
<g id="edge8" class="edge">
<title>82&#45;&gt;89</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M491.94,-166.66C494.28,-156.68 496.47,-147.37 498.36,-139.32"/>
<polygon fill="black" stroke="black" points="501.75,-140.16 500.63,-129.63 494.94,-138.56 501.75,-140.16"/>
<text xml:space="preserve" text-anchor="middle" x="510.47" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 90 -->
<g id="node12" class="node">
<title>90</title>
<polygon fill="#e552ff" stroke="#e552ff" points="643.62,-128 562.38,-128 562.38,-132 550.38,-132 550.38,-92 643.62,-92 643.62,-128"/>
<polyline fill="none" stroke="#e552ff" points="550.38,-128 562.38,-128"/>
<text xml:space="preserve" text-anchor="middle" x="597" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">ScenarioDoc</text>
</g>
<!-- 82&#45;&gt;90 -->
<g id="edge9" class="edge">
<title>82&#45;&gt;90</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M555.34,-166.66C563.42,-155.86 570.92,-145.84 577.27,-137.36"/>
<polygon fill="black" stroke="black" points="580.06,-139.47 583.25,-129.36 574.46,-135.27 580.06,-139.47"/>
<text xml:space="preserve" text-anchor="middle" x="584.87" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 91 -->
<g id="node13" class="node">
<title>91</title>
<polygon fill="#e552ff" stroke="#e552ff" points="726.38,-128 673.62,-128 673.62,-132 661.62,-132 661.62,-92 726.38,-92 726.38,-128"/>
<polyline fill="none" stroke="#e552ff" points="661.62,-128 673.62,-128"/>
<text xml:space="preserve" text-anchor="middle" x="694" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">ReqDoc</text>
</g>
<!-- 82&#45;&gt;91 -->
<g id="edge10" class="edge">
<title>82&#45;&gt;91</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M622.18,-166.66C637.2,-155.02 651.06,-144.27 662.52,-135.39"/>
<polygon fill="black" stroke="black" points="664.59,-138.21 670.36,-129.32 660.31,-132.68 664.59,-138.21"/>
<text xml:space="preserve" text-anchor="middle" x="663.31" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 92 -->
<g id="node14" class="node">
<title>92</title>
<polygon fill="#e552ff" stroke="#e552ff" points="798,-128 756,-128 756,-132 744,-132 744,-92 798,-92 798,-128"/>
<polyline fill="none" stroke="#e552ff" points="744,-128 756,-128"/>
<text xml:space="preserve" text-anchor="middle" x="771" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 82&#45;&gt;92 -->
<g id="edge11" class="edge">
<title>82&#45;&gt;92</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M684.82,-166.66C701.91,-156.47 718.8,-146.18 735,-136 735.85,-135.47 736.7,-134.93 737.56,-134.37"/>
<polygon fill="black" stroke="black" points="739.14,-137.53 745.54,-129.09 735.27,-131.69 739.14,-137.53"/>
<text xml:space="preserve" text-anchor="middle" x="734.22" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 94 -->
<g id="node15" class="node">
<title>94</title>
<polygon fill="#e552ff" stroke="#e552ff" points="870,-128 828,-128 828,-132 816,-132 816,-92 870,-92 870,-128"/>
<polyline fill="none" stroke="#e552ff" points="816,-128 828,-128"/>
<text xml:space="preserve" text-anchor="middle" x="843" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 82&#45;&gt;94 -->
<g id="edge12" class="edge">
<title>82&#45;&gt;94</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M699.96,-190.92C736.37,-173.7 773.1,-155.14 807,-136 807.9,-135.49 808.8,-134.97 809.71,-134.43"/>
<polygon fill="black" stroke="black" points="811.53,-137.42 818.11,-129.12 807.79,-131.51 811.53,-137.42"/>
<text xml:space="preserve" text-anchor="middle" x="802.76" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 93 -->
<g id="node16" class="node">
<title>93</title>
<polygon fill="#e552ff" stroke="#e552ff" points="733.5,-75 618.5,-75 618.5,-79 606.5,-79 606.5,-39 733.5,-39 733.5,-75"/>
<polyline fill="none" stroke="#e552ff" points="606.5,-75 618.5,-75"/>
<text xml:space="preserve" text-anchor="middle" x="670" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">scen_doc._display</text>
</g>
<!-- 92&#45;&gt;93 -->
<g id="edge13" class="edge">
<title>92&#45;&gt;93</title>
<path fill="none" stroke="black" d="M743.67,-95.2C734.74,-90.69 724.57,-85.55 714.7,-80.57"/>
<polygon fill="black" stroke="black" points="716.45,-77.53 705.94,-76.15 713.29,-83.78 716.45,-77.53"/>
</g>
<!-- 95 -->
<g id="node17" class="node">
<title>95</title>
<polygon fill="#e552ff" stroke="#e552ff" points="870,-75 764,-75 764,-79 752,-79 752,-39 870,-39 870,-75"/>
<polyline fill="none" stroke="#e552ff" points="752,-75 764,-75"/>
<text xml:space="preserve" text-anchor="middle" x="811" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">req_doc._display</text>
</g>
<!-- 94&#45;&gt;95 -->
<g id="edge14" class="edge">
<title>94&#45;&gt;95</title>
<path fill="none" stroke="black" d="M832.17,-91.73C830.76,-89.49 829.29,-87.15 827.82,-84.8"/>
<polygon fill="black" stroke="black" points="830.91,-83.14 822.63,-76.53 824.98,-86.86 830.91,-83.14"/>
</g>
<!-- 4 -->
<g id="node18" class="node">
<title>4</title>
<polygon fill="#fffb81" stroke="black" points="584.12,-1088.5 511.88,-1088.5 511.88,-1035.5 584.12,-1035.5 584.12,-1088.5"/>
<text xml:space="preserve" text-anchor="start" x="519.88" y="-1071.2" font-family="DejaVu Sans Mono" font-size="14.00">micro: str</text>
<text xml:space="preserve" text-anchor="start" x="519.88" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">sys: str</text>
<text xml:space="preserve" text-anchor="middle" x="548" y="-1041.2" font-family="DejaVu Sans Mono" font-size="14.00">usr: str</text>
</g>
<!-- 7 -->
<g id="node19" class="node">
<title>7</title>
<polygon fill="#fffb81" stroke="black" points="1081.12,-1103.5 648.88,-1103.5 648.88,-1020.5 1081.12,-1020.5 1081.12,-1103.5"/>
<text xml:space="preserve" text-anchor="start" x="656.88" y="-1086.2" font-family="DejaVu Sans Mono" font-size="14.00">gen_prompt = _Prompt(micro=&#39;oner: craft&#45;initial&#45;prompt&#45;terse&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="656.88" y="-1071.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;You are a prompt engineer. Output a terse prompt only.&#39;, usr=</text>
<text xml:space="preserve" text-anchor="start" x="656.88" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;{problem_desc}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="656.88" y="-1041.2" font-family="DejaVu Sans Mono" font-size="14.00">refine = _Prompt(micro=&#39;oner: refine&#45;prompt&#45;using&#45;weak&#45;areas&#39;, sys=</text>
<text xml:space="preserve" text-anchor="middle" x="865" y="-1026.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;Improve prompt given weak areas.&#39;, usr=&#39;{payload}&#39;)</text>
</g>
<!-- 8 -->
<g id="node20" class="node">
<title>8</title>
<polygon fill="#e552ff" stroke="#e552ff" points="855.88,-517 800.12,-517 800.12,-521 788.12,-521 788.12,-481 855.88,-481 855.88,-517"/>
<polyline fill="none" stroke="#e552ff" points="788.12,-517 800.12,-517"/>
<text xml:space="preserve" text-anchor="middle" x="822" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<!-- 7&#45;&gt;8 -->
<g id="edge17" class="edge">
<title>7&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M861.87,-1020.2C853.6,-912.22 831.27,-620.91 824.19,-528.52"/>
<polygon fill="black" stroke="black" points="827.7,-528.53 823.44,-518.82 820.72,-529.06 827.7,-528.53"/>
<text xml:space="preserve" text-anchor="middle" x="840.83" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 9 -->
<g id="node21" class="node">
<title>9</title>
<polygon fill="#e552ff" stroke="#e552ff" points="941.88,-517 886.12,-517 886.12,-521 874.12,-521 874.12,-481 941.88,-481 941.88,-517"/>
<polyline fill="none" stroke="#e552ff" points="874.12,-517 886.12,-517"/>
<text xml:space="preserve" text-anchor="middle" x="908" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<!-- 7&#45;&gt;9 -->
<g id="edge18" class="edge">
<title>7&#45;&gt;9</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M868.13,-1020.2C876.4,-912.22 898.73,-620.91 905.81,-528.52"/>
<polygon fill="black" stroke="black" points="909.28,-529.06 906.56,-518.82 902.3,-528.53 909.28,-529.06"/>
<text xml:space="preserve" text-anchor="middle" x="916.17" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 12 -->
<g id="node22" class="node">
<title>12</title>
<polygon fill="#fffb81" stroke="black" points="1439,-1103.5 1105,-1103.5 1105,-1020.5 1439,-1020.5 1439,-1103.5"/>
<text xml:space="preserve" text-anchor="start" x="1113" y="-1086.2" font-family="DejaVu Sans Mono" font-size="14.00">model_config = ConfigDict(strict=True, extra=&#39;forbid&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="1113" y="-1071.2" font-family="DejaVu Sans Mono" font-size="14.00">problem_desc: str</text>
<text xml:space="preserve" text-anchor="start" x="1113" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">target_model: str</text>
<text xml:space="preserve" text-anchor="start" x="1113" y="-1041.2" font-family="DejaVu Sans Mono" font-size="14.00">token_limits: dict[str, int]</text>
<text xml:space="preserve" text-anchor="middle" x="1272" y="-1026.2" font-family="DejaVu Sans Mono" font-size="14.00">constraints: List[str]</text>
</g>
<!-- 13 -->
<g id="node23" class="node">
<title>13</title>
<polygon fill="#e552ff" stroke="#e552ff" points="1311.12,-517 1244.88,-517 1244.88,-521 1232.88,-521 1232.88,-481 1311.12,-481 1311.12,-517"/>
<polyline fill="none" stroke="#e552ff" points="1232.88,-517 1244.88,-517"/>
<text xml:space="preserve" text-anchor="middle" x="1272" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">ConfigDict</text>
</g>
<!-- 12&#45;&gt;13 -->
<g id="edge19" class="edge">
<title>12&#45;&gt;13</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1272,-1020.2C1272,-912.33 1272,-621.5 1272,-528.81"/>
<polygon fill="black" stroke="black" points="1275.5,-528.83 1272,-518.83 1268.5,-528.83 1275.5,-528.83"/>
<text xml:space="preserve" text-anchor="middle" x="1285.5" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 16 -->
<g id="node24" class="node">
<title>16</title>
<polygon fill="#fffb81" stroke="black" points="2481.25,-1088.5 2196.75,-1088.5 2196.75,-1035.5 2481.25,-1035.5 2481.25,-1088.5"/>
<text xml:space="preserve" text-anchor="start" x="2204.75" y="-1071.2" font-family="DejaVu Sans Mono" font-size="14.00">versions: List[str] = Field(default_factory=list)</text>
<text xml:space="preserve" text-anchor="start" x="2204.75" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">def current(self) &#45;&gt;str:...</text>
<text xml:space="preserve" text-anchor="middle" x="2339" y="-1041.2" font-family="DejaVu Sans Mono" font-size="14.00">def add(self, p: str):...</text>
</g>
<!-- 17 -->
<g id="node25" class="node">
<title>17</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2366,-517 2324,-517 2324,-521 2312,-521 2312,-481 2366,-481 2366,-517"/>
<polyline fill="none" stroke="#e552ff" points="2312,-517 2324,-517"/>
<text xml:space="preserve" text-anchor="middle" x="2339" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">Field</text>
</g>
<!-- 16&#45;&gt;17 -->
<g id="edge20" class="edge">
<title>16&#45;&gt;17</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2339,-1035.07C2339,-942.07 2339,-625.72 2339,-528.6"/>
<polygon fill="black" stroke="black" points="2342.5,-528.89 2339,-518.89 2335.5,-528.89 2342.5,-528.89"/>
<text xml:space="preserve" text-anchor="middle" x="2352.5" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 19 -->
<g id="node26" class="node">
<title>19</title>
<polygon fill="#98fb98" stroke="black" points="1998.96,-1080 1579.06,-1080 1471.04,-1044 1890.94,-1044 1998.96,-1080"/>
<text xml:space="preserve" text-anchor="middle" x="1735" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">return self.versions[&#45;1] if self.versions else &#39;&#39;</text>
</g>
<!-- 23 -->
<g id="node27" class="node">
<title>23</title>
<polygon fill="#fffb81" stroke="black" points="2179.12,-1080 2022.88,-1080 2022.88,-1044 2179.12,-1044 2179.12,-1080"/>
<text xml:space="preserve" text-anchor="middle" x="2101" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">self.versions.append(p)</text>
</g>
<!-- 24 -->
<g id="node28" class="node">
<title>24</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2170.88,-517 2043.12,-517 2043.12,-521 2031.12,-521 2031.12,-481 2170.88,-481 2170.88,-517"/>
<polyline fill="none" stroke="#e552ff" points="2031.12,-517 2043.12,-517"/>
<text xml:space="preserve" text-anchor="middle" x="2101" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">self.versions.append</text>
</g>
<!-- 23&#45;&gt;24 -->
<g id="edge21" class="edge">
<title>23&#45;&gt;24</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2100.18,-1043.79C2097.24,-981.04 2087.21,-758.6 2084,-575 2083.88,-568.33 2083.08,-566.6 2084,-560 2085.47,-549.44 2088.32,-538.13 2091.28,-528.2"/>
<polygon fill="black" stroke="black" points="2094.58,-529.35 2094.26,-518.76 2087.91,-527.24 2094.58,-529.35"/>
<text xml:space="preserve" text-anchor="middle" x="2097.5" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 28 -->
<g id="node29" class="node">
<title>28</title>
<polygon fill="#fffb81" stroke="black" points="3138.88,-1103.5 2723.12,-1103.5 2723.12,-1020.5 3138.88,-1020.5 3138.88,-1103.5"/>
<text xml:space="preserve" text-anchor="start" x="2731.12" y="-1086.2" font-family="DejaVu Sans Mono" font-size="14.00">model_config = ConfigDict(strict=True, validate_assignment=True)</text>
<text xml:space="preserve" text-anchor="start" x="2731.12" y="-1071.2" font-family="DejaVu Sans Mono" font-size="14.00">prompt: str = Field(..., min_length=10)</text>
<text xml:space="preserve" text-anchor="start" x="2731.12" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">token_count: int</text>
<text xml:space="preserve" text-anchor="start" x="2731.12" y="-1041.2" font-family="DejaVu Sans Mono" font-size="14.00">version: int = 0</text>
<text xml:space="preserve" text-anchor="middle" x="2931" y="-1026.2" font-family="DejaVu Sans Mono" font-size="14.00">@field_validator(&#39;token_count&#39;)...</text>
</g>
<!-- 29 -->
<g id="node30" class="node">
<title>29</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2927.12,-517 2860.88,-517 2860.88,-521 2848.88,-521 2848.88,-481 2927.12,-481 2927.12,-517"/>
<polyline fill="none" stroke="#e552ff" points="2848.88,-517 2860.88,-517"/>
<text xml:space="preserve" text-anchor="middle" x="2888" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">ConfigDict</text>
</g>
<!-- 28&#45;&gt;29 -->
<g id="edge22" class="edge">
<title>28&#45;&gt;29</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2927.87,-1020.2C2919.6,-912.22 2897.27,-620.91 2890.19,-528.52"/>
<polygon fill="black" stroke="black" points="2893.7,-528.53 2889.44,-518.82 2886.72,-529.06 2893.7,-528.53"/>
<text xml:space="preserve" text-anchor="middle" x="2906.83" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 30 -->
<g id="node31" class="node">
<title>30</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2999,-517 2957,-517 2957,-521 2945,-521 2945,-481 2999,-481 2999,-517"/>
<polyline fill="none" stroke="#e552ff" points="2945,-517 2957,-517"/>
<text xml:space="preserve" text-anchor="middle" x="2972" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">Field</text>
</g>
<!-- 28&#45;&gt;30 -->
<g id="edge23" class="edge">
<title>28&#45;&gt;30</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2933.98,-1020.2C2941.87,-912.22 2963.16,-620.91 2969.92,-528.52"/>
<polygon fill="black" stroke="black" points="2973.39,-529.05 2970.62,-518.82 2966.4,-528.54 2973.39,-529.05"/>
<text xml:space="preserve" text-anchor="middle" x="2980.42" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 32 -->
<g id="node32" class="node">
<title>32</title>
<polygon fill="#fffb81" stroke="black" points="2704.75,-1080 2513.25,-1080 2513.25,-1044 2704.75,-1044 2704.75,-1080"/>
<text xml:space="preserve" text-anchor="middle" x="2609" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">assert v &gt; 0, &#39;empty prompt?&#39;</text>
</g>
<!-- 34 -->
<g id="node33" class="node">
<title>34</title>
<polygon fill="#98fb98" stroke="black" points="2667.84,-517 2574.24,-517 2550.16,-481 2643.76,-481 2667.84,-517"/>
<text xml:space="preserve" text-anchor="middle" x="2609" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">return v</text>
</g>
<!-- 32&#45;&gt;34 -->
<g id="edge24" class="edge">
<title>32&#45;&gt;34</title>
<path fill="none" stroke="black" d="M2608.04,-1043.79C2604.57,-981.05 2592.78,-758.63 2589,-575 2588.86,-568.33 2587.93,-566.58 2589,-560 2590.74,-549.29 2594.11,-537.91 2597.6,-527.97"/>
<polygon fill="black" stroke="black" points="2600.77,-529.49 2600.98,-518.89 2594.22,-527.04 2600.77,-529.49"/>
<text xml:space="preserve" text-anchor="middle" x="2604" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">v &gt; 0</text>
</g>
<!-- 39 -->
<g id="node34" class="node">
<title>39</title>
<polygon fill="#fffb81" stroke="black" points="3803.25,-1081 3638.75,-1081 3638.75,-1043 3803.25,-1043 3803.25,-1081"/>
<text xml:space="preserve" text-anchor="start" x="3646.75" y="-1063.7" font-family="DejaVu Sans Mono" font-size="14.00">scenarios: List[str]</text>
<text xml:space="preserve" text-anchor="middle" x="3721" y="-1048.7" font-family="DejaVu Sans Mono" font-size="14.00">def _display(self) &#45;&gt;str:...</text>
</g>
<!-- 41 -->
<g id="node35" class="node">
<title>41</title>
<polygon fill="#98fb98" stroke="black" points="3620.98,-1115 3263.09,-1115 3171.02,-1009 3528.91,-1009 3620.98,-1115"/>
<text xml:space="preserve" text-anchor="middle" x="3396" y="-1071.2" font-family="DejaVu Sans Mono" font-size="14.00">return &#39;## Scenarios</text>
<text xml:space="preserve" text-anchor="middle" x="3396" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="middle" x="3396" y="-1041.2" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {s}&#39; for s in self.scenarios)</text>
</g>
<!-- 46 -->
<g id="node36" class="node">
<title>46</title>
<polygon fill="#fffb81" stroke="black" points="4503.25,-1081 4338.75,-1081 4338.75,-1043 4503.25,-1043 4503.25,-1081"/>
<text xml:space="preserve" text-anchor="start" x="4346.75" y="-1063.7" font-family="DejaVu Sans Mono" font-size="14.00">requirements: List[str]</text>
<text xml:space="preserve" text-anchor="middle" x="4421" y="-1048.7" font-family="DejaVu Sans Mono" font-size="14.00">def _display(self) &#45;&gt;str:...</text>
</g>
<!-- 48 -->
<g id="node37" class="node">
<title>48</title>
<polygon fill="#98fb98" stroke="black" points="4320.86,-1115 3934.52,-1115 3835.14,-1009 4221.48,-1009 4320.86,-1115"/>
<text xml:space="preserve" text-anchor="middle" x="4078" y="-1071.2" font-family="DejaVu Sans Mono" font-size="14.00">return &#39;## Requirements</text>
<text xml:space="preserve" text-anchor="middle" x="4078" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="middle" x="4078" y="-1041.2" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {r}&#39; for r in self.requirements)</text>
</g>
<!-- 53 -->
<g id="node38" class="node">
<title>53</title>
<polygon fill="#fffb81" stroke="black" points="4687.38,-1088.5 4526.62,-1088.5 4526.62,-1035.5 4687.38,-1035.5 4687.38,-1088.5"/>
<text xml:space="preserve" text-anchor="start" x="4534.62" y="-1071.2" font-family="DejaVu Sans Mono" font-size="14.00">final_prompt: str</text>
<text xml:space="preserve" text-anchor="start" x="4534.62" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">test_generation_doc: str</text>
<text xml:space="preserve" text-anchor="middle" x="4607" y="-1041.2" font-family="DejaVu Sans Mono" font-size="14.00">requirements_doc: str</text>
</g>
<!-- 57 -->
<g id="node39" class="node">
<title>57</title>
<polygon fill="#fffb81" stroke="black" points="5439.12,-1088.5 4958.88,-1088.5 4958.88,-1035.5 5439.12,-1035.5 5439.12,-1088.5"/>
<text xml:space="preserve" text-anchor="start" x="4966.88" y="-1071.2" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;LLM wrapper; micro is readability only.&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="4966.88" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">tmpl = ChatPromptTemplate.from_messages([(&#39;system&#39;, sys_prompt), (&#39;user&#39;,</text>
<text xml:space="preserve" text-anchor="middle" x="5199" y="-1041.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;{u}&#39;)])</text>
</g>
<!-- 58 -->
<g id="node40" class="node">
<title>58</title>
<polygon fill="#e552ff" stroke="#e552ff" points="4964.75,-517 4731.25,-517 4731.25,-521 4719.25,-521 4719.25,-481 4964.75,-481 4964.75,-517"/>
<polyline fill="none" stroke="#e552ff" points="4719.25,-517 4731.25,-517"/>
<text xml:space="preserve" text-anchor="middle" x="4842" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">ChatPromptTemplate.from_messages</text>
</g>
<!-- 57&#45;&gt;58 -->
<g id="edge25" class="edge">
<title>57&#45;&gt;58</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5182.5,-1035.07C5122.96,-941.5 4919.54,-621.85 4859.08,-526.84"/>
<polygon fill="black" stroke="black" points="4862.19,-525.21 4853.87,-518.65 4856.28,-528.96 4862.19,-525.21"/>
<text xml:space="preserve" text-anchor="middle" x="4899.73" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 59 -->
<g id="node41" class="node">
<title>59</title>
<polygon fill="#98fb98" stroke="black" points="5911.08,-517 5172.83,-517 4982.92,-481 5721.17,-481 5911.08,-517"/>
<text xml:space="preserve" text-anchor="middle" x="5447" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">return llm.invoke(tmpl.format_prompt(u=user_prompt).to_messages()).content</text>
</g>
<!-- 57&#45;&gt;59 -->
<g id="edge26" class="edge">
<title>57&#45;&gt;59</title>
<path fill="none" stroke="black" d="M5210.46,-1035.07C5251.7,-941.79 5392.28,-623.79 5434.75,-527.71"/>
<polygon fill="black" stroke="black" points="5437.87,-529.32 5438.71,-518.76 5431.46,-526.49 5437.87,-529.32"/>
</g>
<!-- 63 -->
<g id="node42" class="node">
<title>63</title>
<polygon fill="#fffb81" stroke="black" points="6739.62,-1201 6190.38,-1201 6190.38,-923 6739.62,-923 6739.62,-1201"/>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1183.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Minimal Rev&#45;B demo showcasing key improvements&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1168.7" font-family="DejaVu Sans Mono" font-size="14.00">print(&#39;Step 1: Generating prompt with PROMPTS singleton...&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1153.7" font-family="DejaVu Sans Mono" font-size="14.00">body = call_llm(PROMPTS.gen_prompt.micro, PROMPTS.gen_prompt.sys, PROMPTS.</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1138.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;gen_prompt.usr.format(problem_desc=prob.problem_desc))</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1123.7" font-family="DejaVu Sans Mono" font-size="14.00">prompt_doc = PromptDoc(prompt=body, token_count=len(body.split()))</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1108.7" font-family="DejaVu Sans Mono" font-size="14.00">print(&#39;Step 2: Using MasterPrompt for version tracking...&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1093.7" font-family="DejaVu Sans Mono" font-size="14.00">master = MasterPrompt()</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1078.7" font-family="DejaVu Sans Mono" font-size="14.00">master.add(prompt_doc.prompt)</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1063.7" font-family="DejaVu Sans Mono" font-size="14.00">print(&#39;Step 3: Refining with PROMPTS singleton...&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1048.7" font-family="DejaVu Sans Mono" font-size="14.00">payload = f&quot;&quot;&quot;PROMPT:</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1033.7" font-family="DejaVu Sans Mono" font-size="14.00">{prompt_doc.prompt}</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1018.7" font-family="DejaVu Sans Mono" font-size="14.00">ISSUES:clarity,edge cases&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-1003.7" font-family="DejaVu Sans Mono" font-size="14.00">refined = call_llm(PROMPTS.refine.micro, PROMPTS.refine.sys, PROMPTS.refine</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-988.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;.usr.format(payload=payload))</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-973.7" font-family="DejaVu Sans Mono" font-size="14.00">refined_doc = prompt_doc.model_copy(update={&#39;prompt&#39;: refined, &#39;version&#39;: 1,</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-958.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;token_count&#39;: len(refined.split())})</text>
<text xml:space="preserve" text-anchor="start" x="6198.38" y="-943.7" font-family="DejaVu Sans Mono" font-size="14.00">master.add(refined_doc.prompt)</text>
<text xml:space="preserve" text-anchor="middle" x="6465" y="-928.7" font-family="DejaVu Sans Mono" font-size="14.00">print(f&#39;Version history: {len(master.versions)} versions&#39;)</text>
</g>
<!-- 64 -->
<g id="node43" class="node">
<title>64</title>
<polygon fill="#e552ff" stroke="#e552ff" points="5997,-517 5955,-517 5955,-521 5943,-521 5943,-481 5997,-481 5997,-517"/>
<polyline fill="none" stroke="#e552ff" points="5943,-517 5955,-517"/>
<text xml:space="preserve" text-anchor="middle" x="5970" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 63&#45;&gt;64 -->
<g id="edge27" class="edge">
<title>63&#45;&gt;64</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6331.37,-922.64C6236.99,-822.59 6109.41,-682.61 6006,-552 5999.63,-543.96 5993.12,-534.86 5987.41,-526.54"/>
<polygon fill="black" stroke="black" points="5990.48,-524.84 5981.99,-518.51 5984.68,-528.76 5990.48,-524.84"/>
<text xml:space="preserve" text-anchor="middle" x="6030.94" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 65 -->
<g id="node44" class="node">
<title>65</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6076.88,-517 6027.12,-517 6027.12,-521 6015.12,-521 6015.12,-481 6076.88,-481 6076.88,-517"/>
<polyline fill="none" stroke="#e552ff" points="6015.12,-517 6027.12,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6046" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 63&#45;&gt;65 -->
<g id="edge28" class="edge">
<title>63&#45;&gt;65</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6361.7,-922.69C6261.71,-788.82 6117.86,-596.22 6065.77,-526.47"/>
<polygon fill="black" stroke="black" points="6068.74,-524.6 6059.95,-518.68 6063.13,-528.79 6068.74,-524.6"/>
<text xml:space="preserve" text-anchor="middle" x="6111.42" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 67 -->
<g id="node45" class="node">
<title>67</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6179.12,-517 6106.88,-517 6106.88,-521 6094.88,-521 6094.88,-481 6179.12,-481 6179.12,-517"/>
<polyline fill="none" stroke="#e552ff" points="6094.88,-517 6106.88,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6137" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">PromptDoc</text>
</g>
<!-- 63&#45;&gt;67 -->
<g id="edge29" class="edge">
<title>63&#45;&gt;67</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6384.13,-922.69C6306.18,-789.37 6194.18,-597.79 6152.98,-527.34"/>
<polygon fill="black" stroke="black" points="6156.05,-525.64 6147.98,-518.77 6150,-529.17 6156.05,-525.64"/>
<text xml:space="preserve" text-anchor="middle" x="6191.14" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 68 -->
<g id="node46" class="node">
<title>68</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6251,-517 6209,-517 6209,-521 6197,-521 6197,-481 6251,-481 6251,-517"/>
<polyline fill="none" stroke="#e552ff" points="6197,-517 6209,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6224" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 63&#45;&gt;68 -->
<g id="edge30" class="edge">
<title>63&#45;&gt;68</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6405.58,-922.69C6348.43,-789.64 6266.35,-598.58 6235.93,-527.77"/>
<polygon fill="black" stroke="black" points="6239.26,-526.67 6232.1,-518.86 6232.83,-529.43 6239.26,-526.67"/>
<text xml:space="preserve" text-anchor="middle" x="6267.36" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 69 -->
<g id="node47" class="node">
<title>69</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6371.12,-517 6280.88,-517 6280.88,-521 6268.88,-521 6268.88,-481 6371.12,-481 6371.12,-517"/>
<polyline fill="none" stroke="#e552ff" points="6268.88,-517 6280.88,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6320" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">MasterPrompt</text>
</g>
<!-- 63&#45;&gt;69 -->
<g id="edge31" class="edge">
<title>63&#45;&gt;69</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6429.25,-922.69C6394.93,-789.92 6345.68,-599.36 6327.29,-528.21"/>
<polygon fill="black" stroke="black" points="6330.78,-527.74 6324.89,-518.93 6324.01,-529.49 6330.78,-527.74"/>
<text xml:space="preserve" text-anchor="middle" x="6351.47" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 70 -->
<g id="node48" class="node">
<title>70</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6473.12,-517 6400.88,-517 6400.88,-521 6388.88,-521 6388.88,-481 6473.12,-481 6473.12,-517"/>
<polyline fill="none" stroke="#e552ff" points="6388.88,-517 6400.88,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6431" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">master.add</text>
</g>
<!-- 63&#45;&gt;70 -->
<g id="edge32" class="edge">
<title>63&#45;&gt;70</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6456.62,-922.69C6448.59,-790.19 6437.07,-600.15 6432.74,-528.65"/>
<polygon fill="black" stroke="black" points="6436.25,-528.75 6432.15,-518.98 6429.26,-529.17 6436.25,-528.75"/>
<text xml:space="preserve" text-anchor="middle" x="6448.71" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 71 -->
<g id="node49" class="node">
<title>71</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6545,-517 6503,-517 6503,-521 6491,-521 6491,-481 6545,-481 6545,-517"/>
<polyline fill="none" stroke="#e552ff" points="6491,-517 6503,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6518" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 63&#45;&gt;71 -->
<g id="edge33" class="edge">
<title>63&#45;&gt;71</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6478.07,-922.69C6490.58,-790.19 6508.54,-600.15 6515.29,-528.65"/>
<polygon fill="black" stroke="black" points="6518.75,-529.26 6516.21,-518.97 6511.78,-528.6 6518.75,-529.26"/>
<text xml:space="preserve" text-anchor="middle" x="6524.93" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 72 -->
<g id="node50" class="node">
<title>72</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6624.88,-517 6575.12,-517 6575.12,-521 6563.12,-521 6563.12,-481 6624.88,-481 6624.88,-517"/>
<polyline fill="none" stroke="#e552ff" points="6563.12,-517 6575.12,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6594" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 63&#45;&gt;72 -->
<g id="edge34" class="edge">
<title>63&#45;&gt;72</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6496.8,-922.69C6527.27,-790.19 6570.97,-600.15 6587.41,-528.65"/>
<polygon fill="black" stroke="black" points="6590.81,-529.47 6589.64,-518.94 6583.99,-527.9 6590.81,-529.47"/>
<text xml:space="preserve" text-anchor="middle" x="6591.52" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 74 -->
<g id="node51" class="node">
<title>74</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6808.62,-517 6655.38,-517 6655.38,-521 6643.38,-521 6643.38,-481 6808.62,-481 6808.62,-517"/>
<polyline fill="none" stroke="#e552ff" points="6643.38,-517 6655.38,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6726" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">prompt_doc.model_copy</text>
</g>
<!-- 63&#45;&gt;74 -->
<g id="edge35" class="edge">
<title>63&#45;&gt;74</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6529.35,-922.69C6591.25,-789.64 6680.14,-598.58 6713.08,-527.77"/>
<polygon fill="black" stroke="black" points="6716.19,-529.38 6717.23,-518.84 6709.84,-526.43 6716.19,-529.38"/>
<text xml:space="preserve" text-anchor="middle" x="6707.16" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 75 -->
<g id="node52" class="node">
<title>75</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6911.12,-517 6838.88,-517 6838.88,-521 6826.88,-521 6826.88,-481 6911.12,-481 6911.12,-517"/>
<polyline fill="none" stroke="#e552ff" points="6826.88,-517 6838.88,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6869" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">master.add</text>
</g>
<!-- 63&#45;&gt;75 -->
<g id="edge36" class="edge">
<title>63&#45;&gt;75</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6564.61,-922.69C6661.01,-788.82 6799.71,-596.22 6849.93,-526.47"/>
<polygon fill="black" stroke="black" points="6852.53,-528.86 6855.54,-518.7 6846.85,-524.77 6852.53,-528.86"/>
<text xml:space="preserve" text-anchor="middle" x="6832.44" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 76 -->
<g id="node53" class="node">
<title>76</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6983,-517 6941,-517 6941,-521 6929,-521 6929,-481 6983,-481 6983,-517"/>
<polyline fill="none" stroke="#e552ff" points="6929,-517 6941,-517"/>
<text xml:space="preserve" text-anchor="middle" x="6956" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 63&#45;&gt;76 -->
<g id="edge37" class="edge">
<title>63&#45;&gt;76</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6597.08,-922.73C6690.61,-822.58 6817.19,-682.44 6920,-552 6926.35,-543.94 6932.86,-534.84 6938.57,-526.53"/>
<polygon fill="black" stroke="black" points="6941.3,-528.74 6944,-518.5 6935.5,-524.82 6941.3,-528.74"/>
<text xml:space="preserve" text-anchor="middle" x="6922.14" y="-561.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 78 -->
<g id="node57" class="node">
<title>78</title>
<polygon fill="#98fb98" stroke="black" points="7931.24,-552 7191.15,-552 7000.76,-446 7740.85,-446 7931.24,-552"/>
<text xml:space="preserve" text-anchor="start" x="7085.37" y="-508.2" font-family="DejaVu Sans Mono" font-size="14.00">return FinalBundle(final_prompt=master.current(), test_generation_doc=</text>
<text xml:space="preserve" text-anchor="start" x="7085.37" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;&lt;generated using Rev&#45;B patterns&gt;&#39;, requirements_doc=</text>
<text xml:space="preserve" text-anchor="middle" x="7466" y="-478.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;&lt;enhanced with _display() methods&gt;&#39;)</text>
</g>
<!-- 63&#45;&gt;78 -->
<g id="edge41" class="edge">
<title>63&#45;&gt;78</title>
<path fill="none" stroke="black" d="M6712.12,-922.51C6921.16,-805.35 7210.29,-643.31 7362.59,-557.96"/>
<polygon fill="black" stroke="black" points="7364.09,-561.13 7371.1,-553.19 7360.66,-555.02 7364.09,-561.13"/>
</g>
<!-- 66 -->
<g id="node54" class="node">
<title>66</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6169,-309 5955,-309 5955,-313 5943,-313 5943,-273 6169,-273 6169,-309"/>
<polyline fill="none" stroke="#e552ff" points="5943,-309 5955,-309"/>
<text xml:space="preserve" text-anchor="middle" x="6056" y="-285.2" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS.gen_prompt.usr.format</text>
</g>
<!-- 65&#45;&gt;66 -->
<g id="edge38" class="edge">
<title>65&#45;&gt;66</title>
<path fill="none" stroke="black" d="M6046.84,-480.79C6048.55,-445.42 6052.49,-364.24 6054.6,-320.75"/>
<polygon fill="black" stroke="black" points="6058.1,-320.97 6055.09,-310.82 6051.11,-320.64 6058.1,-320.97"/>
</g>
<!-- 73 -->
<g id="node55" class="node">
<title>73</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6687.5,-309 6512.5,-309 6512.5,-313 6500.5,-313 6500.5,-273 6687.5,-273 6687.5,-309"/>
<polyline fill="none" stroke="#e552ff" points="6500.5,-309 6512.5,-309"/>
<text xml:space="preserve" text-anchor="middle" x="6594" y="-285.2" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS.refine.usr.format</text>
</g>
<!-- 72&#45;&gt;73 -->
<g id="edge39" class="edge">
<title>72&#45;&gt;73</title>
<path fill="none" stroke="black" d="M6594,-480.79C6594,-445.42 6594,-364.24 6594,-320.75"/>
<polygon fill="black" stroke="black" points="6597.5,-320.82 6594,-310.82 6590.5,-320.82 6597.5,-320.82"/>
</g>
<!-- 77 -->
<g id="node56" class="node">
<title>77</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6983,-309 6941,-309 6941,-313 6929,-313 6929,-273 6983,-273 6983,-309"/>
<polyline fill="none" stroke="#e552ff" points="6929,-309 6941,-309"/>
<text xml:space="preserve" text-anchor="middle" x="6956" y="-285.2" font-family="DejaVu Sans Mono" font-size="14.00">len</text>
</g>
<!-- 76&#45;&gt;77 -->
<g id="edge40" class="edge">
<title>76&#45;&gt;77</title>
<path fill="none" stroke="black" d="M6956,-480.79C6956,-445.42 6956,-364.24 6956,-320.75"/>
<polygon fill="black" stroke="black" points="6959.5,-320.82 6956,-310.82 6952.5,-320.82 6959.5,-320.82"/>
</g>
<!-- input -->
<g id="node58" class="node">
<title>input</title>
<polygon fill="#afeeee" stroke="black" points="8041.12,-517 7972.53,-517 7954.88,-481 8023.47,-481 8041.12,-517"/>
<text xml:space="preserve" text-anchor="middle" x="7998" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">input</text>
</g>
<!-- call -->
<g id="node63" class="node">
<title>call</title>
<polygon fill="#e552ff" stroke="black" points="8025,-309 7983,-309 7983,-313 7971,-313 7971,-273 8025,-273 8025,-309"/>
<polyline fill="none" stroke="black" points="7971,-309 7983,-309"/>
<text xml:space="preserve" text-anchor="middle" x="7998" y="-285.2" font-family="DejaVu Sans Mono" font-size="14.00">call</text>
</g>
<!-- input&#45;&gt;call -->
<!-- default -->
<g id="node59" class="node">
<title>default</title>
<polygon fill="#fffb81" stroke="black" points="8136.25,-309 8079.75,-309 8079.75,-273 8136.25,-273 8136.25,-309"/>
<text xml:space="preserve" text-anchor="middle" x="8108" y="-285.2" font-family="DejaVu Sans Mono" font-size="14.00">default</text>
</g>
<!-- if -->
<g id="node60" class="node">
<title>if</title>
<polygon fill="#ff6752" stroke="black" points="7985,-1080 7958,-1062 7985,-1044 8012,-1062 7985,-1080"/>
<text xml:space="preserve" text-anchor="middle" x="7985" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">if</text>
</g>
<!-- if&#45;&gt;input -->
<!-- for -->
<g id="node61" class="node">
<title>for</title>
<polygon fill="#ffbe52" stroke="black" points="8084,-1062 8070.5,-1080 8043.5,-1080 8030,-1062 8043.5,-1044 8070.5,-1044 8084,-1062"/>
<text xml:space="preserve" text-anchor="middle" x="8057" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">for</text>
</g>
<!-- return -->
<g id="node64" class="node">
<title>return</title>
<polygon fill="#98fb98" stroke="black" points="8156.84,-517 8079.15,-517 8059.16,-481 8136.85,-481 8156.84,-517"/>
<text xml:space="preserve" text-anchor="middle" x="8108" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">return</text>
</g>
<!-- for&#45;&gt;return -->
<!-- while -->
<g id="node62" class="node">
<title>while</title>
<polygon fill="#ffbe52" stroke="black" points="8172.09,-1062 8154.54,-1080 8119.46,-1080 8101.91,-1062 8119.46,-1044 8154.54,-1044 8172.09,-1062"/>
<text xml:space="preserve" text-anchor="middle" x="8137" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">while</text>
</g>
<!-- return&#45;&gt;default -->
<!-- try -->
<g id="node65" class="node">
<title>try</title>
<polygon fill="orange" stroke="black" points="8219,-1080 8190.51,-1062 8219,-1044 8247.49,-1062 8219,-1080"/>
<polyline fill="none" stroke="black" points="8200,-1068 8200,-1056"/>
<polyline fill="none" stroke="black" points="8209.5,-1050 8228.5,-1050"/>
<polyline fill="none" stroke="black" points="8238,-1056 8238,-1068"/>
<polyline fill="none" stroke="black" points="8228.5,-1074 8209.5,-1074"/>
<text xml:space="preserve" text-anchor="middle" x="8219" y="-1056.2" font-family="DejaVu Sans Mono" font-size="14.00">try</text>
</g>
<!-- raise -->
<g id="node66" class="node">
<title>raise</title>
<polygon fill="#98fb98" stroke="black" points="8247.35,-504.56 8211,-517 8174.65,-504.56 8174.68,-484.44 8247.32,-484.44 8247.35,-504.56"/>
<text xml:space="preserve" text-anchor="middle" x="8211" y="-493.2" font-family="DejaVu Sans Mono" font-size="14.00">raise</text>
</g>
<!-- try&#45;&gt;raise -->
</g>
</svg>
