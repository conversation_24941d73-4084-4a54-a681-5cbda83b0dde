<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 13.1.0 (20250701.0955)
 -->
<!-- Title: cluster0/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/Langchain Pipeline_cfg.dot Pages: 1 -->
<svg width="12527pt" height="1383pt"
 viewBox="0.00 0.00 12527.00 1383.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1379)">
<title>cluster0/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/Langchain Pipeline_cfg.dot</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-1379 12522.75,-1379 12522.75,4 -4,4"/>
<text xml:space="preserve" text-anchor="middle" x="6259.38" y="-5.7" font-family="DejaVu Sans Mono" font-size="14.00">/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/Langchain Pipeline_cfg.dot</text>
<g id="clust1" class="cluster">
<title>cluster_1</title>
<polygon fill="purple" stroke="purple" points="217.75,-293 217.75,-345 325.75,-345 325.75,-293 217.75,-293"/>
</g>
<g id="clust2" class="cluster">
<title>cluster_95</title>
<polygon fill="purple" stroke="purple" points="335.75,-31 335.75,-83 629.75,-83 629.75,-31 335.75,-31"/>
</g>
<g id="clust3" class="cluster">
<title>cluster0ProblemDesc</title>
<polygon fill="none" stroke="black" points="682.75,-285 682.75,-954 1032.75,-954 1032.75,-285 682.75,-285"/>
<text xml:space="preserve" text-anchor="middle" x="857.75" y="-936.7" font-family="DejaVu Sans Mono" font-size="14.00">ProblemDesc</text>
</g>
<g id="clust4" class="cluster">
<title>cluster_3</title>
<polygon fill="purple" stroke="purple" points="810.75,-293 810.75,-345 904.75,-345 904.75,-293 810.75,-293"/>
</g>
<g id="clust5" class="cluster">
<title>cluster0PromptDoc</title>
<polygon fill="none" stroke="black" points="1040.75,-285 1040.75,-962.5 1757.75,-962.5 1757.75,-285 1040.75,-285"/>
<text xml:space="preserve" text-anchor="middle" x="1399.25" y="-945.2" font-family="DejaVu Sans Mono" font-size="14.00">PromptDoc</text>
</g>
<g id="clust6" class="cluster">
<title>cluster_7</title>
<polygon fill="purple" stroke="purple" points="1451.75,-293 1451.75,-345 1617.75,-345 1617.75,-293 1451.75,-293"/>
</g>
<g id="clust7" class="cluster">
<title>cluster0_check_budget</title>
<polygon fill="none" stroke="black" points="1048.75,-293 1048.75,-931.5 1324.75,-931.5 1324.75,-293 1048.75,-293"/>
<text xml:space="preserve" text-anchor="middle" x="1186.75" y="-914.2" font-family="DejaVu Sans Mono" font-size="14.00">_check_budget</text>
</g>
<g id="clust8" class="cluster">
<title>cluster0TestCaseDoc</title>
<polygon fill="none" stroke="black" points="1765.75,-854.5 1765.75,-931.5 1939.75,-931.5 1939.75,-854.5 1765.75,-854.5"/>
<text xml:space="preserve" text-anchor="middle" x="1852.75" y="-914.2" font-family="DejaVu Sans Mono" font-size="14.00">TestCaseDoc</text>
</g>
<g id="clust9" class="cluster">
<title>cluster0SyntheticDoc</title>
<polygon fill="none" stroke="black" points="1947.75,-855.5 1947.75,-930.5 2125.75,-930.5 2125.75,-855.5 1947.75,-855.5"/>
<text xml:space="preserve" text-anchor="middle" x="2036.75" y="-913.2" font-family="DejaVu Sans Mono" font-size="14.00">SyntheticDoc</text>
</g>
<g id="clust10" class="cluster">
<title>cluster0GradeDoc</title>
<polygon fill="none" stroke="black" points="2133.75,-854.5 2133.75,-931.5 2337.75,-931.5 2337.75,-854.5 2133.75,-854.5"/>
<text xml:space="preserve" text-anchor="middle" x="2235.75" y="-914.2" font-family="DejaVu Sans Mono" font-size="14.00">GradeDoc</text>
</g>
<g id="clust11" class="cluster">
<title>cluster0FinalBundle</title>
<polygon fill="none" stroke="black" points="2345.75,-847 2345.75,-939 2521.75,-939 2521.75,-847 2345.75,-847"/>
<text xml:space="preserve" text-anchor="middle" x="2433.75" y="-921.7" font-family="DejaVu Sans Mono" font-size="14.00">FinalBundle</text>
</g>
<g id="clust12" class="cluster">
<title>cluster0call_llm</title>
<polygon fill="none" stroke="black" points="2529.75,-285 2529.75,-939 3745.75,-939 3745.75,-285 2529.75,-285"/>
<text xml:space="preserve" text-anchor="middle" x="3137.75" y="-921.7" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<g id="clust13" class="cluster">
<title>cluster_31</title>
<polygon fill="purple" stroke="purple" points="2537.75,-293 2537.75,-345 2799.75,-345 2799.75,-293 2537.75,-293"/>
</g>
<g id="clust14" class="cluster">
<title>cluster0gen_prompt</title>
<polygon fill="none" stroke="black" points="3753.75,-285 3753.75,-939 4607.75,-939 4607.75,-285 3753.75,-285"/>
<text xml:space="preserve" text-anchor="middle" x="4180.75" y="-921.7" font-family="DejaVu Sans Mono" font-size="14.00">gen_prompt</text>
</g>
<g id="clust15" class="cluster">
<title>cluster_37</title>
<polygon fill="purple" stroke="purple" points="3761.75,-293 3761.75,-345 3839.75,-345 3839.75,-293 3761.75,-293"/>
</g>
<g id="clust16" class="cluster">
<title>cluster0gen_testcases</title>
<polygon fill="none" stroke="black" points="4615.75,-285 4615.75,-931.5 5523.75,-931.5 5523.75,-285 4615.75,-285"/>
<text xml:space="preserve" text-anchor="middle" x="5069.75" y="-914.2" font-family="DejaVu Sans Mono" font-size="14.00">gen_testcases</text>
</g>
<g id="clust17" class="cluster">
<title>cluster_43</title>
<polygon fill="purple" stroke="purple" points="4623.75,-293 4623.75,-345 4701.75,-345 4701.75,-293 4623.75,-293"/>
</g>
<g id="clust18" class="cluster">
<title>cluster0gen_synthetic</title>
<polygon fill="none" stroke="black" points="5531.75,-144 5531.75,-939 6255.75,-939 6255.75,-144 5531.75,-144"/>
<text xml:space="preserve" text-anchor="middle" x="5893.75" y="-921.7" font-family="DejaVu Sans Mono" font-size="14.00">gen_synthetic</text>
</g>
<g id="clust19" class="cluster">
<title>cluster_49</title>
<polygon fill="purple" stroke="purple" points="5539.75,-152 5539.75,-345 5617.75,-345 5617.75,-152 5539.75,-152"/>
</g>
<g id="clust20" class="cluster">
<title>cluster0grade_tests</title>
<polygon fill="none" stroke="black" points="6263.75,-293 6263.75,-930.5 7195.75,-930.5 7195.75,-293 6263.75,-293"/>
<text xml:space="preserve" text-anchor="middle" x="6729.75" y="-913.2" font-family="DejaVu Sans Mono" font-size="14.00">grade_tests</text>
</g>
<g id="clust21" class="cluster">
<title>cluster0refine_prompt</title>
<polygon fill="none" stroke="black" points="7203.75,-144 7203.75,-961.5 8228.75,-961.5 8228.75,-144 7203.75,-144"/>
<text xml:space="preserve" text-anchor="middle" x="7716.25" y="-944.2" font-family="DejaVu Sans Mono" font-size="14.00">refine_prompt</text>
</g>
<g id="clust22" class="cluster">
<title>cluster_61</title>
<polygon fill="purple" stroke="purple" points="7211.75,-152 7211.75,-345 7289.75,-345 7289.75,-152 7211.75,-152"/>
</g>
<g id="clust23" class="cluster">
<title>cluster0optimise_prompt</title>
<polygon fill="none" stroke="black" points="8236.75,-273 8236.75,-931.5 9331.75,-931.5 9331.75,-273 8236.75,-273"/>
<text xml:space="preserve" text-anchor="middle" x="8784.25" y="-914.2" font-family="DejaVu Sans Mono" font-size="14.00">optimise_prompt</text>
</g>
<g id="clust24" class="cluster">
<title>cluster_68</title>
<polygon fill="purple" stroke="purple" points="8244.75,-293 8244.75,-345 8322.75,-345 8322.75,-293 8244.75,-293"/>
</g>
<g id="clust25" class="cluster">
<title>cluster0deliver</title>
<polygon fill="none" stroke="black" points="9339.75,-273 9339.75,-930.5 10331.75,-930.5 10331.75,-273 9339.75,-273"/>
<text xml:space="preserve" text-anchor="middle" x="9835.75" y="-913.2" font-family="DejaVu Sans Mono" font-size="14.00">deliver</text>
</g>
<g id="clust26" class="cluster">
<title>cluster0pipe</title>
<polygon fill="none" stroke="black" points="10339.75,-293 10339.75,-930.5 11055.75,-930.5 11055.75,-293 10339.75,-293"/>
<text xml:space="preserve" text-anchor="middle" x="10697.75" y="-913.2" font-family="DejaVu Sans Mono" font-size="14.00">pipe</text>
</g>
<g id="clust27" class="cluster">
<title>cluster0run_pipeline</title>
<polygon fill="none" stroke="black" points="11063.75,-285 11063.75,-969 12194.75,-969 12194.75,-285 11063.75,-285"/>
<text xml:space="preserve" text-anchor="middle" x="11629.25" y="-951.7" font-family="DejaVu Sans Mono" font-size="14.00">run_pipeline</text>
</g>
<g id="clust28" class="cluster">
<title>cluster_84</title>
<polygon fill="purple" stroke="purple" points="11071.75,-293 11071.75,-345 11772.75,-345 11772.75,-293 11071.75,-293"/>
</g>
<g id="clust29" class="cluster">
<title>cluster_KEY</title>
<polygon fill="none" stroke="black" points="12202.75,-152 12202.75,-930.5 12510.75,-930.5 12510.75,-152 12202.75,-152"/>
<text xml:space="preserve" text-anchor="middle" x="12356.75" y="-913.2" font-family="DejaVu Sans Mono" font-size="14.00">KEY</text>
</g>
<!-- 1 -->
<g id="node1" class="node">
<title>1</title>
<polygon fill="#fffb81" stroke="black" points="653.5,-1375 0,-1375 0,-388 653.5,-388 653.5,-1375"/>
<text xml:space="preserve" text-anchor="start" x="8" y="-1357.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1342.7" font-family="DejaVu Sans Mono" font-size="14.00"> aa_langchain_pipeline.py &#160;📜</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1323.95" font-family="DejaVu Sans Mono" font-size="14.00"> =========================</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1308.95" font-family="DejaVu Sans Mono" font-size="14.00"> Minimal FCIS pipeline that mirrors the Prompt‑Generation flowchart via LangChain.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1293.95" font-family="DejaVu Sans Mono" font-size="14.00"> ‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1278.95" font-family="DejaVu Sans Mono" font-size="14.00"> • Pyramid bullets (why):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1263.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;1. Provide an executable, strictly‑typed LangChain pipeline for prompt/test generation.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1248.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;2. Keep business logic pure, brittle, &amp; readable (Duplo Blocks).</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1233.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;3. Make every stage a tiny callable with Pydantic I/O; pipeline = list composition.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1218.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;4. Expose one CLI entry so running the file prints deliverables end‑to‑end.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1203.95" font-family="DejaVu Sans Mono" font-size="14.00"> • Requirements:</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1188.95" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;⏳ backlog — env var OPENAI_API_KEY must be set.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1170.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;🟠 planned — unit tests + DocTests per block.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1151.45" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;☑️ implemented — strict Pydantic, numbered EOL comments, Pipeline orchestrator.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1132.7" font-family="DejaVu Sans Mono" font-size="14.00"> • Control‑flow (ASCII):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1117.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;Input→PromptGen→TCGen→SynthGen→Chunk→Exec→Grade→Feedback→Refine→TokOpt→Final</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1099.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1084.7" font-family="DejaVu Sans Mono" font-size="14.00">from __future__ import annotations</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1069.7" font-family="DejaVu Sans Mono" font-size="14.00">from functools import reduce</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1054.7" font-family="DejaVu Sans Mono" font-size="14.00">from typing import Callable, List</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1039.7" font-family="DejaVu Sans Mono" font-size="14.00">from pydantic import BaseModel, Field, ConfigDict, field_validator</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1024.7" font-family="DejaVu Sans Mono" font-size="14.00">from langchain_openai import ChatOpenAI</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1009.7" font-family="DejaVu Sans Mono" font-size="14.00">from langchain.prompts import ChatPromptTemplate</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-994.7" font-family="DejaVu Sans Mono" font-size="14.00">class ProblemDesc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-979.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;model_config = ConfigDict(strict=True, extra=&#39;forbid&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-964.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;problem_desc: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-949.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;target_model: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-934.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;token_limits: dict[str, int]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-919.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;constraints: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-904.7" font-family="DejaVu Sans Mono" font-size="14.00">class PromptDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-889.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;model_config = ConfigDict(strict=True, validate_assignment=True)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-874.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;prompt: str = Field(..., min_length=10)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-859.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;token_count: int</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-844.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;version: int = 0</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-813.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;@field_validator(&#39;token_count&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-798.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def _check_budget(cls, v):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-783.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;budget = 4096</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-768.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;assert v &lt;= budget, &#39;token budget blown&#39;</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-753.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return v</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-738.7" font-family="DejaVu Sans Mono" font-size="14.00">class TestCaseDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-723.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;test_categories: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-708.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;template_count: int</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-693.7" font-family="DejaVu Sans Mono" font-size="14.00">class SyntheticDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-678.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;synthetic_tests: List[dict]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-663.7" font-family="DejaVu Sans Mono" font-size="14.00">class GradeDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-648.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;pass_rate: float</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-633.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;improvement_areas: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-618.7" font-family="DejaVu Sans Mono" font-size="14.00">class FinalBundle(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-603.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;final_prompt: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-588.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;test_generation_doc: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-573.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;requirements_doc: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-558.7" font-family="DejaVu Sans Mono" font-size="14.00">llm = ChatOpenAI(model=&#39;gpt&#45;4o&#45;mini&#39;, temperature=0)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-543.7" font-family="DejaVu Sans Mono" font-size="14.00">def call_llm(sys_prompt: str, user_prompt: str) &#45;&gt;str:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-528.7" font-family="DejaVu Sans Mono" font-size="14.00">def gen_prompt(inp: ProblemDesc) &#45;&gt;PromptDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-513.7" font-family="DejaVu Sans Mono" font-size="14.00">def gen_testcases(prompt_doc: PromptDoc) &#45;&gt;TestCaseDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-498.7" font-family="DejaVu Sans Mono" font-size="14.00">def gen_synthetic(tc: TestCaseDoc) &#45;&gt;SyntheticDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-483.7" font-family="DejaVu Sans Mono" font-size="14.00">def grade_tests(synth: SyntheticDoc) &#45;&gt;GradeDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-468.7" font-family="DejaVu Sans Mono" font-size="14.00">def refine_prompt(prompt_doc: PromptDoc, grade: GradeDoc) &#45;&gt;PromptDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-453.7" font-family="DejaVu Sans Mono" font-size="14.00">def optimise_prompt(prompt_doc: PromptDoc) &#45;&gt;PromptDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-438.7" font-family="DejaVu Sans Mono" font-size="14.00">def deliver(prompt_doc: PromptDoc) &#45;&gt;FinalBundle:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-423.7" font-family="DejaVu Sans Mono" font-size="14.00">Stage = Callable[[BaseModel], BaseModel]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-408.7" font-family="DejaVu Sans Mono" font-size="14.00">def pipe(*stages: Stage) &#45;&gt;Stage:...</text>
<text xml:space="preserve" text-anchor="middle" x="326.75" y="-393.7" font-family="DejaVu Sans Mono" font-size="14.00">def run_pipeline(prob: ProblemDesc) &#45;&gt;FinalBundle:...</text>
</g>
<!-- 29 -->
<g id="node2" class="node">
<title>29</title>
<polygon fill="#e552ff" stroke="#e552ff" points="317.25,-337 238.25,-337 238.25,-341 226.25,-341 226.25,-301 317.25,-301 317.25,-337"/>
<polyline fill="none" stroke="#e552ff" points="226.25,-337 238.25,-337"/>
<text xml:space="preserve" text-anchor="middle" x="271.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">ChatOpenAI</text>
</g>
<!-- 1&#45;&gt;29 -->
<g id="edge1" class="edge">
<title>1&#45;&gt;29</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M278.38,-387.61C276.88,-372.32 275.58,-359.07 274.54,-348.46"/>
<polygon fill="black" stroke="black" points="278.05,-348.36 273.59,-338.75 271.08,-349.04 278.05,-348.36"/>
<text xml:space="preserve" text-anchor="middle" x="290.97" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 94 -->
<g id="node3" class="node">
<title>94</title>
<polygon fill="#ff6752" stroke="black" points="504.75,-337 334.94,-319 504.75,-301 674.56,-319 504.75,-337"/>
<text xml:space="preserve" text-anchor="middle" x="504.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">if __name__ == &#39;__main__&#39;:</text>
</g>
<!-- 1&#45;&gt;94 -->
<g id="edge6" class="edge">
<title>1&#45;&gt;94</title>
<path fill="none" stroke="black" d="M483.28,-387.61C488.26,-371.92 492.56,-358.38 495.98,-347.63"/>
<polygon fill="black" stroke="black" points="499.29,-348.75 498.98,-338.16 492.62,-346.63 499.29,-348.75"/>
</g>
<!-- 95 -->
<g id="node4" class="node">
<title>95</title>
<polygon fill="#fffb81" stroke="black" points="696.5,-242 313,-242 313,-114 696.5,-114 696.5,-242"/>
<text xml:space="preserve" text-anchor="start" x="321" y="-224.7" font-family="DejaVu Sans Mono" font-size="14.00">prob = ProblemDesc(problem_desc=</text>
<text xml:space="preserve" text-anchor="start" x="321" y="-209.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;Summarise legal contract clauses for risk.&#39;, target_model=</text>
<text xml:space="preserve" text-anchor="start" x="321" y="-194.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;gpt&#45;4o&#45;mini&#39;, token_limits={&#39;hard&#39;: 4096}, constraints=[</text>
<text xml:space="preserve" text-anchor="start" x="321" y="-179.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;no hallucinations&#39;, &#39;&lt;=2000 tokens&#39;])</text>
<text xml:space="preserve" text-anchor="start" x="321" y="-164.7" font-family="DejaVu Sans Mono" font-size="14.00">bundle = run_pipeline(prob)</text>
<text xml:space="preserve" text-anchor="start" x="321" y="-149.7" font-family="DejaVu Sans Mono" font-size="14.00">print(&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="321" y="-134.7" font-family="DejaVu Sans Mono" font-size="14.00">===== FINAL PROMPT =====</text>
<text xml:space="preserve" text-anchor="middle" x="504.75" y="-119.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;, bundle.final_prompt)</text>
</g>
<!-- 94&#45;&gt;95 -->
<g id="edge5" class="edge">
<title>94&#45;&gt;95</title>
<path fill="none" stroke="green" d="M504.75,-300.68C504.75,-288.5 504.75,-271.29 504.75,-253.63"/>
<polygon fill="green" stroke="green" points="508.25,-253.83 504.75,-243.83 501.25,-253.83 508.25,-253.83"/>
<text xml:space="preserve" text-anchor="middle" x="582" y="-251.7" font-family="DejaVu Sans Mono" font-size="14.00">__name__ == &#39;__main__&#39;</text>
</g>
<!-- 97 -->
<g id="node5" class="node">
<title>97</title>
<polygon fill="#e552ff" stroke="#e552ff" points="441.62,-75 355.88,-75 355.88,-79 343.88,-79 343.88,-39 441.62,-39 441.62,-75"/>
<polyline fill="none" stroke="#e552ff" points="343.88,-75 355.88,-75"/>
<text xml:space="preserve" text-anchor="middle" x="392.75" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">ProblemDesc</text>
</g>
<!-- 95&#45;&gt;97 -->
<g id="edge2" class="edge">
<title>95&#45;&gt;97</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M445.14,-113.67C435.01,-102.9 425.03,-92.3 416.55,-83.29"/>
<polygon fill="black" stroke="black" points="419.36,-81.16 409.95,-76.28 414.26,-85.96 419.36,-81.16"/>
<text xml:space="preserve" text-anchor="middle" x="448.92" y="-92.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 98 -->
<g id="node6" class="node">
<title>98</title>
<polygon fill="#e552ff" stroke="#e552ff" points="549.5,-75 472,-75 472,-79 460,-79 460,-39 549.5,-39 549.5,-75"/>
<polyline fill="none" stroke="#e552ff" points="460,-75 472,-75"/>
<text xml:space="preserve" text-anchor="middle" x="504.75" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">run_pipeline</text>
</g>
<!-- 95&#45;&gt;98 -->
<g id="edge3" class="edge">
<title>95&#45;&gt;98</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M504.75,-113.67C504.75,-104.12 504.75,-94.71 504.75,-86.41"/>
<polygon fill="black" stroke="black" points="508.25,-86.69 504.75,-76.69 501.25,-86.69 508.25,-86.69"/>
<text xml:space="preserve" text-anchor="middle" x="518.25" y="-92.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 99 -->
<g id="node7" class="node">
<title>99</title>
<polygon fill="#e552ff" stroke="#e552ff" points="621.75,-75 579.75,-75 579.75,-79 567.75,-79 567.75,-39 621.75,-39 621.75,-75"/>
<polyline fill="none" stroke="#e552ff" points="567.75,-75 579.75,-75"/>
<text xml:space="preserve" text-anchor="middle" x="594.75" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 95&#45;&gt;99 -->
<g id="edge4" class="edge">
<title>95&#45;&gt;99</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M552.65,-113.67C560.54,-103.23 568.31,-92.95 574.99,-84.13"/>
<polygon fill="black" stroke="black" points="577.61,-86.47 580.85,-76.38 572.02,-82.25 577.61,-86.47"/>
<text xml:space="preserve" text-anchor="middle" x="581.66" y="-92.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 3 -->
<g id="node8" class="node">
<title>3</title>
<polygon fill="#fffb81" stroke="black" points="1024.75,-923 690.75,-923 690.75,-840 1024.75,-840 1024.75,-923"/>
<text xml:space="preserve" text-anchor="start" x="698.75" y="-905.7" font-family="DejaVu Sans Mono" font-size="14.00">model_config = ConfigDict(strict=True, extra=&#39;forbid&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="698.75" y="-890.7" font-family="DejaVu Sans Mono" font-size="14.00">problem_desc: str</text>
<text xml:space="preserve" text-anchor="start" x="698.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">target_model: str</text>
<text xml:space="preserve" text-anchor="start" x="698.75" y="-860.7" font-family="DejaVu Sans Mono" font-size="14.00">token_limits: dict[str, int]</text>
<text xml:space="preserve" text-anchor="middle" x="857.75" y="-845.7" font-family="DejaVu Sans Mono" font-size="14.00">constraints: List[str]</text>
</g>
<!-- 4 -->
<g id="node9" class="node">
<title>4</title>
<polygon fill="#e552ff" stroke="#e552ff" points="896.88,-337 830.62,-337 830.62,-341 818.62,-341 818.62,-301 896.88,-301 896.88,-337"/>
<polyline fill="none" stroke="#e552ff" points="818.62,-337 830.62,-337"/>
<text xml:space="preserve" text-anchor="middle" x="857.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">ConfigDict</text>
</g>
<!-- 3&#45;&gt;4 -->
<g id="edge7" class="edge">
<title>3&#45;&gt;4</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M857.75,-839.74C857.75,-731.96 857.75,-441.4 857.75,-348.78"/>
<polygon fill="black" stroke="black" points="861.25,-348.81 857.75,-338.81 854.25,-348.81 861.25,-348.81"/>
<text xml:space="preserve" text-anchor="middle" x="871.25" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 7 -->
<g id="node10" class="node">
<title>7</title>
<polygon fill="#fffb81" stroke="black" points="1749.62,-923 1333.88,-923 1333.88,-840 1749.62,-840 1749.62,-923"/>
<text xml:space="preserve" text-anchor="start" x="1341.88" y="-905.7" font-family="DejaVu Sans Mono" font-size="14.00">model_config = ConfigDict(strict=True, validate_assignment=True)</text>
<text xml:space="preserve" text-anchor="start" x="1341.88" y="-890.7" font-family="DejaVu Sans Mono" font-size="14.00">prompt: str = Field(..., min_length=10)</text>
<text xml:space="preserve" text-anchor="start" x="1341.88" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">token_count: int</text>
<text xml:space="preserve" text-anchor="start" x="1341.88" y="-860.7" font-family="DejaVu Sans Mono" font-size="14.00">version: int = 0</text>
<text xml:space="preserve" text-anchor="middle" x="1541.75" y="-845.7" font-family="DejaVu Sans Mono" font-size="14.00">@field_validator(&#39;token_count&#39;)...</text>
</g>
<!-- 8 -->
<g id="node11" class="node">
<title>8</title>
<polygon fill="#e552ff" stroke="#e552ff" points="1537.88,-337 1471.62,-337 1471.62,-341 1459.62,-341 1459.62,-301 1537.88,-301 1537.88,-337"/>
<polyline fill="none" stroke="#e552ff" points="1459.62,-337 1471.62,-337"/>
<text xml:space="preserve" text-anchor="middle" x="1498.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">ConfigDict</text>
</g>
<!-- 7&#45;&gt;8 -->
<g id="edge8" class="edge">
<title>7&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1538.62,-839.74C1530.35,-731.85 1508.02,-440.8 1500.94,-348.5"/>
<polygon fill="black" stroke="black" points="1504.45,-348.51 1500.19,-338.81 1497.47,-349.05 1504.45,-348.51"/>
<text xml:space="preserve" text-anchor="middle" x="1516.72" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 9 -->
<g id="node12" class="node">
<title>9</title>
<polygon fill="#e552ff" stroke="#e552ff" points="1609.75,-337 1567.75,-337 1567.75,-341 1555.75,-341 1555.75,-301 1609.75,-301 1609.75,-337"/>
<polyline fill="none" stroke="#e552ff" points="1555.75,-337 1567.75,-337"/>
<text xml:space="preserve" text-anchor="middle" x="1582.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">Field</text>
</g>
<!-- 7&#45;&gt;9 -->
<g id="edge9" class="edge">
<title>7&#45;&gt;9</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1544.73,-839.74C1552.62,-731.96 1573.87,-441.4 1580.64,-348.78"/>
<polygon fill="black" stroke="black" points="1584.14,-349.04 1581.37,-338.81 1577.15,-348.53 1584.14,-349.04"/>
<text xml:space="preserve" text-anchor="middle" x="1592.74" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 11 -->
<g id="node13" class="node">
<title>11</title>
<polygon fill="#fffb81" stroke="black" points="1316.25,-900.5 1057.25,-900.5 1057.25,-862.5 1316.25,-862.5 1316.25,-900.5"/>
<text xml:space="preserve" text-anchor="start" x="1065.25" y="-883.2" font-family="DejaVu Sans Mono" font-size="14.00">budget = 4096</text>
<text xml:space="preserve" text-anchor="middle" x="1186.75" y="-868.2" font-family="DejaVu Sans Mono" font-size="14.00">assert v &lt;= budget, &#39;token budget blown&#39;</text>
</g>
<!-- 13 -->
<g id="node14" class="node">
<title>13</title>
<polygon fill="#98fb98" stroke="black" points="1245.59,-337 1151.99,-337 1127.91,-301 1221.51,-301 1245.59,-337"/>
<text xml:space="preserve" text-anchor="middle" x="1186.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">return v</text>
</g>
<!-- 11&#45;&gt;13 -->
<g id="edge10" class="edge">
<title>11&#45;&gt;13</title>
<path fill="none" stroke="black" d="M1183.69,-862.4C1172.86,-797.4 1136.45,-569.64 1124.75,-380 1124.34,-373.35 1121.72,-370.94 1124.75,-365 1129.01,-356.65 1135.61,-349.49 1142.96,-343.5"/>
<polygon fill="black" stroke="black" points="1144.61,-346.63 1150.6,-337.89 1140.46,-340.99 1144.61,-346.63"/>
<text xml:space="preserve" text-anchor="middle" x="1160.75" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">v &lt;= budget</text>
</g>
<!-- 18 -->
<g id="node15" class="node">
<title>18</title>
<polygon fill="#fffb81" stroke="black" points="1932,-900.5 1773.5,-900.5 1773.5,-862.5 1932,-862.5 1932,-900.5"/>
<text xml:space="preserve" text-anchor="start" x="1781.5" y="-883.2" font-family="DejaVu Sans Mono" font-size="14.00">test_categories: List[str]</text>
<text xml:space="preserve" text-anchor="middle" x="1852.75" y="-868.2" font-family="DejaVu Sans Mono" font-size="14.00">template_count: int</text>
</g>
<!-- 21 -->
<g id="node16" class="node">
<title>21</title>
<polygon fill="#fffb81" stroke="black" points="2117.88,-899.5 1955.62,-899.5 1955.62,-863.5 2117.88,-863.5 2117.88,-899.5"/>
<text xml:space="preserve" text-anchor="middle" x="2036.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">synthetic_tests: List[dict]</text>
</g>
<!-- 24 -->
<g id="node17" class="node">
<title>24</title>
<polygon fill="#fffb81" stroke="black" points="2329.62,-900.5 2141.88,-900.5 2141.88,-862.5 2329.62,-862.5 2329.62,-900.5"/>
<text xml:space="preserve" text-anchor="start" x="2149.88" y="-883.2" font-family="DejaVu Sans Mono" font-size="14.00">pass_rate: float</text>
<text xml:space="preserve" text-anchor="middle" x="2235.75" y="-868.2" font-family="DejaVu Sans Mono" font-size="14.00">improvement_areas: List[str]</text>
</g>
<!-- 27 -->
<g id="node18" class="node">
<title>27</title>
<polygon fill="#fffb81" stroke="black" points="2514.12,-908 2353.38,-908 2353.38,-855 2514.12,-855 2514.12,-908"/>
<text xml:space="preserve" text-anchor="start" x="2361.38" y="-890.7" font-family="DejaVu Sans Mono" font-size="14.00">final_prompt: str</text>
<text xml:space="preserve" text-anchor="start" x="2361.38" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">test_generation_doc: str</text>
<text xml:space="preserve" text-anchor="middle" x="2433.75" y="-860.7" font-family="DejaVu Sans Mono" font-size="14.00">requirements_doc: str</text>
</g>
<!-- 31 -->
<g id="node19" class="node">
<title>31</title>
<polygon fill="#fffb81" stroke="black" points="3265.88,-908 2785.62,-908 2785.62,-855 3265.88,-855 3265.88,-908"/>
<text xml:space="preserve" text-anchor="start" x="2793.62" y="-890.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;LLM wrapper 3 LOC&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="2793.62" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">tmpl = ChatPromptTemplate.from_messages([(&#39;system&#39;, sys_prompt), (&#39;user&#39;,</text>
<text xml:space="preserve" text-anchor="middle" x="3025.75" y="-860.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;{u}&#39;)])</text>
</g>
<!-- 32 -->
<g id="node20" class="node">
<title>32</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2791.5,-337 2558,-337 2558,-341 2546,-341 2546,-301 2791.5,-301 2791.5,-337"/>
<polyline fill="none" stroke="#e552ff" points="2546,-337 2558,-337"/>
<text xml:space="preserve" text-anchor="middle" x="2668.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">ChatPromptTemplate.from_messages</text>
</g>
<!-- 31&#45;&gt;32 -->
<g id="edge11" class="edge">
<title>31&#45;&gt;32</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M3009.25,-854.59C2949.71,-761.11 2746.29,-441.74 2685.83,-346.82"/>
<polygon fill="black" stroke="black" points="2688.94,-345.19 2680.62,-338.63 2683.04,-348.95 2688.94,-345.19"/>
<text xml:space="preserve" text-anchor="middle" x="2719.38" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 33 -->
<g id="node21" class="node">
<title>33</title>
<polygon fill="#98fb98" stroke="black" points="3737.83,-337 2999.58,-337 2809.67,-301 3547.92,-301 3737.83,-337"/>
<text xml:space="preserve" text-anchor="middle" x="3273.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">return llm.invoke(tmpl.format_prompt(u=user_prompt).to_messages()).content</text>
</g>
<!-- 31&#45;&gt;33 -->
<g id="edge12" class="edge">
<title>31&#45;&gt;33</title>
<path fill="none" stroke="black" d="M3037.21,-854.59C3078.45,-761.4 3219.03,-443.68 3261.5,-347.69"/>
<polygon fill="black" stroke="black" points="3264.61,-349.3 3265.46,-338.74 3258.21,-346.47 3264.61,-349.3"/>
</g>
<!-- 37 -->
<g id="node22" class="node">
<title>37</title>
<polygon fill="#fffb81" stroke="black" points="4333.75,-908 3879.75,-908 3879.75,-855 4333.75,-855 4333.75,-908"/>
<text xml:space="preserve" text-anchor="start" x="3887.75" y="-890.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Step‑1 Prompt Generator&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="3887.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">body = call_llm(&#39;You are a prompt engineer. Output a terse prompt only.&#39;,</text>
<text xml:space="preserve" text-anchor="middle" x="4106.75" y="-860.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;inp.problem_desc)</text>
</g>
<!-- 38 -->
<g id="node23" class="node">
<title>38</title>
<polygon fill="#e552ff" stroke="#e552ff" points="3831.62,-337 3781.88,-337 3781.88,-341 3769.88,-341 3769.88,-301 3831.62,-301 3831.62,-337"/>
<polyline fill="none" stroke="#e552ff" points="3769.88,-337 3781.88,-337"/>
<text xml:space="preserve" text-anchor="middle" x="3800.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 37&#45;&gt;38 -->
<g id="edge13" class="edge">
<title>37&#45;&gt;38</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4092.6,-854.59C4041.67,-761.3 3867.92,-443.03 3815.71,-347.4"/>
<polygon fill="black" stroke="black" points="3818.81,-345.79 3810.95,-338.69 3812.67,-349.14 3818.81,-345.79"/>
<text xml:space="preserve" text-anchor="middle" x="3846.07" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 39 -->
<g id="node24" class="node">
<title>39</title>
<polygon fill="#98fb98" stroke="black" points="4599.49,-337 4003.36,-337 3850.01,-301 4446.14,-301 4599.49,-337"/>
<text xml:space="preserve" text-anchor="middle" x="4224.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">return PromptDoc(prompt=body, token_count=len(body.split()))</text>
</g>
<!-- 37&#45;&gt;39 -->
<g id="edge14" class="edge">
<title>37&#45;&gt;39</title>
<path fill="none" stroke="black" d="M4112.2,-854.59C4131.76,-761.68 4198.31,-445.61 4218.73,-348.57"/>
<polygon fill="black" stroke="black" points="4222.15,-349.34 4220.78,-338.84 4215.3,-347.9 4222.15,-349.34"/>
</g>
<!-- 43 -->
<g id="node25" class="node">
<title>43</title>
<polygon fill="#fffb81" stroke="black" points="5217.38,-900.5 4752.12,-900.5 4752.12,-862.5 5217.38,-862.5 5217.38,-900.5"/>
<text xml:space="preserve" text-anchor="start" x="4760.12" y="-883.2" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Step‑2 Test‑case template generation&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="middle" x="4984.75" y="-868.2" font-family="DejaVu Sans Mono" font-size="14.00">cats = call_llm(&#39;List three JSON category labels only.&#39;, prompt_doc.prompt)</text>
</g>
<!-- 44 -->
<g id="node26" class="node">
<title>44</title>
<polygon fill="#e552ff" stroke="#e552ff" points="4693.62,-337 4643.88,-337 4643.88,-341 4631.88,-341 4631.88,-301 4693.62,-301 4693.62,-337"/>
<polyline fill="none" stroke="#e552ff" points="4631.88,-337 4643.88,-337"/>
<text xml:space="preserve" text-anchor="middle" x="4662.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 43&#45;&gt;44 -->
<g id="edge15" class="edge">
<title>43&#45;&gt;44</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M4974.22,-862.17C4927,-779.98 4734.65,-445.16 4678.32,-347.11"/>
<polygon fill="black" stroke="black" points="4681.49,-345.59 4673.47,-338.67 4675.42,-349.08 4681.49,-345.59"/>
<text xml:space="preserve" text-anchor="middle" x="4709.74" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 45 -->
<g id="node27" class="node">
<title>45</title>
<polygon fill="#98fb98" stroke="black" points="5515.65,-337 4876.32,-337 4711.85,-301 5351.18,-301 5515.65,-337"/>
<text xml:space="preserve" text-anchor="middle" x="5113.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">return TestCaseDoc(test_categories=cats.split(), template_count=3)</text>
</g>
<!-- 43&#45;&gt;45 -->
<g id="edge16" class="edge">
<title>43&#45;&gt;45</title>
<path fill="none" stroke="black" d="M4988.97,-862.17C5007.81,-780.31 5084.32,-447.87 5107.23,-348.31"/>
<polygon fill="black" stroke="black" points="5110.58,-349.36 5109.42,-338.83 5103.76,-347.79 5110.58,-349.36"/>
</g>
<!-- 49 -->
<g id="node28" class="node">
<title>49</title>
<polygon fill="#fffb81" stroke="black" points="6056.38,-908 5633.12,-908 5633.12,-855 6056.38,-855 6056.38,-908"/>
<text xml:space="preserve" text-anchor="start" x="5641.12" y="-890.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Step‑3 Synthetic test expansion&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="5641.12" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">synth = call_llm(&#39;Generate minimal synthetic tests JSON.&#39;, &#39; &#39;.join(tc.</text>
<text xml:space="preserve" text-anchor="middle" x="5844.75" y="-860.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;test_categories))</text>
</g>
<!-- 50 -->
<g id="node29" class="node">
<title>50</title>
<polygon fill="#e552ff" stroke="#e552ff" points="5609.62,-337 5559.88,-337 5559.88,-341 5547.88,-341 5547.88,-301 5609.62,-301 5609.62,-337"/>
<polyline fill="none" stroke="#e552ff" points="5547.88,-337 5559.88,-337"/>
<text xml:space="preserve" text-anchor="middle" x="5578.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 49&#45;&gt;50 -->
<g id="edge17" class="edge">
<title>49&#45;&gt;50</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5832.45,-854.59C5788.23,-761.4 5637.45,-443.68 5591.89,-347.69"/>
<polygon fill="black" stroke="black" points="5595.09,-346.26 5587.64,-338.72 5588.76,-349.26 5595.09,-346.26"/>
<text xml:space="preserve" text-anchor="middle" x="5619.91" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 52 -->
<g id="node31" class="node">
<title>52</title>
<polygon fill="#98fb98" stroke="black" points="6247.46,-337 5754.78,-337 5628.04,-301 6120.72,-301 6247.46,-337"/>
<text xml:space="preserve" text-anchor="middle" x="5937.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">return SyntheticDoc(synthetic_tests=[{&#39;raw&#39;: synth}])</text>
</g>
<!-- 49&#45;&gt;52 -->
<g id="edge19" class="edge">
<title>49&#45;&gt;52</title>
<path fill="none" stroke="black" d="M5849.05,-854.59C5864.47,-761.68 5916.91,-445.61 5933.01,-348.57"/>
<polygon fill="black" stroke="black" points="5936.44,-349.29 5934.62,-338.85 5929.53,-348.14 5936.44,-349.29"/>
</g>
<!-- 51 -->
<g id="node30" class="node">
<title>51</title>
<polygon fill="#e552ff" stroke="#e552ff" points="5605.75,-196 5563.75,-196 5563.75,-200 5551.75,-200 5551.75,-160 5605.75,-160 5605.75,-196"/>
<polyline fill="none" stroke="#e552ff" points="5551.75,-196 5563.75,-196"/>
<text xml:space="preserve" text-anchor="middle" x="5578.75" y="-172.2" font-family="DejaVu Sans Mono" font-size="14.00"> .join</text>
</g>
<!-- 50&#45;&gt;51 -->
<g id="edge18" class="edge">
<title>50&#45;&gt;51</title>
<path fill="none" stroke="black" d="M5578.75,-300.68C5578.75,-277.35 5578.75,-235.53 5578.75,-207.53"/>
<polygon fill="black" stroke="black" points="5582.25,-207.62 5578.75,-197.62 5575.25,-207.62 5582.25,-207.62"/>
</g>
<!-- 56 -->
<g id="node32" class="node">
<title>56</title>
<polygon fill="#fffb81" stroke="black" points="6857,-899.5 6602.5,-899.5 6602.5,-863.5 6857,-863.5 6857,-899.5"/>
<text xml:space="preserve" text-anchor="middle" x="6729.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Step‑4 Execute + Grade (mocked)&quot;&quot;&quot;</text>
</g>
<!-- 57 -->
<g id="node33" class="node">
<title>57</title>
<polygon fill="#98fb98" stroke="black" points="7187.4,-337 6459.38,-337 6272.1,-301 7000.12,-301 7187.4,-337"/>
<text xml:space="preserve" text-anchor="middle" x="6729.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">return GradeDoc(pass_rate=0.7, improvement_areas=[&#39;clarity&#39;, &#39;edge cases&#39;])</text>
</g>
<!-- 56&#45;&gt;57 -->
<g id="edge20" class="edge">
<title>56&#45;&gt;57</title>
<path fill="none" stroke="black" d="M6729.75,-863.11C6729.75,-783.02 6729.75,-449.33 6729.75,-348.79"/>
<polygon fill="black" stroke="black" points="6733.25,-348.98 6729.75,-338.98 6726.25,-348.98 6733.25,-348.98"/>
</g>
<!-- 61 -->
<g id="node34" class="node">
<title>61</title>
<polygon fill="#fffb81" stroke="black" points="7756.62,-930.5 7394.88,-930.5 7394.88,-832.5 7756.62,-832.5 7756.62,-930.5"/>
<text xml:space="preserve" text-anchor="start" x="7402.88" y="-913.2" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Step‑5 Refiner&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="7402.88" y="-898.2" font-family="DejaVu Sans Mono" font-size="14.00">new_body = call_llm(&#39;Improve prompt given weak areas.&#39;,</text>
<text xml:space="preserve" text-anchor="start" x="7402.88" y="-883.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;f&quot;&quot;&quot;PROMPT:</text>
<text xml:space="preserve" text-anchor="start" x="7402.88" y="-868.2" font-family="DejaVu Sans Mono" font-size="14.00">{prompt_doc.prompt}</text>
<text xml:space="preserve" text-anchor="start" x="7402.88" y="-853.2" font-family="DejaVu Sans Mono" font-size="14.00">ISSUES:{&#39;,&#39;.join(grade.improvement_areas)}&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="middle" x="7575.75" y="-838.2" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;)</text>
</g>
<!-- 62 -->
<g id="node35" class="node">
<title>62</title>
<polygon fill="#e552ff" stroke="#e552ff" points="7281.62,-337 7231.88,-337 7231.88,-341 7219.88,-341 7219.88,-301 7281.62,-301 7281.62,-337"/>
<polyline fill="none" stroke="#e552ff" points="7219.88,-337 7231.88,-337"/>
<text xml:space="preserve" text-anchor="middle" x="7250.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 61&#45;&gt;62 -->
<g id="edge21" class="edge">
<title>61&#45;&gt;62</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M7547.76,-832.23C7481.77,-718.42 7318.15,-436.23 7266.58,-347.3"/>
<polygon fill="black" stroke="black" points="7269.66,-345.64 7261.62,-338.75 7263.61,-349.15 7269.66,-345.64"/>
<text xml:space="preserve" text-anchor="middle" x="7298.05" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 64 -->
<g id="node37" class="node">
<title>64</title>
<polygon fill="#98fb98" stroke="black" points="8220.33,-357 7487.65,-357 7299.17,-281 8031.85,-281 8220.33,-357"/>
<text xml:space="preserve" text-anchor="start" x="7383.02" y="-320.7" font-family="DejaVu Sans Mono" font-size="14.00">return prompt_doc.model_copy(update={&#39;prompt&#39;: new_body, &#39;version&#39;: </text>
<text xml:space="preserve" text-anchor="middle" x="7759.75" y="-305.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;prompt_doc.version + 1})</text>
</g>
<!-- 61&#45;&gt;64 -->
<g id="edge23" class="edge">
<title>61&#45;&gt;64</title>
<path fill="none" stroke="black" d="M7591.6,-832.23C7626.41,-726.18 7709.22,-473.92 7743.97,-368.06"/>
<polygon fill="black" stroke="black" points="7747.21,-369.42 7747,-358.83 7740.56,-367.24 7747.21,-369.42"/>
</g>
<!-- 63 -->
<g id="node36" class="node">
<title>63</title>
<polygon fill="#e552ff" stroke="#e552ff" points="7277.75,-196 7235.75,-196 7235.75,-200 7223.75,-200 7223.75,-160 7277.75,-160 7277.75,-196"/>
<polyline fill="none" stroke="#e552ff" points="7223.75,-196 7235.75,-196"/>
<text xml:space="preserve" text-anchor="middle" x="7250.75" y="-172.2" font-family="DejaVu Sans Mono" font-size="14.00">,.join</text>
</g>
<!-- 62&#45;&gt;63 -->
<g id="edge22" class="edge">
<title>62&#45;&gt;63</title>
<path fill="none" stroke="black" d="M7250.75,-300.68C7250.75,-277.35 7250.75,-235.53 7250.75,-207.53"/>
<polygon fill="black" stroke="black" points="7254.25,-207.62 7250.75,-197.62 7247.25,-207.62 7254.25,-207.62"/>
</g>
<!-- 68 -->
<g id="node38" class="node">
<title>68</title>
<polygon fill="#fffb81" stroke="black" points="8904.12,-900.5 8413.38,-900.5 8413.38,-862.5 8904.12,-862.5 8904.12,-900.5"/>
<text xml:space="preserve" text-anchor="start" x="8421.38" y="-883.2" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Step‑6 Token optimiser via LLM compression&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="middle" x="8658.75" y="-868.2" font-family="DejaVu Sans Mono" font-size="14.00">shorter = call_llm(&#39;Compress tokens but keep semantics.&#39;, prompt_doc.prompt)</text>
</g>
<!-- 69 -->
<g id="node39" class="node">
<title>69</title>
<polygon fill="#e552ff" stroke="#e552ff" points="8314.62,-337 8264.88,-337 8264.88,-341 8252.88,-341 8252.88,-301 8314.62,-301 8314.62,-337"/>
<polyline fill="none" stroke="#e552ff" points="8252.88,-337 8264.88,-337"/>
<text xml:space="preserve" text-anchor="middle" x="8283.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 68&#45;&gt;69 -->
<g id="edge24" class="edge">
<title>68&#45;&gt;69</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8646.49,-862.17C8591.44,-779.9 8367.04,-444.49 8301.69,-346.81"/>
<polygon fill="black" stroke="black" points="8304.67,-344.98 8296.2,-338.61 8298.85,-348.87 8304.67,-344.98"/>
<text xml:space="preserve" text-anchor="middle" x="8336.25" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 70 -->
<g id="node40" class="node">
<title>70</title>
<polygon fill="#98fb98" stroke="black" points="9323.32,-357 8534.98,-357 8332.18,-281 9120.52,-281 9323.32,-357"/>
<text xml:space="preserve" text-anchor="start" x="8421.79" y="-320.7" font-family="DejaVu Sans Mono" font-size="14.00">return prompt_doc.model_copy(update={&#39;prompt&#39;: shorter, &#39;token_count&#39;: len(</text>
<text xml:space="preserve" text-anchor="middle" x="8827.75" y="-305.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;shorter.split())})</text>
</g>
<!-- 68&#45;&gt;70 -->
<g id="edge25" class="edge">
<title>68&#45;&gt;70</title>
<path fill="none" stroke="black" d="M8664.28,-862.17C8687.51,-785.13 8777.66,-486.13 8813.21,-368.21"/>
<polygon fill="black" stroke="black" points="8816.51,-369.41 8816.04,-358.82 8809.81,-367.39 8816.51,-369.41"/>
</g>
<!-- 74 -->
<g id="node41" class="node">
<title>74</title>
<polygon fill="#fffb81" stroke="black" points="9918,-899.5 9753.5,-899.5 9753.5,-863.5 9918,-863.5 9918,-899.5"/>
<text xml:space="preserve" text-anchor="middle" x="9835.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Step‑7 Final bundle&quot;&quot;&quot;</text>
</g>
<!-- 75 -->
<g id="node42" class="node">
<title>75</title>
<polygon fill="#98fb98" stroke="black" points="10323.54,-357 9547.57,-357 9347.96,-281 10123.93,-281 10323.54,-357"/>
<text xml:space="preserve" text-anchor="start" x="9436.29" y="-320.7" font-family="DejaVu Sans Mono" font-size="14.00">return FinalBundle(final_prompt=prompt_doc.prompt, test_generation_doc=</text>
<text xml:space="preserve" text-anchor="middle" x="9835.75" y="-305.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;&lt;stub&gt;&#39;, requirements_doc=&#39;&lt;stub&gt;&#39;)</text>
</g>
<!-- 74&#45;&gt;75 -->
<g id="edge26" class="edge">
<title>74&#45;&gt;75</title>
<path fill="none" stroke="black" d="M9835.75,-863.11C9835.75,-787.65 9835.75,-487.01 9835.75,-368.44"/>
<polygon fill="black" stroke="black" points="9839.25,-368.73 9835.75,-358.73 9832.25,-368.73 9839.25,-368.73"/>
</g>
<!-- 79 -->
<g id="node43" class="node">
<title>79</title>
<polygon fill="#fffb81" stroke="black" points="10765,-899.5 10630.5,-899.5 10630.5,-863.5 10765,-863.5 10765,-899.5"/>
<text xml:space="preserve" text-anchor="middle" x="10697.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;functional pipe&quot;&quot;&quot;</text>
</g>
<!-- 80 -->
<g id="node44" class="node">
<title>80</title>
<polygon fill="#98fb98" stroke="black" points="11047.48,-337 10491.14,-337 10348.02,-301 10904.36,-301 11047.48,-337"/>
<text xml:space="preserve" text-anchor="middle" x="10697.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">return lambda x: reduce(lambda acc, fn: fn(acc), stages, x)</text>
</g>
<!-- 79&#45;&gt;80 -->
<g id="edge27" class="edge">
<title>79&#45;&gt;80</title>
<path fill="none" stroke="black" d="M10697.75,-863.11C10697.75,-783.02 10697.75,-449.33 10697.75,-348.79"/>
<polygon fill="black" stroke="black" points="10701.25,-348.98 10697.75,-338.98 10694.25,-348.98 10701.25,-348.98"/>
</g>
<!-- 84 -->
<g id="node45" class="node">
<title>84</title>
<polygon fill="#fffb81" stroke="black" points="11670.38,-938 11265.12,-938 11265.12,-825 11670.38,-825 11670.38,-938"/>
<text xml:space="preserve" text-anchor="start" x="11273.12" y="-920.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Complete pipeline execution with proper state management&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="11273.12" y="-905.7" font-family="DejaVu Sans Mono" font-size="14.00">prompt_doc = gen_prompt(prob)</text>
<text xml:space="preserve" text-anchor="start" x="11273.12" y="-890.7" font-family="DejaVu Sans Mono" font-size="14.00">test_cases = gen_testcases(prompt_doc)</text>
<text xml:space="preserve" text-anchor="start" x="11273.12" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">synthetic = gen_synthetic(test_cases)</text>
<text xml:space="preserve" text-anchor="start" x="11273.12" y="-860.7" font-family="DejaVu Sans Mono" font-size="14.00">grade = grade_tests(synthetic)</text>
<text xml:space="preserve" text-anchor="start" x="11273.12" y="-845.7" font-family="DejaVu Sans Mono" font-size="14.00">refined_prompt = refine_prompt(prompt_doc, grade)</text>
<text xml:space="preserve" text-anchor="middle" x="11467.75" y="-830.7" font-family="DejaVu Sans Mono" font-size="14.00">optimized_prompt = optimise_prompt(refined_prompt)</text>
</g>
<!-- 85 -->
<g id="node46" class="node">
<title>85</title>
<polygon fill="#e552ff" stroke="#e552ff" points="11168.12,-337 11091.38,-337 11091.38,-341 11079.38,-341 11079.38,-301 11168.12,-301 11168.12,-337"/>
<polyline fill="none" stroke="#e552ff" points="11079.38,-337 11091.38,-337"/>
<text xml:space="preserve" text-anchor="middle" x="11123.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">gen_prompt</text>
</g>
<!-- 84&#45;&gt;85 -->
<g id="edge28" class="edge">
<title>84&#45;&gt;85</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M11433.51,-824.71C11360.84,-706.3 11193.49,-433.62 11140.3,-346.96"/>
<polygon fill="black" stroke="black" points="11143.4,-345.32 11135.18,-338.63 11137.43,-348.98 11143.4,-345.32"/>
<text xml:space="preserve" text-anchor="middle" x="11173.03" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 86 -->
<g id="node47" class="node">
<title>86</title>
<polygon fill="#e552ff" stroke="#e552ff" points="11289.25,-337 11198.25,-337 11198.25,-341 11186.25,-341 11186.25,-301 11289.25,-301 11289.25,-337"/>
<polyline fill="none" stroke="#e552ff" points="11186.25,-337 11198.25,-337"/>
<text xml:space="preserve" text-anchor="middle" x="11237.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">gen_testcases</text>
</g>
<!-- 84&#45;&gt;86 -->
<g id="edge29" class="edge">
<title>84&#45;&gt;86</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M11444.86,-824.71C11396.42,-706.66 11285.06,-435.29 11249.14,-347.76"/>
<polygon fill="black" stroke="black" points="11252.47,-346.66 11245.44,-338.74 11246,-349.32 11252.47,-346.66"/>
<text xml:space="preserve" text-anchor="middle" x="11275.17" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 87 -->
<g id="node48" class="node">
<title>87</title>
<polygon fill="#e552ff" stroke="#e552ff" points="11406.38,-337 11319.12,-337 11319.12,-341 11307.12,-341 11307.12,-301 11406.38,-301 11406.38,-337"/>
<polyline fill="none" stroke="#e552ff" points="11307.12,-337 11319.12,-337"/>
<text xml:space="preserve" text-anchor="middle" x="11356.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">gen_synthetic</text>
</g>
<!-- 84&#45;&gt;87 -->
<g id="edge30" class="edge">
<title>84&#45;&gt;87</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M11456.7,-824.71C11433.4,-707.02 11379.91,-436.94 11362.41,-348.57"/>
<polygon fill="black" stroke="black" points="11365.85,-347.95 11360.48,-338.82 11358.99,-349.31 11365.85,-347.95"/>
<text xml:space="preserve" text-anchor="middle" x="11381.79" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 88 -->
<g id="node49" class="node">
<title>88</title>
<polygon fill="#e552ff" stroke="#e552ff" points="11511,-337 11436.5,-337 11436.5,-341 11424.5,-341 11424.5,-301 11511,-301 11511,-337"/>
<polyline fill="none" stroke="#e552ff" points="11424.5,-337 11436.5,-337"/>
<text xml:space="preserve" text-anchor="middle" x="11467.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">grade_tests</text>
</g>
<!-- 84&#45;&gt;88 -->
<g id="edge31" class="edge">
<title>84&#45;&gt;88</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M11467.75,-824.71C11467.75,-707.14 11467.75,-437.5 11467.75,-348.84"/>
<polygon fill="black" stroke="black" points="11471.25,-348.85 11467.75,-338.85 11464.25,-348.85 11471.25,-348.85"/>
<text xml:space="preserve" text-anchor="middle" x="11481.25" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 89 -->
<g id="node50" class="node">
<title>89</title>
<polygon fill="#e552ff" stroke="#e552ff" points="11628.75,-337 11540.75,-337 11540.75,-341 11528.75,-341 11528.75,-301 11628.75,-301 11628.75,-337"/>
<polyline fill="none" stroke="#e552ff" points="11528.75,-337 11540.75,-337"/>
<text xml:space="preserve" text-anchor="middle" x="11578.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">refine_prompt</text>
</g>
<!-- 84&#45;&gt;89 -->
<g id="edge32" class="edge">
<title>84&#45;&gt;89</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M11478.8,-824.71C11502.1,-707.02 11555.59,-436.94 11573.09,-348.57"/>
<polygon fill="black" stroke="black" points="11576.51,-349.31 11575.02,-338.82 11569.65,-347.95 11576.51,-349.31"/>
<text xml:space="preserve" text-anchor="middle" x="11582.76" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 90 -->
<g id="node51" class="node">
<title>90</title>
<polygon fill="#e552ff" stroke="#e552ff" points="11764.38,-337 11659.12,-337 11659.12,-341 11647.12,-341 11647.12,-301 11764.38,-301 11764.38,-337"/>
<polyline fill="none" stroke="#e552ff" points="11647.12,-337 11659.12,-337"/>
<text xml:space="preserve" text-anchor="middle" x="11705.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">optimise_prompt</text>
</g>
<!-- 84&#45;&gt;90 -->
<g id="edge33" class="edge">
<title>84&#45;&gt;90</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M11491.44,-824.71C11541.57,-706.66 11656.8,-435.29 11693.96,-347.76"/>
<polygon fill="black" stroke="black" points="11697.11,-349.3 11697.8,-338.73 11690.67,-346.57 11697.11,-349.3"/>
<text xml:space="preserve" text-anchor="middle" x="11698.89" y="-366.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 91 -->
<g id="node52" class="node">
<title>91</title>
<polygon fill="#98fb98" stroke="black" points="12187.25,-337 11865.12,-337 11782.25,-301 12104.38,-301 12187.25,-337"/>
<text xml:space="preserve" text-anchor="middle" x="11984.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">return deliver(optimized_prompt)</text>
</g>
<!-- 84&#45;&gt;91 -->
<g id="edge34" class="edge">
<title>84&#45;&gt;91</title>
<path fill="none" stroke="black" d="M11519.21,-824.71C11628.98,-705.7 11882.5,-430.85 11961.09,-345.65"/>
<polygon fill="black" stroke="black" points="11963.53,-348.17 11967.73,-338.45 11958.38,-343.43 11963.53,-348.17"/>
</g>
<!-- input -->
<g id="node53" class="node">
<title>input</title>
<polygon fill="#afeeee" stroke="black" points="12296.87,-337 12228.28,-337 12210.63,-301 12279.22,-301 12296.87,-337"/>
<text xml:space="preserve" text-anchor="middle" x="12253.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">input</text>
</g>
<!-- call -->
<g id="node58" class="node">
<title>call</title>
<polygon fill="#e552ff" stroke="black" points="12280.75,-196 12238.75,-196 12238.75,-200 12226.75,-200 12226.75,-160 12280.75,-160 12280.75,-196"/>
<polyline fill="none" stroke="black" points="12226.75,-196 12238.75,-196"/>
<text xml:space="preserve" text-anchor="middle" x="12253.75" y="-172.2" font-family="DejaVu Sans Mono" font-size="14.00">call</text>
</g>
<!-- input&#45;&gt;call -->
<!-- default -->
<g id="node54" class="node">
<title>default</title>
<polygon fill="#fffb81" stroke="black" points="12392,-196 12335.5,-196 12335.5,-160 12392,-160 12392,-196"/>
<text xml:space="preserve" text-anchor="middle" x="12363.75" y="-172.2" font-family="DejaVu Sans Mono" font-size="14.00">default</text>
</g>
<!-- if -->
<g id="node55" class="node">
<title>if</title>
<polygon fill="#ff6752" stroke="black" points="12240.75,-899.5 12213.75,-881.5 12240.75,-863.5 12267.75,-881.5 12240.75,-899.5"/>
<text xml:space="preserve" text-anchor="middle" x="12240.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">if</text>
</g>
<!-- if&#45;&gt;input -->
<!-- for -->
<g id="node56" class="node">
<title>for</title>
<polygon fill="#ffbe52" stroke="black" points="12339.75,-881.5 12326.25,-899.5 12299.25,-899.5 12285.75,-881.5 12299.25,-863.5 12326.25,-863.5 12339.75,-881.5"/>
<text xml:space="preserve" text-anchor="middle" x="12312.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">for</text>
</g>
<!-- return -->
<g id="node59" class="node">
<title>return</title>
<polygon fill="#98fb98" stroke="black" points="12412.59,-337 12334.9,-337 12314.91,-301 12392.6,-301 12412.59,-337"/>
<text xml:space="preserve" text-anchor="middle" x="12363.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">return</text>
</g>
<!-- for&#45;&gt;return -->
<!-- while -->
<g id="node57" class="node">
<title>while</title>
<polygon fill="#ffbe52" stroke="black" points="12427.84,-881.5 12410.29,-899.5 12375.21,-899.5 12357.66,-881.5 12375.21,-863.5 12410.29,-863.5 12427.84,-881.5"/>
<text xml:space="preserve" text-anchor="middle" x="12392.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">while</text>
</g>
<!-- return&#45;&gt;default -->
<!-- try -->
<g id="node60" class="node">
<title>try</title>
<polygon fill="orange" stroke="black" points="12474.75,-899.5 12446.26,-881.5 12474.75,-863.5 12503.24,-881.5 12474.75,-899.5"/>
<polyline fill="none" stroke="black" points="12455.75,-887.5 12455.75,-875.5"/>
<polyline fill="none" stroke="black" points="12465.25,-869.5 12484.25,-869.5"/>
<polyline fill="none" stroke="black" points="12493.75,-875.5 12493.75,-887.5"/>
<polyline fill="none" stroke="black" points="12484.25,-893.5 12465.25,-893.5"/>
<text xml:space="preserve" text-anchor="middle" x="12474.75" y="-875.7" font-family="DejaVu Sans Mono" font-size="14.00">try</text>
</g>
<!-- raise -->
<g id="node61" class="node">
<title>raise</title>
<polygon fill="#98fb98" stroke="black" points="12503.1,-324.56 12466.75,-337 12430.4,-324.56 12430.43,-304.44 12503.07,-304.44 12503.1,-324.56"/>
<text xml:space="preserve" text-anchor="middle" x="12466.75" y="-313.2" font-family="DejaVu Sans Mono" font-size="14.00">raise</text>
</g>
<!-- try&#45;&gt;raise -->
</g>
</svg>
