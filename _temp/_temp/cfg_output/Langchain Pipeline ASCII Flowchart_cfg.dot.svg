<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 13.1.0 (20250701.0955)
 -->
<!-- Title: cluster0/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/Langchain Pipeline ASCII Flowchart_cfg.dot Pages: 1 -->
<svg width="17961pt" height="2163pt"
 viewBox="0.00 0.00 17961.00 2163.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 2158.75)">
<title>cluster0/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/Langchain Pipeline ASCII Flowchart_cfg.dot</title>
<polygon fill="white" stroke="none" points="-4,4 -4,-2158.75 17956.75,-2158.75 17956.75,4 -4,4"/>
<text xml:space="preserve" text-anchor="middle" x="8976.38" y="-5.7" font-family="DejaVu Sans Mono" font-size="14.00">/Users/<USER>/Documents/Code3&#45;import/Github/Auto&#45;promptgen/_temp/_temp/cfg_output/Langchain Pipeline ASCII Flowchart_cfg.dot</text>
<g id="clust1" class="cluster">
<title>cluster_1</title>
<polygon fill="purple" stroke="purple" points="114.75,-432 114.75,-484 329.75,-484 329.75,-432 114.75,-432"/>
</g>
<g id="clust2" class="cluster">
<title>cluster_158</title>
<polygon fill="purple" stroke="purple" points="108.75,-31 108.75,-136 846.75,-136 846.75,-31 108.75,-31"/>
</g>
<g id="clust3" class="cluster">
<title>cluster0_Prompt</title>
<polygon fill="none" stroke="black" points="575.75,-1306.38 575.75,-1398.38 663.75,-1398.38 663.75,-1306.38 575.75,-1306.38"/>
<text xml:space="preserve" text-anchor="middle" x="619.75" y="-1381.08" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<g id="clust4" class="cluster">
<title>cluster0PROMPTS</title>
<polygon fill="none" stroke="black" points="686.75,-424 686.75,-1465.88 1238.75,-1465.88 1238.75,-424 686.75,-424"/>
<text xml:space="preserve" text-anchor="middle" x="962.75" y="-1448.58" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_7</title>
<polygon fill="purple" stroke="purple" points="748.75,-432 748.75,-484 1176.75,-484 1176.75,-432 748.75,-432"/>
</g>
<g id="clust6" class="cluster">
<title>cluster0ProblemDesc</title>
<polygon fill="none" stroke="black" points="1246.75,-424 1246.75,-1413.38 1596.75,-1413.38 1596.75,-424 1246.75,-424"/>
<text xml:space="preserve" text-anchor="middle" x="1421.75" y="-1396.08" font-family="DejaVu Sans Mono" font-size="14.00">ProblemDesc</text>
</g>
<g id="clust7" class="cluster">
<title>cluster_15</title>
<polygon fill="purple" stroke="purple" points="1374.75,-432 1374.75,-484 1468.75,-484 1468.75,-432 1374.75,-432"/>
</g>
<g id="clust8" class="cluster">
<title>cluster0MasterPrompt</title>
<polygon fill="none" stroke="black" points="1604.75,-416 1604.75,-1420.88 2394.75,-1420.88 2394.75,-416 1604.75,-416"/>
<text xml:space="preserve" text-anchor="middle" x="1999.75" y="-1403.58" font-family="DejaVu Sans Mono" font-size="14.00">MasterPrompt</text>
</g>
<g id="clust9" class="cluster">
<title>cluster_19</title>
<polygon fill="purple" stroke="purple" points="2209.75,-432 2209.75,-484 2279.75,-484 2279.75,-432 2209.75,-432"/>
</g>
<g id="clust10" class="cluster">
<title>cluster0current</title>
<polygon fill="none" stroke="black" points="1612.75,-1314.88 1612.75,-1389.88 1912.75,-1389.88 1912.75,-1314.88 1612.75,-1314.88"/>
<text xml:space="preserve" text-anchor="middle" x="1762.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">current</text>
</g>
<g id="clust11" class="cluster">
<title>cluster0add</title>
<polygon fill="none" stroke="black" points="1920.75,-424 1920.75,-1389.88 2092.75,-1389.88 2092.75,-424 1920.75,-424"/>
<text xml:space="preserve" text-anchor="middle" x="2006.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">add</text>
</g>
<g id="clust12" class="cluster">
<title>cluster_26</title>
<polygon fill="purple" stroke="purple" points="1928.75,-432 1928.75,-484 2084.75,-484 2084.75,-432 1928.75,-432"/>
</g>
<g id="clust13" class="cluster">
<title>cluster0PromptDoc</title>
<polygon fill="none" stroke="black" points="2402.75,-424 2402.75,-1420.88 3052.75,-1420.88 3052.75,-424 2402.75,-424"/>
<text xml:space="preserve" text-anchor="middle" x="2727.75" y="-1403.58" font-family="DejaVu Sans Mono" font-size="14.00">PromptDoc</text>
</g>
<g id="clust14" class="cluster">
<title>cluster_31</title>
<polygon fill="purple" stroke="purple" points="2747.75,-432 2747.75,-484 2913.75,-484 2913.75,-432 2747.75,-432"/>
</g>
<g id="clust15" class="cluster">
<title>cluster0_budget</title>
<polygon fill="none" stroke="black" points="2410.75,-432 2410.75,-1389.88 2618.75,-1389.88 2618.75,-432 2410.75,-432"/>
<text xml:space="preserve" text-anchor="middle" x="2514.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">_budget</text>
</g>
<g id="clust16" class="cluster">
<title>cluster0TestCaseDoc</title>
<polygon fill="none" stroke="black" points="3060.75,-1313.88 3060.75,-1390.88 3234.75,-1390.88 3234.75,-1313.88 3060.75,-1313.88"/>
<text xml:space="preserve" text-anchor="middle" x="3147.75" y="-1373.58" font-family="DejaVu Sans Mono" font-size="14.00">TestCaseDoc</text>
</g>
<g id="clust17" class="cluster">
<title>cluster0SyntheticTestCases</title>
<polygon fill="none" stroke="black" points="3242.75,-1314.88 3242.75,-1389.88 3396.75,-1389.88 3396.75,-1314.88 3242.75,-1314.88"/>
<text xml:space="preserve" text-anchor="middle" x="3319.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">SyntheticTestCases</text>
</g>
<g id="clust18" class="cluster">
<title>cluster0SyntheticGraders</title>
<polygon fill="none" stroke="black" points="3404.75,-1314.88 3404.75,-1389.88 3538.75,-1389.88 3538.75,-1314.88 3404.75,-1314.88"/>
<text xml:space="preserve" text-anchor="middle" x="3471.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">SyntheticGraders</text>
</g>
<g id="clust19" class="cluster">
<title>cluster0GradeDoc</title>
<polygon fill="none" stroke="black" points="3546.75,-1313.88 3546.75,-1390.88 3750.75,-1390.88 3750.75,-1313.88 3546.75,-1313.88"/>
<text xml:space="preserve" text-anchor="middle" x="3648.75" y="-1373.58" font-family="DejaVu Sans Mono" font-size="14.00">GradeDoc</text>
</g>
<g id="clust20" class="cluster">
<title>cluster0ScenarioDoc</title>
<polygon fill="none" stroke="black" points="3758.75,-1271.88 3758.75,-1455.88 4414.75,-1455.88 4414.75,-1271.88 3758.75,-1271.88"/>
<text xml:space="preserve" text-anchor="middle" x="4086.75" y="-1438.58" font-family="DejaVu Sans Mono" font-size="14.00">ScenarioDoc</text>
</g>
<g id="clust21" class="cluster">
<title>cluster0_display</title>
<polygon fill="none" stroke="black" points="3766.75,-1279.88 3766.75,-1424.88 4232.75,-1424.88 4232.75,-1279.88 3766.75,-1279.88"/>
<text xml:space="preserve" text-anchor="middle" x="3999.75" y="-1407.58" font-family="DejaVu Sans Mono" font-size="14.00">_display</text>
</g>
<g id="clust22" class="cluster">
<title>cluster0ReqDoc</title>
<polygon fill="none" stroke="black" points="4422.75,-1271.88 4422.75,-1455.88 5114.75,-1455.88 5114.75,-1271.88 4422.75,-1271.88"/>
<text xml:space="preserve" text-anchor="middle" x="4768.75" y="-1438.58" font-family="DejaVu Sans Mono" font-size="14.00">ReqDoc</text>
</g>
<g id="clust23" class="cluster">
<title>cluster1_display</title>
<polygon fill="none" stroke="black" points="4430.75,-1279.88 4430.75,-1424.88 4932.75,-1424.88 4932.75,-1279.88 4430.75,-1279.88"/>
<text xml:space="preserve" text-anchor="middle" x="4681.75" y="-1407.58" font-family="DejaVu Sans Mono" font-size="14.00">_display</text>
</g>
<g id="clust24" class="cluster">
<title>cluster0FinalBundle</title>
<polygon fill="none" stroke="black" points="5122.75,-1306.38 5122.75,-1398.38 5298.75,-1398.38 5298.75,-1306.38 5122.75,-1306.38"/>
<text xml:space="preserve" text-anchor="middle" x="5210.75" y="-1381.08" font-family="DejaVu Sans Mono" font-size="14.00">FinalBundle</text>
</g>
<g id="clust25" class="cluster">
<title>cluster0call_llm</title>
<polygon fill="none" stroke="black" points="5306.75,-424 5306.75,-1398.38 6522.75,-1398.38 6522.75,-424 5306.75,-424"/>
<text xml:space="preserve" text-anchor="middle" x="5914.75" y="-1381.08" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<g id="clust26" class="cluster">
<title>cluster_72</title>
<polygon fill="purple" stroke="purple" points="5314.75,-432 5314.75,-484 5576.75,-484 5576.75,-432 5314.75,-432"/>
</g>
<g id="clust27" class="cluster">
<title>cluster0gen_prompt</title>
<polygon fill="none" stroke="black" points="6530.75,-242 6530.75,-1390.88 7546.75,-1390.88 7546.75,-242 6530.75,-242"/>
<text xml:space="preserve" text-anchor="middle" x="7038.75" y="-1373.58" font-family="DejaVu Sans Mono" font-size="14.00">gen_prompt</text>
</g>
<g id="clust28" class="cluster">
<title>cluster_78</title>
<polygon fill="purple" stroke="purple" points="6538.75,-250 6538.75,-484 6780.75,-484 6780.75,-250 6538.75,-250"/>
</g>
<g id="clust29" class="cluster">
<title>cluster0gen_testcases</title>
<polygon fill="none" stroke="black" points="7554.75,-242 7554.75,-1390.88 8606.75,-1390.88 8606.75,-242 7554.75,-242"/>
<text xml:space="preserve" text-anchor="middle" x="8080.75" y="-1373.58" font-family="DejaVu Sans Mono" font-size="14.00">gen_testcases</text>
</g>
<g id="clust30" class="cluster">
<title>cluster_85</title>
<polygon fill="purple" stroke="purple" points="7562.75,-250 7562.75,-484 7786.75,-484 7786.75,-250 7562.75,-250"/>
</g>
<g id="clust31" class="cluster">
<title>cluster0_synth_task_sync</title>
<polygon fill="none" stroke="black" points="8614.75,-242 8614.75,-1390.88 9164.75,-1390.88 9164.75,-242 8614.75,-242"/>
<text xml:space="preserve" text-anchor="middle" x="8889.75" y="-1373.58" font-family="DejaVu Sans Mono" font-size="14.00">_synth_task_sync</text>
</g>
<g id="clust32" class="cluster">
<title>cluster_92</title>
<polygon fill="purple" stroke="purple" points="8646.75,-250 8646.75,-484 8884.75,-484 8884.75,-250 8646.75,-250"/>
</g>
<g id="clust33" class="cluster">
<title>cluster0gen_synth_TC1s_sync</title>
<polygon fill="none" stroke="black" points="9172.75,-424 9172.75,-1389.88 9927.75,-1389.88 9927.75,-424 9172.75,-424"/>
<text xml:space="preserve" text-anchor="middle" x="9550.25" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">gen_synth_TC1s_sync</text>
</g>
<g id="clust34" class="cluster">
<title>cluster_99</title>
<polygon fill="purple" stroke="purple" points="9180.75,-432 9180.75,-484 9320.75,-484 9320.75,-432 9180.75,-432"/>
</g>
<g id="clust35" class="cluster">
<title>cluster0_grader_task</title>
<polygon fill="none" stroke="black" points="9935.75,-1314.88 9935.75,-1389.88 10365.75,-1389.88 10365.75,-1314.88 9935.75,-1314.88"/>
<text xml:space="preserve" text-anchor="middle" x="10150.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">_grader_task</text>
</g>
<g id="clust36" class="cluster">
<title>cluster0gen_graders</title>
<polygon fill="none" stroke="black" points="10373.75,-242 10373.75,-1389.88 11019.75,-1389.88 11019.75,-242 10373.75,-242"/>
<text xml:space="preserve" text-anchor="middle" x="10696.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">gen_graders</text>
</g>
<g id="clust37" class="cluster">
<title>cluster_109</title>
<polygon fill="purple" stroke="purple" points="10381.75,-250 10381.75,-484 10501.75,-484 10501.75,-250 10381.75,-250"/>
</g>
<g id="clust38" class="cluster">
<title>cluster0grade_tests</title>
<polygon fill="none" stroke="black" points="11027.75,-1314.88 11027.75,-1389.88 11973.75,-1389.88 11973.75,-1314.88 11027.75,-1314.88"/>
<text xml:space="preserve" text-anchor="middle" x="11500.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">grade_tests</text>
</g>
<g id="clust39" class="cluster">
<title>cluster0refine_prompt</title>
<polygon fill="none" stroke="black" points="11981.75,-242 11981.75,-1413.38 13131.75,-1413.38 13131.75,-242 11981.75,-242"/>
<text xml:space="preserve" text-anchor="middle" x="12556.75" y="-1396.08" font-family="DejaVu Sans Mono" font-size="14.00">refine_prompt</text>
</g>
<g id="clust40" class="cluster">
<title>cluster_120</title>
<polygon fill="purple" stroke="purple" points="11989.75,-250 11989.75,-484 12193.75,-484 12193.75,-250 11989.75,-250"/>
</g>
<g id="clust41" class="cluster">
<title>cluster0optimise_prompt</title>
<polygon fill="none" stroke="black" points="13139.75,-242 13139.75,-1390.88 14383.75,-1390.88 14383.75,-242 13139.75,-242"/>
<text xml:space="preserve" text-anchor="middle" x="13761.75" y="-1373.58" font-family="DejaVu Sans Mono" font-size="14.00">optimise_prompt</text>
</g>
<g id="clust42" class="cluster">
<title>cluster_128</title>
<polygon fill="purple" stroke="purple" points="13147.75,-250 13147.75,-484 13375.75,-484 13375.75,-250 13147.75,-250"/>
</g>
<g id="clust43" class="cluster">
<title>cluster0deliver</title>
<polygon fill="none" stroke="black" points="14391.75,-1294.88 14391.75,-1409.88 15383.75,-1409.88 15383.75,-1294.88 14391.75,-1294.88"/>
<text xml:space="preserve" text-anchor="middle" x="14887.75" y="-1392.58" font-family="DejaVu Sans Mono" font-size="14.00">deliver</text>
</g>
<g id="clust44" class="cluster">
<title>cluster0pipe</title>
<polygon fill="none" stroke="black" points="15391.75,-1314.88 15391.75,-1389.88 16107.75,-1389.88 16107.75,-1314.88 15391.75,-1314.88"/>
<text xml:space="preserve" text-anchor="middle" x="15749.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">pipe</text>
</g>
<g id="clust45" class="cluster">
<title>cluster0run_async</title>
<polygon fill="none" stroke="black" points="16115.75,-1314.88 16115.75,-1389.88 16435.75,-1389.88 16435.75,-1314.88 16115.75,-1314.88"/>
<text xml:space="preserve" text-anchor="middle" x="16275.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">run_async</text>
</g>
<g id="clust46" class="cluster">
<title>cluster0run_pipeline_revb</title>
<polygon fill="none" stroke="black" points="16443.75,-424 16443.75,-1428.38 17628.75,-1428.38 17628.75,-424 16443.75,-424"/>
<text xml:space="preserve" text-anchor="middle" x="17036.25" y="-1411.08" font-family="DejaVu Sans Mono" font-size="14.00">run_pipeline_revb</text>
</g>
<g id="clust47" class="cluster">
<title>cluster_147</title>
<polygon fill="purple" stroke="purple" points="16451.75,-432 16451.75,-484 17206.75,-484 17206.75,-432 16451.75,-432"/>
</g>
<g id="clust48" class="cluster">
<title>cluster_KEY</title>
<polygon fill="none" stroke="black" points="17636.75,-250 17636.75,-1389.88 17944.75,-1389.88 17944.75,-250 17636.75,-250"/>
<text xml:space="preserve" text-anchor="middle" x="17790.75" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00">KEY</text>
</g>
<!-- 1 -->
<g id="node1" class="node">
<title>1</title>
<polygon fill="#fffb81" stroke="black" points="551.5,-2154.75 0,-2154.75 0,-527 551.5,-527 551.5,-2154.75"/>
<text xml:space="preserve" text-anchor="start" x="8" y="-2137.45" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-2122.45" font-family="DejaVu Sans Mono" font-size="14.00">aa_langchain_pipeline.py &#160;📜 &#160;(rev‑B)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-2103.7" font-family="DejaVu Sans Mono" font-size="14.00">=========================</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-2088.7" font-family="DejaVu Sans Mono" font-size="14.00">FCIS, Duplo‑block LangChain pipeline w/ PROMPTS singleton &amp; async fan‑out.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-2073.7" font-family="DejaVu Sans Mono" font-size="14.00">Pyramid bullets (why):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-2058.7" font-family="DejaVu Sans Mono" font-size="14.00">1. Swap call_llm signature → (micro, sys, usr) for readability.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-2040.7" font-family="DejaVu Sans Mono" font-size="14.00">2. Centralise prompts in PROMPTS singleton (micro+sys+usr fields).</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-2025.7" font-family="DejaVu Sans Mono" font-size="14.00">3. Rename SyntheticDoc → SyntheticTestCases; add SyntheticGraders placeholder.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-2007.7" font-family="DejaVu Sans Mono" font-size="14.00">4. Provide async fan‑out helpers for synthetic TC1s &amp; grader gens.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1992.7" font-family="DejaVu Sans Mono" font-size="14.00">5. Add ScenarioDoc &amp; ReqDoc w/ _display() to render Markdown.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1977.7" font-family="DejaVu Sans Mono" font-size="14.00">6. Introduce MasterPrompt to store iterative versions.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1962.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1947.7" font-family="DejaVu Sans Mono" font-size="14.00">from __future__ import annotations</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1932.7" font-family="DejaVu Sans Mono" font-size="14.00">import asyncio</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1917.7" font-family="DejaVu Sans Mono" font-size="14.00">from functools import reduce</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1902.7" font-family="DejaVu Sans Mono" font-size="14.00">from typing import Callable, List</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1887.7" font-family="DejaVu Sans Mono" font-size="14.00">from dotenv import load_dotenv</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1872.7" font-family="DejaVu Sans Mono" font-size="14.00">from langchain.prompts import ChatPromptTemplate</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1857.7" font-family="DejaVu Sans Mono" font-size="14.00">from langchain_openai import ChatOpenAI</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1842.7" font-family="DejaVu Sans Mono" font-size="14.00">from pydantic import BaseModel, ConfigDict, Field, field_validator</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1827.7" font-family="DejaVu Sans Mono" font-size="14.00">load_dotenv()</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1812.7" font-family="DejaVu Sans Mono" font-size="14.00">class _Prompt(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1797.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;micro: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1782.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;sys: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1767.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;usr: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1752.7" font-family="DejaVu Sans Mono" font-size="14.00">class PROMPTS:</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1737.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&quot;&quot;&quot;PROMPTS singleton that contains predefined prompt templates for various tasks.&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1722.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;gen_prompt = _Prompt(micro=&#39;oner: craft‑initial‑prompt‑terse&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1707.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;You are a prompt engineer. Output a terse prompt only.&#39;, usr=</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1692.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;{problem_desc}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1677.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;test_cats = _Prompt(micro=&#39;oner: list‑3‑json‑categories&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1662.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;List three JSON category labels only.&#39;, usr=&#39;{prompt}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1647.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;synth_tests = _Prompt(micro=&#39;oner: expand‑synthetic‑tests‑json&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1632.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;Generate minimal synthetic tests JSON.&#39;, usr=&#39;{cats}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1617.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;refine = _Prompt(micro=&#39;oner: refine‑prompt‑using‑weak‑areas&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1602.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;Improve prompt given weak areas.&#39;, usr=&#39;{payload}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1587.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;compress = _Prompt(micro=&#39;oner: compress‑prompt‑token‑wise&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1572.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#39;Compress tokens but keep semantics.&#39;, usr=&#39;{prompt}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1557.7" font-family="DejaVu Sans Mono" font-size="14.00">class ProblemDesc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1542.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;model_config = ConfigDict(strict=True, extra=&#39;forbid&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1527.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;problem_desc: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1512.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;target_model: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1497.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;token_limits: dict[str, int]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1482.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;constraints: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1467.7" font-family="DejaVu Sans Mono" font-size="14.00">class MasterPrompt(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1452.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;versions: List[str] = Field(default_factory=list)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1421.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def current(self) &#45;&gt;str:</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1406.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return self.versions[&#45;1]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1375.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def add(self, p: str):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1360.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;self.versions.append(p)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1345.7" font-family="DejaVu Sans Mono" font-size="14.00">class PromptDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1330.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;model_config = ConfigDict(strict=True, validate_assignment=True)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1315.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;prompt: str = Field(..., min_length=10)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1300.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;token_count: int</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1285.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;version: int = 0</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1254.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;@field_validator(&#39;token_count&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1239.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def _budget(cls, v):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1224.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;assert v &gt; 0, &#39;empty prompt?&#39;</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1209.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return v</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1194.7" font-family="DejaVu Sans Mono" font-size="14.00">class TestCaseDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1179.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;test_categories: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1164.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;template_count: int</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1149.7" font-family="DejaVu Sans Mono" font-size="14.00">class SyntheticTestCases(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1134.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;test_cases: List[dict]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1119.7" font-family="DejaVu Sans Mono" font-size="14.00">class SyntheticGraders(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1104.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;graders: List[dict]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1089.7" font-family="DejaVu Sans Mono" font-size="14.00">class GradeDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1074.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;pass_rate: float</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1059.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;improvement_areas: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1044.7" font-family="DejaVu Sans Mono" font-size="14.00">class ScenarioDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-1029.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;scenarios: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-998.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def _display(self) &#45;&gt;str:</text>
<text xml:space="preserve" text-anchor="middle" x="275.75" y="-983.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return &#39;## Scenarios</text>
<text xml:space="preserve" text-anchor="middle" x="275.75" y="-968.7" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-953.7" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {s}&#39; for s in self.scenarios)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-938.7" font-family="DejaVu Sans Mono" font-size="14.00">class ReqDoc(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-923.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;requirements: List[str]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-892.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;def _display(self) &#45;&gt;str:</text>
<text xml:space="preserve" text-anchor="middle" x="275.75" y="-877.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;return &#39;## Requirements</text>
<text xml:space="preserve" text-anchor="middle" x="275.75" y="-862.7" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-847.7" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {r}&#39; for r in self.</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-832.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;requirements)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-817.7" font-family="DejaVu Sans Mono" font-size="14.00">class FinalBundle(BaseModel):</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-802.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;final_prompt: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-787.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;test_generation_doc: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-772.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;requirements_doc: str</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-757.7" font-family="DejaVu Sans Mono" font-size="14.00">llm = ChatOpenAI(model=&#39;gpt&#45;4o&#45;mini&#39;, temperature=0)</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-742.7" font-family="DejaVu Sans Mono" font-size="14.00">def call_llm(micro: str, sys_prompt: str, user_prompt: str) &#45;&gt;str:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-727.7" font-family="DejaVu Sans Mono" font-size="14.00">def gen_prompt(inp: ProblemDesc) &#45;&gt;PromptDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-712.7" font-family="DejaVu Sans Mono" font-size="14.00">def gen_testcases(prompt_doc: PromptDoc) &#45;&gt;TestCaseDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-697.7" font-family="DejaVu Sans Mono" font-size="14.00">def _synth_task_sync(cat: str) &#45;&gt;dict:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-682.7" font-family="DejaVu Sans Mono" font-size="14.00">def gen_synth_TC1s_sync(tc_doc: TestCaseDoc) &#45;&gt;SyntheticTestCases:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-667.7" font-family="DejaVu Sans Mono" font-size="14.00">async def _grader_task(req: str) &#45;&gt;dict:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-652.7" font-family="DejaVu Sans Mono" font-size="14.00">async def gen_graders(req_doc: ReqDoc) &#45;&gt;SyntheticGraders:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-637.7" font-family="DejaVu Sans Mono" font-size="14.00">def grade_tests(synth: SyntheticTestCases) &#45;&gt;GradeDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-622.7" font-family="DejaVu Sans Mono" font-size="14.00">def refine_prompt(prompt_doc: PromptDoc, grade: GradeDoc) &#45;&gt;PromptDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-607.7" font-family="DejaVu Sans Mono" font-size="14.00">def optimise_prompt(prompt_doc: PromptDoc) &#45;&gt;PromptDoc:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-592.7" font-family="DejaVu Sans Mono" font-size="14.00">def deliver(prompt_doc: PromptDoc) &#45;&gt;FinalBundle:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-577.7" font-family="DejaVu Sans Mono" font-size="14.00">Stage = Callable[[BaseModel], BaseModel]</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-562.7" font-family="DejaVu Sans Mono" font-size="14.00">def pipe(*stages: Stage) &#45;&gt;Stage:...</text>
<text xml:space="preserve" text-anchor="start" x="8" y="-547.7" font-family="DejaVu Sans Mono" font-size="14.00">def run_async(coro):...</text>
<text xml:space="preserve" text-anchor="middle" x="275.75" y="-532.7" font-family="DejaVu Sans Mono" font-size="14.00">def run_pipeline_revb(prob: ProblemDesc) &#45;&gt;FinalBundle:...</text>
</g>
<!-- 2 -->
<g id="node2" class="node">
<title>2</title>
<polygon fill="#e552ff" stroke="#e552ff" points="212.5,-476 135,-476 135,-480 123,-480 123,-440 212.5,-440 212.5,-476"/>
<polyline fill="none" stroke="#e552ff" points="123,-476 135,-476"/>
<text xml:space="preserve" text-anchor="middle" x="167.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">load_dotenv</text>
</g>
<!-- 1&#45;&gt;2 -->
<g id="edge1" class="edge">
<title>1&#45;&gt;2</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M176.05,-526.66C174.16,-511.25 172.54,-498.07 171.25,-487.55"/>
<polygon fill="black" stroke="black" points="174.77,-487.46 170.07,-477.96 167.82,-488.31 174.77,-487.46"/>
<text xml:space="preserve" text-anchor="middle" x="187.81" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 70 -->
<g id="node3" class="node">
<title>70</title>
<polygon fill="#e552ff" stroke="#e552ff" points="321.25,-476 242.25,-476 242.25,-480 230.25,-480 230.25,-440 321.25,-440 321.25,-476"/>
<polyline fill="none" stroke="#e552ff" points="230.25,-476 242.25,-476"/>
<text xml:space="preserve" text-anchor="middle" x="275.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">ChatOpenAI</text>
</g>
<!-- 1&#45;&gt;70 -->
<g id="edge2" class="edge">
<title>1&#45;&gt;70</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M275.75,-526.66C275.75,-511.41 275.75,-498.36 275.75,-487.9"/>
<polygon fill="black" stroke="black" points="279.25,-487.97 275.75,-477.97 272.25,-487.97 279.25,-487.97"/>
<text xml:space="preserve" text-anchor="middle" x="289.25" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 157 -->
<g id="node4" class="node">
<title>157</title>
<polygon fill="#ff6752" stroke="black" points="508.75,-476 338.94,-458 508.75,-440 678.56,-458 508.75,-476"/>
<text xml:space="preserve" text-anchor="middle" x="508.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">if __name__ == &#39;__main__&#39;:</text>
</g>
<!-- 1&#45;&gt;157 -->
<g id="edge15" class="edge">
<title>1&#45;&gt;157</title>
<path fill="none" stroke="black" d="M490.85,-526.66C495.04,-510.84 498.6,-497.38 501.42,-486.73"/>
<polygon fill="black" stroke="black" points="504.71,-487.95 503.89,-477.38 497.95,-486.16 504.71,-487.95"/>
</g>
<!-- 158 -->
<g id="node5" class="node">
<title>158</title>
<polygon fill="#fffb81" stroke="black" points="745.5,-385 272,-385 272,-167 745.5,-167 745.5,-385"/>
<text xml:space="preserve" text-anchor="start" x="280" y="-367.7" font-family="DejaVu Sans Mono" font-size="14.00">prob = ProblemDesc(problem_desc=</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-352.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;Summarise legal contract clauses for risk.&#39;, target_model=</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-337.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;gpt&#45;4o&#45;mini&#39;, token_limits={&#39;hard&#39;: 4096}, constraints=[</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-322.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;no hallucinations&#39;, &#39;&lt;=2000 tokens&#39;])</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-307.7" font-family="DejaVu Sans Mono" font-size="14.00">bundle = run_pipeline_revb(prob)</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-292.7" font-family="DejaVu Sans Mono" font-size="14.00">print(&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-277.7" font-family="DejaVu Sans Mono" font-size="14.00">===== FINAL PROMPT =====</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-262.7" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;, bundle.final_prompt)</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-247.7" font-family="DejaVu Sans Mono" font-size="14.00">scen_doc = ScenarioDoc(scenarios=[&#39;sue for breach&#39;, &#39;transfer risk&#39;,</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-232.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;terminate early&#39;])</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-217.7" font-family="DejaVu Sans Mono" font-size="14.00">req_doc = ReqDoc(requirements=[&#39;accuracy&gt;90%&#39;, &#39;explain liability&#39;, &#39;short&#39;])</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-202.7" font-family="DejaVu Sans Mono" font-size="14.00">print(scen_doc._display())</text>
<text xml:space="preserve" text-anchor="start" x="280" y="-187.7" font-family="DejaVu Sans Mono" font-size="14.00">print(req_doc._display())</text>
<text xml:space="preserve" text-anchor="middle" x="508.75" y="-172.7" font-family="DejaVu Sans Mono" font-size="14.00">print(req_doc._display())</text>
</g>
<!-- 157&#45;&gt;158 -->
<g id="edge14" class="edge">
<title>157&#45;&gt;158</title>
<path fill="none" stroke="green" d="M508.75,-439.74C508.75,-428.77 508.75,-413.49 508.75,-396.65"/>
<polygon fill="green" stroke="green" points="512.25,-396.9 508.75,-386.9 505.25,-396.9 512.25,-396.9"/>
<text xml:space="preserve" text-anchor="middle" x="586" y="-394.7" font-family="DejaVu Sans Mono" font-size="14.00">__name__ == &#39;__main__&#39;</text>
</g>
<!-- 160 -->
<g id="node6" class="node">
<title>160</title>
<polygon fill="#e552ff" stroke="#e552ff" points="214.62,-128 128.88,-128 128.88,-132 116.88,-132 116.88,-92 214.62,-92 214.62,-128"/>
<polyline fill="none" stroke="#e552ff" points="116.88,-128 128.88,-128"/>
<text xml:space="preserve" text-anchor="middle" x="165.75" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">ProblemDesc</text>
</g>
<!-- 158&#45;&gt;160 -->
<g id="edge3" class="edge">
<title>158&#45;&gt;160</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M281.87,-166.52C255.91,-154.11 231.84,-142.6 212.43,-133.32"/>
<polygon fill="black" stroke="black" points="214.04,-130.21 203.51,-129.05 211.02,-136.52 214.04,-130.21"/>
<text xml:space="preserve" text-anchor="middle" x="271.24" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 161 -->
<g id="node7" class="node">
<title>161</title>
<polygon fill="#e552ff" stroke="#e552ff" points="356.38,-128 245.12,-128 245.12,-132 233.12,-132 233.12,-92 356.38,-92 356.38,-128"/>
<polyline fill="none" stroke="#e552ff" points="233.12,-128 245.12,-128"/>
<text xml:space="preserve" text-anchor="middle" x="294.75" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">run_pipeline_revb</text>
</g>
<!-- 158&#45;&gt;161 -->
<g id="edge4" class="edge">
<title>158&#45;&gt;161</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M367.2,-166.52C351.99,-154.87 337.82,-144.01 326.12,-135.04"/>
<polygon fill="black" stroke="black" points="328.57,-132.5 318.5,-129.2 324.31,-138.06 328.57,-132.5"/>
<text xml:space="preserve" text-anchor="middle" x="365.64" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 162 -->
<g id="node8" class="node">
<title>162</title>
<polygon fill="#e552ff" stroke="#e552ff" points="428.75,-128 386.75,-128 386.75,-132 374.75,-132 374.75,-92 428.75,-92 428.75,-128"/>
<polyline fill="none" stroke="#e552ff" points="374.75,-128 386.75,-128"/>
<text xml:space="preserve" text-anchor="middle" x="401.75" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 158&#45;&gt;162 -->
<g id="edge5" class="edge">
<title>158&#45;&gt;162</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M437.98,-166.52C431.12,-156.02 424.69,-146.17 419.21,-137.76"/>
<polygon fill="black" stroke="black" points="422.25,-136.01 413.85,-129.55 416.38,-139.83 422.25,-136.01"/>
<text xml:space="preserve" text-anchor="middle" x="443.95" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 163 -->
<g id="node9" class="node">
<title>163</title>
<polygon fill="#e552ff" stroke="#e552ff" points="540.38,-128 459.12,-128 459.12,-132 447.12,-132 447.12,-92 540.38,-92 540.38,-128"/>
<polyline fill="none" stroke="#e552ff" points="447.12,-128 459.12,-128"/>
<text xml:space="preserve" text-anchor="middle" x="493.75" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">ScenarioDoc</text>
</g>
<!-- 158&#45;&gt;163 -->
<g id="edge6" class="edge">
<title>158&#45;&gt;163</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M498.83,-166.52C497.94,-156.83 497.11,-147.69 496.38,-139.73"/>
<polygon fill="black" stroke="black" points="499.86,-139.43 495.47,-129.79 492.89,-140.06 499.86,-139.43"/>
<text xml:space="preserve" text-anchor="middle" x="511.27" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 164 -->
<g id="node10" class="node">
<title>164</title>
<polygon fill="#e552ff" stroke="#e552ff" points="623.12,-128 570.38,-128 570.38,-132 558.38,-132 558.38,-92 623.12,-92 623.12,-128"/>
<polyline fill="none" stroke="#e552ff" points="558.38,-128 570.38,-128"/>
<text xml:space="preserve" text-anchor="middle" x="590.75" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">ReqDoc</text>
</g>
<!-- 158&#45;&gt;164 -->
<g id="edge7" class="edge">
<title>158&#45;&gt;164</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M562.99,-166.52C568.12,-156.25 572.95,-146.6 577.09,-138.32"/>
<polygon fill="black" stroke="black" points="580.09,-140.14 581.43,-129.63 573.83,-137.01 580.09,-140.14"/>
<text xml:space="preserve" text-anchor="middle" x="586.54" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 165 -->
<g id="node11" class="node">
<title>165</title>
<polygon fill="#e552ff" stroke="#e552ff" points="694.75,-128 652.75,-128 652.75,-132 640.75,-132 640.75,-92 694.75,-92 694.75,-128"/>
<polyline fill="none" stroke="#e552ff" points="640.75,-128 652.75,-128"/>
<text xml:space="preserve" text-anchor="middle" x="667.75" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 158&#45;&gt;165 -->
<g id="edge8" class="edge">
<title>158&#45;&gt;165</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M613.92,-166.52C624.66,-155.44 634.7,-145.09 643.14,-136.39"/>
<polygon fill="black" stroke="black" points="645.5,-138.98 649.94,-129.37 640.47,-134.11 645.5,-138.98"/>
<text xml:space="preserve" text-anchor="middle" x="646.91" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 167 -->
<g id="node12" class="node">
<title>167</title>
<polygon fill="#e552ff" stroke="#e552ff" points="766.75,-128 724.75,-128 724.75,-132 712.75,-132 712.75,-92 766.75,-92 766.75,-128"/>
<polyline fill="none" stroke="#e552ff" points="712.75,-128 724.75,-128"/>
<text xml:space="preserve" text-anchor="middle" x="739.75" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 158&#45;&gt;167 -->
<g id="edge9" class="edge">
<title>158&#45;&gt;167</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M661.54,-166.52C678.13,-154.75 693.56,-143.79 706.26,-134.78"/>
<polygon fill="black" stroke="black" points="708.05,-137.8 714.18,-129.16 704,-132.09 708.05,-137.8"/>
<text xml:space="preserve" text-anchor="middle" x="703.35" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 169 -->
<g id="node13" class="node">
<title>169</title>
<polygon fill="#e552ff" stroke="#e552ff" points="838.75,-128 796.75,-128 796.75,-132 784.75,-132 784.75,-92 838.75,-92 838.75,-128"/>
<polyline fill="none" stroke="#e552ff" points="784.75,-128 796.75,-128"/>
<text xml:space="preserve" text-anchor="middle" x="811.75" y="-104.2" font-family="DejaVu Sans Mono" font-size="14.00">print</text>
</g>
<!-- 158&#45;&gt;169 -->
<g id="edge10" class="edge">
<title>158&#45;&gt;169</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M722.87,-166.62C740.92,-156.55 758.75,-146.27 775.75,-136 776.6,-135.48 777.47,-134.95 778.33,-134.42"/>
<polygon fill="black" stroke="black" points="779.88,-137.58 786.36,-129.2 776.07,-131.71 779.88,-137.58"/>
<text xml:space="preserve" text-anchor="middle" x="775.34" y="-145.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 166 -->
<g id="node14" class="node">
<title>166</title>
<polygon fill="#e552ff" stroke="#e552ff" points="566.25,-75 451.25,-75 451.25,-79 439.25,-79 439.25,-39 566.25,-39 566.25,-75"/>
<polyline fill="none" stroke="#e552ff" points="439.25,-75 451.25,-75"/>
<text xml:space="preserve" text-anchor="middle" x="502.75" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">scen_doc._display</text>
</g>
<!-- 165&#45;&gt;166 -->
<g id="edge11" class="edge">
<title>165&#45;&gt;166</title>
<path fill="none" stroke="black" d="M640.45,-95.48C637.55,-94.23 634.61,-93.04 631.75,-92 621.14,-88.13 599.88,-82.28 577.64,-76.51"/>
<polygon fill="black" stroke="black" points="578.71,-73.17 568.15,-74.07 576.96,-79.95 578.71,-73.17"/>
</g>
<!-- 168 -->
<g id="node15" class="node">
<title>168</title>
<polygon fill="#e552ff" stroke="#e552ff" points="702.75,-75 596.75,-75 596.75,-79 584.75,-79 584.75,-39 702.75,-39 702.75,-75"/>
<polyline fill="none" stroke="#e552ff" points="584.75,-75 596.75,-75"/>
<text xml:space="preserve" text-anchor="middle" x="643.75" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">req_doc._display</text>
</g>
<!-- 167&#45;&gt;168 -->
<g id="edge12" class="edge">
<title>167&#45;&gt;168</title>
<path fill="none" stroke="black" d="M712.5,-94.52C704.45,-90.25 695.44,-85.46 686.66,-80.79"/>
<polygon fill="black" stroke="black" points="688.33,-77.72 677.86,-76.12 685.05,-83.9 688.33,-77.72"/>
</g>
<!-- 170 -->
<g id="node16" class="node">
<title>170</title>
<polygon fill="#e552ff" stroke="#e552ff" points="838.75,-75 732.75,-75 732.75,-79 720.75,-79 720.75,-39 838.75,-39 838.75,-75"/>
<polyline fill="none" stroke="#e552ff" points="720.75,-75 732.75,-75"/>
<text xml:space="preserve" text-anchor="middle" x="779.75" y="-51.2" font-family="DejaVu Sans Mono" font-size="14.00">req_doc._display</text>
</g>
<!-- 169&#45;&gt;170 -->
<g id="edge13" class="edge">
<title>169&#45;&gt;170</title>
<path fill="none" stroke="black" d="M800.92,-91.73C799.51,-89.49 798.04,-87.15 796.57,-84.8"/>
<polygon fill="black" stroke="black" points="799.66,-83.14 791.38,-76.53 793.73,-86.86 799.66,-83.14"/>
</g>
<!-- 4 -->
<g id="node17" class="node">
<title>4</title>
<polygon fill="#fffb81" stroke="black" points="655.88,-1367.38 583.62,-1367.38 583.62,-1314.38 655.88,-1314.38 655.88,-1367.38"/>
<text xml:space="preserve" text-anchor="start" x="591.62" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">micro: str</text>
<text xml:space="preserve" text-anchor="start" x="591.62" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">sys: str</text>
<text xml:space="preserve" text-anchor="middle" x="619.75" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00">usr: str</text>
</g>
<!-- 7 -->
<g id="node18" class="node">
<title>7</title>
<polygon fill="#fffb81" stroke="black" points="1231,-1434.88 694.5,-1434.88 694.5,-1246.88 1231,-1246.88 1231,-1434.88"/>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1417.58" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;PROMPTS singleton that contains predefined prompt templates for various tasks.&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1402.58" font-family="DejaVu Sans Mono" font-size="14.00">gen_prompt = _Prompt(micro=&#39;oner: craft‑initial‑prompt‑terse&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1387.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;You are a prompt engineer. Output a terse prompt only.&#39;, usr=</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1372.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;{problem_desc}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1357.58" font-family="DejaVu Sans Mono" font-size="14.00">test_cats = _Prompt(micro=&#39;oner: list‑3‑json‑categories&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;List three JSON category labels only.&#39;, usr=&#39;{prompt}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00">synth_tests = _Prompt(micro=&#39;oner: expand‑synthetic‑tests‑json&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1312.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;Generate minimal synthetic tests JSON.&#39;, usr=&#39;{cats}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1297.58" font-family="DejaVu Sans Mono" font-size="14.00">refine = _Prompt(micro=&#39;oner: refine‑prompt‑using‑weak‑areas&#39;, sys=</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1282.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;Improve prompt given weak areas.&#39;, usr=&#39;{payload}&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="702.5" y="-1267.58" font-family="DejaVu Sans Mono" font-size="14.00">compress = _Prompt(micro=&#39;oner: compress‑prompt‑token‑wise&#39;, sys=</text>
<text xml:space="preserve" text-anchor="middle" x="962.75" y="-1252.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;Compress tokens but keep semantics.&#39;, usr=&#39;{prompt}&#39;)</text>
</g>
<!-- 8 -->
<g id="node19" class="node">
<title>8</title>
<polygon fill="#e552ff" stroke="#e552ff" points="824.62,-476 768.88,-476 768.88,-480 756.88,-480 756.88,-440 824.62,-440 824.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="756.88,-476 768.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="790.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<!-- 7&#45;&gt;8 -->
<g id="edge16" class="edge">
<title>7&#45;&gt;8</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M944.51,-1246.44C906.22,-1050.36 819.14,-604.41 796.31,-487.5"/>
<polygon fill="black" stroke="black" points="799.78,-486.98 794.43,-477.83 792.91,-488.32 799.78,-486.98"/>
<text xml:space="preserve" text-anchor="middle" x="814.7" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 9 -->
<g id="node20" class="node">
<title>9</title>
<polygon fill="#e552ff" stroke="#e552ff" points="910.62,-476 854.88,-476 854.88,-480 842.88,-480 842.88,-440 910.62,-440 910.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="842.88,-476 854.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="876.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<!-- 7&#45;&gt;9 -->
<g id="edge17" class="edge">
<title>7&#45;&gt;9</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M953.63,-1246.44C934.48,-1050.36 890.95,-604.41 879.53,-487.5"/>
<polygon fill="black" stroke="black" points="883.05,-487.47 878.59,-477.85 876.08,-488.15 883.05,-487.47"/>
<text xml:space="preserve" text-anchor="middle" x="895.47" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 10 -->
<g id="node21" class="node">
<title>10</title>
<polygon fill="#e552ff" stroke="#e552ff" points="996.62,-476 940.88,-476 940.88,-480 928.88,-480 928.88,-440 996.62,-440 996.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="928.88,-476 940.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="962.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<!-- 7&#45;&gt;10 -->
<g id="edge18" class="edge">
<title>7&#45;&gt;10</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M962.75,-1246.44C962.75,-1050.56 962.75,-605.32 962.75,-487.85"/>
<polygon fill="black" stroke="black" points="966.25,-487.86 962.75,-477.86 959.25,-487.86 966.25,-487.86"/>
<text xml:space="preserve" text-anchor="middle" x="976.25" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 11 -->
<g id="node22" class="node">
<title>11</title>
<polygon fill="#e552ff" stroke="#e552ff" points="1082.62,-476 1026.88,-476 1026.88,-480 1014.88,-480 1014.88,-440 1082.62,-440 1082.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="1014.88,-476 1026.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="1048.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<!-- 7&#45;&gt;11 -->
<g id="edge19" class="edge">
<title>7&#45;&gt;11</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M971.87,-1246.44C991.02,-1050.36 1034.55,-604.41 1045.97,-487.5"/>
<polygon fill="black" stroke="black" points="1049.42,-488.15 1046.91,-477.85 1042.45,-487.47 1049.42,-488.15"/>
<text xml:space="preserve" text-anchor="middle" x="1057.67" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 12 -->
<g id="node23" class="node">
<title>12</title>
<polygon fill="#e552ff" stroke="#e552ff" points="1168.62,-476 1112.88,-476 1112.88,-480 1100.88,-480 1100.88,-440 1168.62,-440 1168.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="1100.88,-476 1112.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="1134.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">_Prompt</text>
</g>
<!-- 7&#45;&gt;12 -->
<g id="edge20" class="edge">
<title>7&#45;&gt;12</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M980.99,-1246.44C1019.28,-1050.36 1106.36,-604.41 1129.19,-487.5"/>
<polygon fill="black" stroke="black" points="1132.59,-488.32 1131.07,-477.83 1125.72,-486.98 1132.59,-488.32"/>
<text xml:space="preserve" text-anchor="middle" x="1139.08" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 15 -->
<g id="node24" class="node">
<title>15</title>
<polygon fill="#fffb81" stroke="black" points="1588.75,-1382.38 1254.75,-1382.38 1254.75,-1299.38 1588.75,-1299.38 1588.75,-1382.38"/>
<text xml:space="preserve" text-anchor="start" x="1262.75" y="-1365.08" font-family="DejaVu Sans Mono" font-size="14.00">model_config = ConfigDict(strict=True, extra=&#39;forbid&#39;)</text>
<text xml:space="preserve" text-anchor="start" x="1262.75" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">problem_desc: str</text>
<text xml:space="preserve" text-anchor="start" x="1262.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">target_model: str</text>
<text xml:space="preserve" text-anchor="start" x="1262.75" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00">token_limits: dict[str, int]</text>
<text xml:space="preserve" text-anchor="middle" x="1421.75" y="-1305.08" font-family="DejaVu Sans Mono" font-size="14.00">constraints: List[str]</text>
</g>
<!-- 16 -->
<g id="node25" class="node">
<title>16</title>
<polygon fill="#e552ff" stroke="#e552ff" points="1460.88,-476 1394.62,-476 1394.62,-480 1382.62,-480 1382.62,-440 1460.88,-440 1460.88,-476"/>
<polyline fill="none" stroke="#e552ff" points="1382.62,-476 1394.62,-476"/>
<text xml:space="preserve" text-anchor="middle" x="1421.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">ConfigDict</text>
</g>
<!-- 15&#45;&gt;16 -->
<g id="edge21" class="edge">
<title>15&#45;&gt;16</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M1421.75,-1299.16C1421.75,-1147.39 1421.75,-616.76 1421.75,-487.53"/>
<polygon fill="black" stroke="black" points="1425.25,-487.82 1421.75,-477.82 1418.25,-487.82 1425.25,-487.82"/>
<text xml:space="preserve" text-anchor="middle" x="1435.25" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 19 -->
<g id="node26" class="node">
<title>19</title>
<polygon fill="#fffb81" stroke="black" points="2387,-1367.38 2102.5,-1367.38 2102.5,-1314.38 2387,-1314.38 2387,-1367.38"/>
<text xml:space="preserve" text-anchor="start" x="2110.5" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">versions: List[str] = Field(default_factory=list)</text>
<text xml:space="preserve" text-anchor="start" x="2110.5" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">def current(self) &#45;&gt;str:...</text>
<text xml:space="preserve" text-anchor="middle" x="2244.75" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00">def add(self, p: str):...</text>
</g>
<!-- 20 -->
<g id="node27" class="node">
<title>20</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2271.75,-476 2229.75,-476 2229.75,-480 2217.75,-480 2217.75,-440 2271.75,-440 2271.75,-476"/>
<polyline fill="none" stroke="#e552ff" points="2217.75,-476 2229.75,-476"/>
<text xml:space="preserve" text-anchor="middle" x="2244.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">Field</text>
</g>
<!-- 19&#45;&gt;20 -->
<g id="edge22" class="edge">
<title>19&#45;&gt;20</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2244.75,-1313.99C2244.75,-1185.98 2244.75,-621.46 2244.75,-487.56"/>
<polygon fill="black" stroke="black" points="2248.25,-487.82 2244.75,-477.82 2241.25,-487.82 2248.25,-487.82"/>
<text xml:space="preserve" text-anchor="middle" x="2258.25" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 22 -->
<g id="node28" class="node">
<title>22</title>
<polygon fill="#98fb98" stroke="black" points="1905.21,-1358.88 1678.59,-1358.88 1620.29,-1322.88 1846.91,-1322.88 1905.21,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="1762.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">return self.versions[&#45;1]</text>
</g>
<!-- 26 -->
<g id="node29" class="node">
<title>26</title>
<polygon fill="#fffb81" stroke="black" points="2084.88,-1358.88 1928.62,-1358.88 1928.62,-1322.88 2084.88,-1322.88 2084.88,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="2006.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">self.versions.append(p)</text>
</g>
<!-- 27 -->
<g id="node30" class="node">
<title>27</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2076.62,-476 1948.88,-476 1948.88,-480 1936.88,-480 1936.88,-440 2076.62,-440 2076.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="1936.88,-476 1948.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="2006.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">self.versions.append</text>
</g>
<!-- 26&#45;&gt;27 -->
<g id="edge23" class="edge">
<title>26&#45;&gt;27</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2006.26,-1322.55C2003.83,-1235.99 1993.09,-841.84 1989.75,-519 1989.68,-512.33 1988.55,-510.56 1989.75,-504 1990.81,-498.19 1992.58,-492.16 1994.59,-486.47"/>
<polygon fill="black" stroke="black" points="1997.71,-488.1 1998.07,-477.51 1991.19,-485.57 1997.71,-488.1"/>
<text xml:space="preserve" text-anchor="middle" x="2003.25" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 31 -->
<g id="node31" class="node">
<title>31</title>
<polygon fill="#fffb81" stroke="black" points="3044.62,-1382.38 2628.88,-1382.38 2628.88,-1299.38 3044.62,-1299.38 3044.62,-1382.38"/>
<text xml:space="preserve" text-anchor="start" x="2636.88" y="-1365.08" font-family="DejaVu Sans Mono" font-size="14.00">model_config = ConfigDict(strict=True, validate_assignment=True)</text>
<text xml:space="preserve" text-anchor="start" x="2636.88" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">prompt: str = Field(..., min_length=10)</text>
<text xml:space="preserve" text-anchor="start" x="2636.88" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">token_count: int</text>
<text xml:space="preserve" text-anchor="start" x="2636.88" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00">version: int = 0</text>
<text xml:space="preserve" text-anchor="middle" x="2836.75" y="-1305.08" font-family="DejaVu Sans Mono" font-size="14.00">@field_validator(&#39;token_count&#39;)...</text>
</g>
<!-- 32 -->
<g id="node32" class="node">
<title>32</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2833.88,-476 2767.62,-476 2767.62,-480 2755.62,-480 2755.62,-440 2833.88,-440 2833.88,-476"/>
<polyline fill="none" stroke="#e552ff" points="2755.62,-476 2767.62,-476"/>
<text xml:space="preserve" text-anchor="middle" x="2794.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">ConfigDict</text>
</g>
<!-- 31&#45;&gt;32 -->
<g id="edge24" class="edge">
<title>31&#45;&gt;32</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2834.81,-1299.16C2827.57,-1147.39 2802.27,-616.76 2796.11,-487.53"/>
<polygon fill="black" stroke="black" points="2799.62,-487.64 2795.65,-477.82 2792.63,-487.97 2799.62,-487.64"/>
<text xml:space="preserve" text-anchor="middle" x="2810.8" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 33 -->
<g id="node33" class="node">
<title>33</title>
<polygon fill="#e552ff" stroke="#e552ff" points="2905.75,-476 2863.75,-476 2863.75,-480 2851.75,-480 2851.75,-440 2905.75,-440 2905.75,-476"/>
<polyline fill="none" stroke="#e552ff" points="2851.75,-476 2863.75,-476"/>
<text xml:space="preserve" text-anchor="middle" x="2878.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">Field</text>
</g>
<!-- 31&#45;&gt;33 -->
<g id="edge25" class="edge">
<title>31&#45;&gt;33</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M2838.69,-1299.16C2845.93,-1147.39 2871.23,-616.76 2877.39,-487.53"/>
<polygon fill="black" stroke="black" points="2880.87,-487.97 2877.85,-477.82 2873.88,-487.64 2880.87,-487.97"/>
<text xml:space="preserve" text-anchor="middle" x="2890.01" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 35 -->
<g id="node34" class="node">
<title>35</title>
<polygon fill="#fffb81" stroke="black" points="2610.5,-1358.88 2419,-1358.88 2419,-1322.88 2610.5,-1322.88 2610.5,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="2514.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">assert v &gt; 0, &#39;empty prompt?&#39;</text>
</g>
<!-- 37 -->
<g id="node35" class="node">
<title>37</title>
<polygon fill="#98fb98" stroke="black" points="2573.59,-476 2479.99,-476 2455.91,-440 2549.51,-440 2573.59,-476"/>
<text xml:space="preserve" text-anchor="middle" x="2514.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">return v</text>
</g>
<!-- 35&#45;&gt;37 -->
<g id="edge26" class="edge">
<title>35&#45;&gt;37</title>
<path fill="none" stroke="black" d="M2514.17,-1322.55C2511.31,-1236 2498.68,-841.86 2494.75,-519 2494.67,-512.33 2493.37,-510.52 2494.75,-504 2495.98,-498.19 2498.02,-492.22 2500.33,-486.6"/>
<polygon fill="black" stroke="black" points="2503.4,-488.31 2504.36,-477.75 2497.03,-485.41 2503.4,-488.31"/>
<text xml:space="preserve" text-anchor="middle" x="2509.75" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">v &gt; 0</text>
</g>
<!-- 42 -->
<g id="node36" class="node">
<title>42</title>
<polygon fill="#fffb81" stroke="black" points="3227,-1359.88 3068.5,-1359.88 3068.5,-1321.88 3227,-1321.88 3227,-1359.88"/>
<text xml:space="preserve" text-anchor="start" x="3076.5" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00">test_categories: List[str]</text>
<text xml:space="preserve" text-anchor="middle" x="3147.75" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00">template_count: int</text>
</g>
<!-- 45 -->
<g id="node37" class="node">
<title>45</title>
<polygon fill="#fffb81" stroke="black" points="3388.5,-1358.88 3251,-1358.88 3251,-1322.88 3388.5,-1322.88 3388.5,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="3319.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">test_cases: List[dict]</text>
</g>
<!-- 48 -->
<g id="node38" class="node">
<title>48</title>
<polygon fill="#fffb81" stroke="black" points="3531.12,-1358.88 3412.38,-1358.88 3412.38,-1322.88 3531.12,-1322.88 3531.12,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="3471.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">graders: List[dict]</text>
</g>
<!-- 51 -->
<g id="node39" class="node">
<title>51</title>
<polygon fill="#fffb81" stroke="black" points="3742.62,-1359.88 3554.88,-1359.88 3554.88,-1321.88 3742.62,-1321.88 3742.62,-1359.88"/>
<text xml:space="preserve" text-anchor="start" x="3562.88" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00">pass_rate: float</text>
<text xml:space="preserve" text-anchor="middle" x="3648.75" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00">improvement_areas: List[str]</text>
</g>
<!-- 54 -->
<g id="node40" class="node">
<title>54</title>
<polygon fill="#fffb81" stroke="black" points="4407,-1359.88 4242.5,-1359.88 4242.5,-1321.88 4407,-1321.88 4407,-1359.88"/>
<text xml:space="preserve" text-anchor="start" x="4250.5" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00">scenarios: List[str]</text>
<text xml:space="preserve" text-anchor="middle" x="4324.75" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00">def _display(self) &#45;&gt;str:...</text>
</g>
<!-- 56 -->
<g id="node41" class="node">
<title>56</title>
<polygon fill="#98fb98" stroke="black" points="4224.73,-1393.88 3866.84,-1393.88 3774.77,-1287.88 4132.66,-1287.88 4224.73,-1393.88"/>
<text xml:space="preserve" text-anchor="middle" x="3999.75" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">return &#39;## Scenarios</text>
<text xml:space="preserve" text-anchor="middle" x="3999.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="middle" x="3999.75" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {s}&#39; for s in self.scenarios)</text>
</g>
<!-- 61 -->
<g id="node42" class="node">
<title>61</title>
<polygon fill="#fffb81" stroke="black" points="5107,-1359.88 4942.5,-1359.88 4942.5,-1321.88 5107,-1321.88 5107,-1359.88"/>
<text xml:space="preserve" text-anchor="start" x="4950.5" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00">requirements: List[str]</text>
<text xml:space="preserve" text-anchor="middle" x="5024.75" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00">def _display(self) &#45;&gt;str:...</text>
</g>
<!-- 63 -->
<g id="node43" class="node">
<title>63</title>
<polygon fill="#98fb98" stroke="black" points="4924.61,-1393.88 4538.27,-1393.88 4438.89,-1287.88 4825.23,-1287.88 4924.61,-1393.88"/>
<text xml:space="preserve" text-anchor="middle" x="4681.75" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">return &#39;## Requirements</text>
<text xml:space="preserve" text-anchor="middle" x="4681.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">&#39; + &#39;</text>
<text xml:space="preserve" text-anchor="middle" x="4681.75" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00">&#39;.join(f&#39;&#45; {r}&#39; for r in self.requirements)</text>
</g>
<!-- 68 -->
<g id="node44" class="node">
<title>68</title>
<polygon fill="#fffb81" stroke="black" points="5291.12,-1367.38 5130.38,-1367.38 5130.38,-1314.38 5291.12,-1314.38 5291.12,-1367.38"/>
<text xml:space="preserve" text-anchor="start" x="5138.38" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">final_prompt: str</text>
<text xml:space="preserve" text-anchor="start" x="5138.38" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">test_generation_doc: str</text>
<text xml:space="preserve" text-anchor="middle" x="5210.75" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00">requirements_doc: str</text>
</g>
<!-- 72 -->
<g id="node45" class="node">
<title>72</title>
<polygon fill="#fffb81" stroke="black" points="6042.88,-1367.38 5562.62,-1367.38 5562.62,-1314.38 6042.88,-1314.38 6042.88,-1367.38"/>
<text xml:space="preserve" text-anchor="start" x="5570.62" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;LLM wrapper; micro is readability only.&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="5570.62" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">tmpl = ChatPromptTemplate.from_messages([(&#39;system&#39;, sys_prompt), (&#39;user&#39;,</text>
<text xml:space="preserve" text-anchor="middle" x="5802.75" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;{u}&#39;)])</text>
</g>
<!-- 73 -->
<g id="node46" class="node">
<title>73</title>
<polygon fill="#e552ff" stroke="#e552ff" points="5568.5,-476 5335,-476 5335,-480 5323,-480 5323,-440 5568.5,-440 5568.5,-476"/>
<polyline fill="none" stroke="#e552ff" points="5323,-476 5335,-476"/>
<text xml:space="preserve" text-anchor="middle" x="5445.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">ChatPromptTemplate.from_messages</text>
</g>
<!-- 72&#45;&gt;73 -->
<g id="edge27" class="edge">
<title>72&#45;&gt;73</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M5792.26,-1313.99C5740.28,-1185.73 5510.67,-619.19 5457,-486.75"/>
<polygon fill="black" stroke="black" points="5460.33,-485.66 5453.33,-477.71 5453.84,-488.29 5460.33,-485.66"/>
<text xml:space="preserve" text-anchor="middle" x="5480.94" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 74 -->
<g id="node47" class="node">
<title>74</title>
<polygon fill="#98fb98" stroke="black" points="6514.83,-476 5776.58,-476 5586.67,-440 6324.92,-440 6514.83,-476"/>
<text xml:space="preserve" text-anchor="middle" x="6050.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">return llm.invoke(tmpl.format_prompt(u=user_prompt).to_messages()).content</text>
</g>
<!-- 72&#45;&gt;74 -->
<g id="edge28" class="edge">
<title>72&#45;&gt;74</title>
<path fill="none" stroke="black" d="M5810.04,-1313.99C5846.11,-1185.85 6005.33,-620.32 6042.82,-487.15"/>
<polygon fill="black" stroke="black" points="6046.13,-488.33 6045.47,-477.76 6039.39,-486.44 6046.13,-488.33"/>
</g>
<!-- 78 -->
<g id="node48" class="node">
<title>78</title>
<polygon fill="#fffb81" stroke="black" points="7263.38,-1359.88 6714.12,-1359.88 6714.12,-1321.88 7263.38,-1321.88 7263.38,-1359.88"/>
<text xml:space="preserve" text-anchor="start" x="6722.12" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00">body = call_llm(PROMPTS.gen_prompt.micro, PROMPTS.gen_prompt.sys, PROMPTS.</text>
<text xml:space="preserve" text-anchor="middle" x="6988.75" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;gen_prompt.usr.format(problem_desc=inp.problem_desc))</text>
</g>
<!-- 79 -->
<g id="node49" class="node">
<title>79</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6750.62,-476 6700.88,-476 6700.88,-480 6688.88,-480 6688.88,-440 6750.62,-440 6750.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="6688.88,-476 6700.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="6719.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 78&#45;&gt;79 -->
<g id="edge29" class="edge">
<title>78&#45;&gt;79</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M6983.13,-1321.48C6949.01,-1209.73 6769.62,-622.3 6728.26,-486.87"/>
<polygon fill="black" stroke="black" points="6731.71,-486.19 6725.45,-477.65 6725.02,-488.24 6731.71,-486.19"/>
<text xml:space="preserve" text-anchor="middle" x="6749.59" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 81 -->
<g id="node51" class="node">
<title>81</title>
<polygon fill="#98fb98" stroke="black" points="7538.49,-476 6942.36,-476 6789.01,-440 7385.14,-440 7538.49,-476"/>
<text xml:space="preserve" text-anchor="middle" x="7163.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">return PromptDoc(prompt=body, token_count=len(body.split()))</text>
</g>
<!-- 78&#45;&gt;81 -->
<g id="edge31" class="edge">
<title>78&#45;&gt;81</title>
<path fill="none" stroke="black" d="M6992.41,-1321.48C7014.58,-1209.84 7131.07,-623.48 7158.13,-487.28"/>
<polygon fill="black" stroke="black" points="7161.52,-488.18 7160.04,-477.69 7154.66,-486.81 7161.52,-488.18"/>
</g>
<!-- 80 -->
<g id="node50" class="node">
<title>80</title>
<polygon fill="#e552ff" stroke="#e552ff" points="6772.75,-294 6558.75,-294 6558.75,-298 6546.75,-298 6546.75,-258 6772.75,-258 6772.75,-294"/>
<polyline fill="none" stroke="#e552ff" points="6546.75,-294 6558.75,-294"/>
<text xml:space="preserve" text-anchor="middle" x="6659.75" y="-270.2" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS.gen_prompt.usr.format</text>
</g>
<!-- 79&#45;&gt;80 -->
<g id="edge30" class="edge">
<title>79&#45;&gt;80</title>
<path fill="none" stroke="black" d="M6714,-439.74C6703.63,-408.64 6681.73,-342.93 6669.15,-305.21"/>
<polygon fill="black" stroke="black" points="6672.54,-304.29 6666.05,-295.91 6665.89,-306.5 6672.54,-304.29"/>
</g>
<!-- 85 -->
<g id="node52" class="node">
<title>85</title>
<polygon fill="#fffb81" stroke="black" points="8260.38,-1359.88 7753.12,-1359.88 7753.12,-1321.88 8260.38,-1321.88 8260.38,-1359.88"/>
<text xml:space="preserve" text-anchor="start" x="7761.12" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00">cats = call_llm(PROMPTS.test_cats.micro, PROMPTS.test_cats.sys, PROMPTS.</text>
<text xml:space="preserve" text-anchor="middle" x="8006.75" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;test_cats.usr.format(prompt=prompt_doc.prompt))</text>
</g>
<!-- 86 -->
<g id="node53" class="node">
<title>86</title>
<polygon fill="#e552ff" stroke="#e552ff" points="7758.62,-476 7708.88,-476 7708.88,-480 7696.88,-480 7696.88,-440 7758.62,-440 7758.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="7696.88,-476 7708.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="7727.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 85&#45;&gt;86 -->
<g id="edge32" class="edge">
<title>85&#45;&gt;86</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8000.92,-1321.48C7965.53,-1209.73 7779.47,-622.3 7736.58,-486.87"/>
<polygon fill="black" stroke="black" points="7740.01,-486.12 7733.66,-477.65 7733.34,-488.24 7740.01,-486.12"/>
<text xml:space="preserve" text-anchor="middle" x="7758.2" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 88 -->
<g id="node55" class="node">
<title>88</title>
<polygon fill="#98fb98" stroke="black" points="8598.65,-476 7959.32,-476 7794.85,-440 8434.18,-440 8598.65,-476"/>
<text xml:space="preserve" text-anchor="middle" x="8196.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">return TestCaseDoc(test_categories=cats.split(), template_count=3)</text>
</g>
<!-- 85&#45;&gt;88 -->
<g id="edge34" class="edge">
<title>85&#45;&gt;88</title>
<path fill="none" stroke="black" d="M8010.72,-1321.48C8034.8,-1209.84 8161.27,-623.48 8190.65,-487.28"/>
<polygon fill="black" stroke="black" points="8194.03,-488.2 8192.72,-477.68 8187.19,-486.72 8194.03,-488.2"/>
</g>
<!-- 87 -->
<g id="node54" class="node">
<title>87</title>
<polygon fill="#e552ff" stroke="#e552ff" points="7778.38,-294 7583.12,-294 7583.12,-298 7571.12,-298 7571.12,-258 7778.38,-258 7778.38,-294"/>
<polyline fill="none" stroke="#e552ff" points="7571.12,-294 7583.12,-294"/>
<text xml:space="preserve" text-anchor="middle" x="7674.75" y="-270.2" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS.test_cats.usr.format</text>
</g>
<!-- 86&#45;&gt;87 -->
<g id="edge33" class="edge">
<title>86&#45;&gt;87</title>
<path fill="none" stroke="black" d="M7722.67,-439.74C7713.51,-408.64 7694.16,-342.93 7683.06,-305.21"/>
<polygon fill="black" stroke="black" points="7686.5,-304.53 7680.32,-295.92 7679.79,-306.5 7686.5,-304.53"/>
</g>
<!-- 92 -->
<g id="node56" class="node">
<title>92</title>
<polygon fill="#fffb81" stroke="black" points="9156.5,-1359.88 8623,-1359.88 8623,-1321.88 9156.5,-1321.88 9156.5,-1359.88"/>
<text xml:space="preserve" text-anchor="start" x="8631" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00">resp = call_llm(PROMPTS.synth_tests.micro, PROMPTS.synth_tests.sys, PROMPTS</text>
<text xml:space="preserve" text-anchor="middle" x="8889.75" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;.synth_tests.usr.format(cats=cat))</text>
</g>
<!-- 93 -->
<g id="node57" class="node">
<title>93</title>
<polygon fill="#e552ff" stroke="#e552ff" points="8855.62,-476 8805.88,-476 8805.88,-480 8793.88,-480 8793.88,-440 8855.62,-440 8855.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="8793.88,-476 8805.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="8824.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 92&#45;&gt;93 -->
<g id="edge35" class="edge">
<title>92&#45;&gt;93</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M8888.39,-1321.48C8880.15,-1209.84 8836.89,-623.48 8826.84,-487.28"/>
<polygon fill="black" stroke="black" points="8830.36,-487.43 8826.13,-477.71 8823.38,-487.94 8830.36,-487.43"/>
<text xml:space="preserve" text-anchor="middle" x="8842.2" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 95 -->
<g id="node59" class="node">
<title>95</title>
<polygon fill="#98fb98" stroke="black" points="9132.34,-476 8942.1,-476 8893.16,-440 9083.4,-440 9132.34,-476"/>
<text xml:space="preserve" text-anchor="middle" x="9012.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">return {&#39;raw&#39;: resp}</text>
</g>
<!-- 92&#45;&gt;95 -->
<g id="edge37" class="edge">
<title>92&#45;&gt;95</title>
<path fill="none" stroke="black" d="M8892.32,-1321.48C8907.91,-1209.84 8989.78,-623.48 9008.8,-487.28"/>
<polygon fill="black" stroke="black" points="9012.22,-488.09 9010.14,-477.7 9005.29,-487.12 9012.22,-488.09"/>
</g>
<!-- 94 -->
<g id="node58" class="node">
<title>94</title>
<polygon fill="#e552ff" stroke="#e552ff" points="8876.5,-294 8667,-294 8667,-298 8655,-298 8655,-258 8876.5,-258 8876.5,-294"/>
<polyline fill="none" stroke="#e552ff" points="8655,-294 8667,-294"/>
<text xml:space="preserve" text-anchor="middle" x="8765.75" y="-270.2" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS.synth_tests.usr.format</text>
</g>
<!-- 93&#45;&gt;94 -->
<g id="edge36" class="edge">
<title>93&#45;&gt;94</title>
<path fill="none" stroke="black" d="M8819.09,-439.74C8808.9,-408.64 8787.36,-342.93 8775,-305.21"/>
<polygon fill="black" stroke="black" points="8778.39,-304.32 8771.95,-295.91 8771.74,-306.5 8778.39,-304.32"/>
</g>
<!-- 99 -->
<g id="node60" class="node">
<title>99</title>
<polygon fill="#fffb81" stroke="black" points="9682.62,-1358.88 9308.88,-1358.88 9308.88,-1322.88 9682.62,-1322.88 9682.62,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="9495.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">tests = [_synth_task_sync(c) for c in tc_doc.test_categories]</text>
</g>
<!-- 100 -->
<g id="node61" class="node">
<title>100</title>
<polygon fill="#e552ff" stroke="#e552ff" points="9312.38,-476 9201.12,-476 9201.12,-480 9189.12,-480 9189.12,-440 9312.38,-440 9312.38,-476"/>
<polyline fill="none" stroke="#e552ff" points="9189.12,-476 9201.12,-476"/>
<text xml:space="preserve" text-anchor="middle" x="9250.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">_synth_task_sync</text>
</g>
<!-- 99&#45;&gt;100 -->
<g id="edge38" class="edge">
<title>99&#45;&gt;100</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M9490.97,-1322.67C9460.75,-1214.04 9296.44,-623.29 9258.55,-487.04"/>
<polygon fill="black" stroke="black" points="9262.02,-486.47 9255.97,-477.77 9255.28,-488.34 9262.02,-486.47"/>
<text xml:space="preserve" text-anchor="middle" x="9279.13" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 101 -->
<g id="node62" class="node">
<title>101</title>
<polygon fill="#98fb98" stroke="black" points="9919.45,-476 9450.65,-476 9330.05,-440 9798.85,-440 9919.45,-476"/>
<text xml:space="preserve" text-anchor="middle" x="9624.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">return SyntheticTestCases(test_cases=list(tests))</text>
</g>
<!-- 99&#45;&gt;101 -->
<g id="edge39" class="edge">
<title>99&#45;&gt;101</title>
<path fill="none" stroke="black" d="M9498.27,-1322.67C9514.16,-1214.15 9600.52,-624.48 9620.58,-487.46"/>
<polygon fill="black" stroke="black" points="9624.01,-488.21 9622,-477.81 9617.08,-487.2 9624.01,-488.21"/>
</g>
<!-- 105 -->
<g id="node63" class="node">
<title>105</title>
<polygon fill="#98fb98" stroke="black" points="10357.54,-1358.88 10028.58,-1358.88 9943.96,-1322.88 10272.92,-1322.88 10357.54,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="10150.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">return {&#39;grader&#39;: f&#39;grader for {req}&#39;}</text>
</g>
<!-- 109 -->
<g id="node64" class="node">
<title>109</title>
<polygon fill="#fffb81" stroke="black" points="10917.88,-1358.88 10455.62,-1358.88 10455.62,-1322.88 10917.88,-1322.88 10917.88,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="10686.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">gs = await asyncio.gather(*(_grader_task(r) for r in req_doc.requirements))</text>
</g>
<!-- 111 -->
<g id="node65" class="node">
<title>111</title>
<polygon fill="#e552ff" stroke="#e552ff" points="10493.62,-476 10401.88,-476 10401.88,-480 10389.88,-480 10389.88,-440 10493.62,-440 10493.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="10389.88,-476 10401.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="10441.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">asyncio.gather</text>
</g>
<!-- 109&#45;&gt;111 -->
<g id="edge40" class="edge">
<title>109&#45;&gt;111</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M10681.97,-1322.67C10651.75,-1214.04 10487.44,-623.29 10449.55,-487.04"/>
<polygon fill="black" stroke="black" points="10453.02,-486.47 10446.97,-477.77 10446.28,-488.34 10453.02,-486.47"/>
<text xml:space="preserve" text-anchor="middle" x="10470.13" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 110 -->
<g id="node67" class="node">
<title>110</title>
<polygon fill="#98fb98" stroke="black" points="11012.13,-476 10613.83,-476 10511.37,-440 10909.67,-440 11012.13,-476"/>
<text xml:space="preserve" text-anchor="middle" x="10761.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">return SyntheticGraders(graders=list(gs))</text>
</g>
<!-- 109&#45;&gt;110 -->
<g id="edge42" class="edge">
<title>109&#45;&gt;110</title>
<path fill="none" stroke="black" d="M10688.21,-1322.67C10697.45,-1214.15 10747.66,-624.48 10759.33,-487.46"/>
<polygon fill="black" stroke="black" points="10762.79,-488.08 10760.15,-477.82 10755.81,-487.49 10762.79,-488.08"/>
</g>
<!-- 112 -->
<g id="node66" class="node">
<title>112</title>
<polygon fill="#e552ff" stroke="#e552ff" points="10489.12,-294 10406.38,-294 10406.38,-298 10394.38,-298 10394.38,-258 10489.12,-258 10489.12,-294"/>
<polyline fill="none" stroke="#e552ff" points="10394.38,-294 10406.38,-294"/>
<text xml:space="preserve" text-anchor="middle" x="10441.75" y="-270.2" font-family="DejaVu Sans Mono" font-size="14.00">_grader_task</text>
</g>
<!-- 111&#45;&gt;112 -->
<g id="edge41" class="edge">
<title>111&#45;&gt;112</title>
<path fill="none" stroke="black" d="M10441.75,-439.74C10441.75,-408.77 10441.75,-343.49 10441.75,-305.7"/>
<polygon fill="black" stroke="black" points="10445.25,-305.98 10441.75,-295.98 10438.25,-305.98 10445.25,-305.98"/>
</g>
<!-- 116 -->
<g id="node68" class="node">
<title>116</title>
<polygon fill="#98fb98" stroke="black" points="11965.55,-1358.88 11226.16,-1358.88 11035.95,-1322.88 11775.34,-1322.88 11965.55,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="11500.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">return GradeDoc(pass_rate=0.75, improvement_areas=[&#39;clarity&#39;, &#39;edge cases&#39;])</text>
</g>
<!-- 120 -->
<g id="node69" class="node">
<title>120</title>
<polygon fill="#fffb81" stroke="black" points="12493.5,-1382.38 11990,-1382.38 11990,-1299.38 12493.5,-1299.38 12493.5,-1382.38"/>
<text xml:space="preserve" text-anchor="start" x="11998" y="-1365.08" font-family="DejaVu Sans Mono" font-size="14.00">payload = f&quot;&quot;&quot;PROMPT:</text>
<text xml:space="preserve" text-anchor="start" x="11998" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">{prompt_doc.prompt}</text>
<text xml:space="preserve" text-anchor="start" x="11998" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">ISSUES:{&#39;,&#39;.join(grade.improvement_areas)}&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="11998" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00">new_body = call_llm(PROMPTS.refine.micro, PROMPTS.refine.sys, PROMPTS.</text>
<text xml:space="preserve" text-anchor="middle" x="12241.75" y="-1305.08" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;refine.usr.format(payload=payload))</text>
</g>
<!-- 121 -->
<g id="node70" class="node">
<title>121</title>
<polygon fill="#e552ff" stroke="#e552ff" points="12104.75,-476 12062.75,-476 12062.75,-480 12050.75,-480 12050.75,-440 12104.75,-440 12104.75,-476"/>
<polyline fill="none" stroke="#e552ff" points="12050.75,-476 12062.75,-476"/>
<text xml:space="preserve" text-anchor="middle" x="12077.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">,.join</text>
</g>
<!-- 120&#45;&gt;121 -->
<g id="edge43" class="edge">
<title>120&#45;&gt;121</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M12234.17,-1299.16C12205.91,-1147.39 12107.12,-616.76 12083.06,-487.53"/>
<polygon fill="black" stroke="black" points="12086.52,-486.99 12081.25,-477.8 12079.64,-488.27 12086.52,-486.99"/>
<text xml:space="preserve" text-anchor="middle" x="12101.21" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 122 -->
<g id="node71" class="node">
<title>122</title>
<polygon fill="#e552ff" stroke="#e552ff" points="12184.62,-476 12134.88,-476 12134.88,-480 12122.88,-480 12122.88,-440 12184.62,-440 12184.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="12122.88,-476 12134.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="12153.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 120&#45;&gt;122 -->
<g id="edge44" class="edge">
<title>120&#45;&gt;122</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M12237.68,-1299.16C12222.52,-1147.39 12169.51,-616.76 12156.6,-487.53"/>
<polygon fill="black" stroke="black" points="12160.11,-487.42 12155.63,-477.81 12153.14,-488.11 12160.11,-487.42"/>
<text xml:space="preserve" text-anchor="middle" x="12172.6" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 124 -->
<g id="node73" class="node">
<title>124</title>
<polygon fill="#98fb98" stroke="black" points="13123.33,-496 12390.65,-496 12202.17,-420 12934.85,-420 13123.33,-496"/>
<text xml:space="preserve" text-anchor="start" x="12286.02" y="-459.7" font-family="DejaVu Sans Mono" font-size="14.00">return prompt_doc.model_copy(update={&#39;prompt&#39;: new_body, &#39;version&#39;: </text>
<text xml:space="preserve" text-anchor="middle" x="12662.75" y="-444.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;prompt_doc.version + 1, &#39;token_count&#39;: len(new_body.split())})</text>
</g>
<!-- 120&#45;&gt;124 -->
<g id="edge46" class="edge">
<title>120&#45;&gt;124</title>
<path fill="none" stroke="black" d="M12261.21,-1299.16C12330.61,-1153.95 12565.76,-661.93 12640.06,-506.48"/>
<polygon fill="black" stroke="black" points="12643.11,-508.21 12644.27,-497.67 12636.8,-505.19 12643.11,-508.21"/>
</g>
<!-- 123 -->
<g id="node72" class="node">
<title>123</title>
<polygon fill="#e552ff" stroke="#e552ff" points="12185.25,-294 12010.25,-294 12010.25,-298 11998.25,-298 11998.25,-258 12185.25,-258 12185.25,-294"/>
<polyline fill="none" stroke="#e552ff" points="11998.25,-294 12010.25,-294"/>
<text xml:space="preserve" text-anchor="middle" x="12091.75" y="-270.2" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS.refine.usr.format</text>
</g>
<!-- 122&#45;&gt;123 -->
<g id="edge45" class="edge">
<title>122&#45;&gt;123</title>
<path fill="none" stroke="black" d="M12147.81,-439.74C12137.09,-408.64 12114.46,-342.93 12101.47,-305.21"/>
<polygon fill="black" stroke="black" points="12104.83,-304.22 12098.26,-295.9 12098.21,-306.5 12104.83,-304.22"/>
</g>
<!-- 128 -->
<g id="node74" class="node">
<title>128</title>
<polygon fill="#fffb81" stroke="black" points="13915.25,-1359.88 13380.25,-1359.88 13380.25,-1321.88 13915.25,-1321.88 13915.25,-1359.88"/>
<text xml:space="preserve" text-anchor="start" x="13388.25" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00">shorter = call_llm(PROMPTS.compress.micro, PROMPTS.compress.sys, PROMPTS.</text>
<text xml:space="preserve" text-anchor="middle" x="13647.75" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;compress.usr.format(prompt=prompt_doc.prompt))</text>
</g>
<!-- 129 -->
<g id="node75" class="node">
<title>129</title>
<polygon fill="#e552ff" stroke="#e552ff" points="13347.62,-476 13297.88,-476 13297.88,-480 13285.88,-480 13285.88,-440 13347.62,-440 13347.62,-476"/>
<polyline fill="none" stroke="#e552ff" points="13285.88,-476 13297.88,-476"/>
<text xml:space="preserve" text-anchor="middle" x="13316.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">call_llm</text>
</g>
<!-- 128&#45;&gt;129 -->
<g id="edge47" class="edge">
<title>128&#45;&gt;129</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M13640.84,-1321.48C13598.84,-1209.73 13378.11,-622.3 13327.22,-486.87"/>
<polygon fill="black" stroke="black" points="13330.54,-485.75 13323.75,-477.62 13323.99,-488.21 13330.54,-485.75"/>
<text xml:space="preserve" text-anchor="middle" x="13350.36" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 131 -->
<g id="node77" class="node">
<title>131</title>
<polygon fill="#98fb98" stroke="black" points="14375.32,-496 13586.98,-496 13384.18,-420 14172.52,-420 14375.32,-496"/>
<text xml:space="preserve" text-anchor="start" x="13473.79" y="-459.7" font-family="DejaVu Sans Mono" font-size="14.00">return prompt_doc.model_copy(update={&#39;prompt&#39;: shorter, &#39;token_count&#39;: len(</text>
<text xml:space="preserve" text-anchor="middle" x="13879.75" y="-444.7" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;shorter.split())})</text>
</g>
<!-- 128&#45;&gt;131 -->
<g id="edge49" class="edge">
<title>128&#45;&gt;131</title>
<path fill="none" stroke="black" d="M13652.6,-1321.48C13680.74,-1214.61 13823.46,-672.71 13867.07,-507.13"/>
<polygon fill="black" stroke="black" points="13870.37,-508.34 13869.54,-497.78 13863.6,-506.56 13870.37,-508.34"/>
</g>
<!-- 130 -->
<g id="node76" class="node">
<title>130</title>
<polygon fill="#e552ff" stroke="#e552ff" points="13368,-294 13167.5,-294 13167.5,-298 13155.5,-298 13155.5,-258 13368,-258 13368,-294"/>
<polyline fill="none" stroke="#e552ff" points="13155.5,-294 13167.5,-294"/>
<text xml:space="preserve" text-anchor="middle" x="13261.75" y="-270.2" font-family="DejaVu Sans Mono" font-size="14.00">PROMPTS.compress.usr.format</text>
</g>
<!-- 129&#45;&gt;130 -->
<g id="edge48" class="edge">
<title>129&#45;&gt;130</title>
<path fill="none" stroke="black" d="M13311.48,-439.74C13301.97,-408.64 13281.89,-342.93 13270.37,-305.21"/>
<polygon fill="black" stroke="black" points="13273.8,-304.46 13267.53,-295.92 13267.11,-306.51 13273.8,-304.46"/>
</g>
<!-- 135 -->
<g id="node78" class="node">
<title>135</title>
<polygon fill="#98fb98" stroke="black" points="15375.54,-1378.88 14599.57,-1378.88 14399.96,-1302.88 15175.93,-1302.88 15375.54,-1378.88"/>
<text xml:space="preserve" text-anchor="start" x="14488.29" y="-1342.58" font-family="DejaVu Sans Mono" font-size="14.00">return FinalBundle(final_prompt=prompt_doc.prompt, test_generation_doc=</text>
<text xml:space="preserve" text-anchor="middle" x="14887.75" y="-1327.58" font-family="DejaVu Sans Mono" font-size="14.00"> &#160;&#160;&#160;&#39;&lt;stub&gt;&#39;, requirements_doc=&#39;&lt;stub&gt;&#39;)</text>
</g>
<!-- 139 -->
<g id="node79" class="node">
<title>139</title>
<polygon fill="#98fb98" stroke="black" points="16099.48,-1358.88 15543.14,-1358.88 15400.02,-1322.88 15956.36,-1322.88 16099.48,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="15749.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">return lambda x: reduce(lambda acc, fn: fn(acc), stages, x)</text>
</g>
<!-- 143 -->
<g id="node80" class="node">
<title>143</title>
<polygon fill="#98fb98" stroke="black" points="16427.51,-1358.88 16186.1,-1358.88 16123.99,-1322.88 16365.4,-1322.88 16427.51,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="16275.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">return asyncio.run(coro)</text>
</g>
<!-- 147 -->
<g id="node81" class="node">
<title>147</title>
<polygon fill="#fffb81" stroke="black" points="17161.38,-1397.38 16642.12,-1397.38 16642.12,-1284.38 17161.38,-1284.38 17161.38,-1397.38"/>
<text xml:space="preserve" text-anchor="start" x="16650.12" y="-1380.08" font-family="DejaVu Sans Mono" font-size="14.00">&quot;&quot;&quot;Complete pipeline execution with proper state management and async support&quot;&quot;&quot;</text>
<text xml:space="preserve" text-anchor="start" x="16650.12" y="-1365.08" font-family="DejaVu Sans Mono" font-size="14.00">prompt_doc = gen_prompt(prob)</text>
<text xml:space="preserve" text-anchor="start" x="16650.12" y="-1350.08" font-family="DejaVu Sans Mono" font-size="14.00">test_cases = gen_testcases(prompt_doc)</text>
<text xml:space="preserve" text-anchor="start" x="16650.12" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">synthetic = gen_synth_TC1s_sync(test_cases)</text>
<text xml:space="preserve" text-anchor="start" x="16650.12" y="-1320.08" font-family="DejaVu Sans Mono" font-size="14.00">grade = grade_tests(synthetic)</text>
<text xml:space="preserve" text-anchor="start" x="16650.12" y="-1305.08" font-family="DejaVu Sans Mono" font-size="14.00">refined_prompt = refine_prompt(prompt_doc, grade)</text>
<text xml:space="preserve" text-anchor="middle" x="16901.75" y="-1290.08" font-family="DejaVu Sans Mono" font-size="14.00">optimized_prompt = optimise_prompt(refined_prompt)</text>
</g>
<!-- 148 -->
<g id="node82" class="node">
<title>148</title>
<polygon fill="#e552ff" stroke="#e552ff" points="16548.12,-476 16471.38,-476 16471.38,-480 16459.38,-480 16459.38,-440 16548.12,-440 16548.12,-476"/>
<polyline fill="none" stroke="#e552ff" points="16459.38,-476 16471.38,-476"/>
<text xml:space="preserve" text-anchor="middle" x="16503.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">gen_prompt</text>
</g>
<!-- 147&#45;&gt;148 -->
<g id="edge50" class="edge">
<title>147&#45;&gt;148</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M16876.55,-1284.1C16800,-1114.68 16572.01,-610.07 16516.19,-486.53"/>
<polygon fill="black" stroke="black" points="16519.54,-485.45 16512.24,-477.78 16513.16,-488.34 16519.54,-485.45"/>
<text xml:space="preserve" text-anchor="middle" x="16541.43" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 149 -->
<g id="node83" class="node">
<title>149</title>
<polygon fill="#e552ff" stroke="#e552ff" points="16669.25,-476 16578.25,-476 16578.25,-480 16566.25,-480 16566.25,-440 16669.25,-440 16669.25,-476"/>
<polyline fill="none" stroke="#e552ff" points="16566.25,-476 16578.25,-476"/>
<text xml:space="preserve" text-anchor="middle" x="16617.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">gen_testcases</text>
</g>
<!-- 147&#45;&gt;149 -->
<g id="edge51" class="edge">
<title>147&#45;&gt;149</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M16883.77,-1284.1C16829.26,-1115.02 16667.11,-612.1 16626.87,-487.28"/>
<polygon fill="black" stroke="black" points="16630.23,-486.29 16623.83,-477.84 16623.56,-488.44 16630.23,-486.29"/>
<text xml:space="preserve" text-anchor="middle" x="16648.5" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 150 -->
<g id="node84" class="node">
<title>150</title>
<polygon fill="#e552ff" stroke="#e552ff" points="16840.38,-476 16699.12,-476 16699.12,-480 16687.12,-480 16687.12,-440 16840.38,-440 16840.38,-476"/>
<polyline fill="none" stroke="#e552ff" points="16687.12,-476 16699.12,-476"/>
<text xml:space="preserve" text-anchor="middle" x="16763.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">gen_synth_TC1s_sync</text>
</g>
<!-- 147&#45;&gt;150 -->
<g id="edge52" class="edge">
<title>147&#45;&gt;150</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M16893.01,-1284.1C16866.55,-1115.19 16787.89,-613.12 16768.24,-487.66"/>
<polygon fill="black" stroke="black" points="16771.72,-487.24 16766.71,-477.9 16764.8,-488.32 16771.72,-487.24"/>
<text xml:space="preserve" text-anchor="middle" x="16785.63" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 151 -->
<g id="node85" class="node">
<title>151</title>
<polygon fill="#e552ff" stroke="#e552ff" points="16945,-476 16870.5,-476 16870.5,-480 16858.5,-480 16858.5,-440 16945,-440 16945,-476"/>
<polyline fill="none" stroke="#e552ff" points="16858.5,-476 16870.5,-476"/>
<text xml:space="preserve" text-anchor="middle" x="16901.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">grade_tests</text>
</g>
<!-- 147&#45;&gt;151 -->
<g id="edge53" class="edge">
<title>147&#45;&gt;151</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M16901.75,-1284.1C16901.75,-1115.19 16901.75,-613.12 16901.75,-487.66"/>
<polygon fill="black" stroke="black" points="16905.25,-487.92 16901.75,-477.92 16898.25,-487.92 16905.25,-487.92"/>
<text xml:space="preserve" text-anchor="middle" x="16915.25" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 152 -->
<g id="node86" class="node">
<title>152</title>
<polygon fill="#e552ff" stroke="#e552ff" points="17062.75,-476 16974.75,-476 16974.75,-480 16962.75,-480 16962.75,-440 17062.75,-440 17062.75,-476"/>
<polyline fill="none" stroke="#e552ff" points="16962.75,-476 16974.75,-476"/>
<text xml:space="preserve" text-anchor="middle" x="17012.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">refine_prompt</text>
</g>
<!-- 147&#45;&gt;152 -->
<g id="edge54" class="edge">
<title>147&#45;&gt;152</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M16908.78,-1284.1C16930.06,-1115.19 16993.33,-613.12 17009.14,-487.66"/>
<polygon fill="black" stroke="black" points="17012.59,-488.26 17010.37,-477.91 17005.64,-487.39 17012.59,-488.26"/>
<text xml:space="preserve" text-anchor="middle" x="17020.33" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 153 -->
<g id="node87" class="node">
<title>153</title>
<polygon fill="#e552ff" stroke="#e552ff" points="17198.38,-476 17093.12,-476 17093.12,-480 17081.12,-480 17081.12,-440 17198.38,-440 17198.38,-476"/>
<polyline fill="none" stroke="#e552ff" points="17081.12,-476 17093.12,-476"/>
<text xml:space="preserve" text-anchor="middle" x="17139.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">optimise_prompt</text>
</g>
<!-- 147&#45;&gt;153 -->
<g id="edge55" class="edge">
<title>147&#45;&gt;153</title>
<path fill="none" stroke="black" stroke-dasharray="5,2" d="M16916.82,-1284.1C16962.5,-1115.02 17098.38,-612.1 17132.11,-487.28"/>
<polygon fill="black" stroke="black" points="17135.42,-488.43 17134.65,-477.86 17128.67,-486.61 17135.42,-488.43"/>
<text xml:space="preserve" text-anchor="middle" x="17140.56" y="-505.7" font-family="DejaVu Sans Mono" font-size="14.00">calls</text>
</g>
<!-- 154 -->
<g id="node88" class="node">
<title>154</title>
<polygon fill="#98fb98" stroke="black" points="17621.25,-476 17299.12,-476 17216.25,-440 17538.38,-440 17621.25,-476"/>
<text xml:space="preserve" text-anchor="middle" x="17418.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">return deliver(optimized_prompt)</text>
</g>
<!-- 147&#45;&gt;154 -->
<g id="edge56" class="edge">
<title>147&#45;&gt;154</title>
<path fill="none" stroke="black" d="M16934.48,-1284.1C17034.02,-1114.51 17330.68,-609.06 17402.81,-486.16"/>
<polygon fill="black" stroke="black" points="17405.73,-488.1 17407.77,-477.71 17399.69,-484.56 17405.73,-488.1"/>
</g>
<!-- input -->
<g id="node89" class="node">
<title>input</title>
<polygon fill="#afeeee" stroke="black" points="17730.87,-476 17662.28,-476 17644.63,-440 17713.22,-440 17730.87,-476"/>
<text xml:space="preserve" text-anchor="middle" x="17687.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">input</text>
</g>
<!-- call -->
<g id="node94" class="node">
<title>call</title>
<polygon fill="#e552ff" stroke="black" points="17714.75,-294 17672.75,-294 17672.75,-298 17660.75,-298 17660.75,-258 17714.75,-258 17714.75,-294"/>
<polyline fill="none" stroke="black" points="17660.75,-294 17672.75,-294"/>
<text xml:space="preserve" text-anchor="middle" x="17687.75" y="-270.2" font-family="DejaVu Sans Mono" font-size="14.00">call</text>
</g>
<!-- input&#45;&gt;call -->
<!-- default -->
<g id="node90" class="node">
<title>default</title>
<polygon fill="#fffb81" stroke="black" points="17826,-294 17769.5,-294 17769.5,-258 17826,-258 17826,-294"/>
<text xml:space="preserve" text-anchor="middle" x="17797.75" y="-270.2" font-family="DejaVu Sans Mono" font-size="14.00">default</text>
</g>
<!-- if -->
<g id="node91" class="node">
<title>if</title>
<polygon fill="#ff6752" stroke="black" points="17674.75,-1358.88 17647.75,-1340.88 17674.75,-1322.88 17701.75,-1340.88 17674.75,-1358.88"/>
<text xml:space="preserve" text-anchor="middle" x="17674.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">if</text>
</g>
<!-- if&#45;&gt;input -->
<!-- for -->
<g id="node92" class="node">
<title>for</title>
<polygon fill="#ffbe52" stroke="black" points="17773.75,-1340.88 17760.25,-1358.88 17733.25,-1358.88 17719.75,-1340.88 17733.25,-1322.88 17760.25,-1322.88 17773.75,-1340.88"/>
<text xml:space="preserve" text-anchor="middle" x="17746.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">for</text>
</g>
<!-- return -->
<g id="node95" class="node">
<title>return</title>
<polygon fill="#98fb98" stroke="black" points="17846.59,-476 17768.9,-476 17748.91,-440 17826.6,-440 17846.59,-476"/>
<text xml:space="preserve" text-anchor="middle" x="17797.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">return</text>
</g>
<!-- for&#45;&gt;return -->
<!-- while -->
<g id="node93" class="node">
<title>while</title>
<polygon fill="#ffbe52" stroke="black" points="17861.84,-1340.88 17844.29,-1358.88 17809.21,-1358.88 17791.66,-1340.88 17809.21,-1322.88 17844.29,-1322.88 17861.84,-1340.88"/>
<text xml:space="preserve" text-anchor="middle" x="17826.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">while</text>
</g>
<!-- return&#45;&gt;default -->
<!-- try -->
<g id="node96" class="node">
<title>try</title>
<polygon fill="orange" stroke="black" points="17908.75,-1358.88 17880.26,-1340.88 17908.75,-1322.88 17937.24,-1340.88 17908.75,-1358.88"/>
<polyline fill="none" stroke="black" points="17889.75,-1346.88 17889.75,-1334.88"/>
<polyline fill="none" stroke="black" points="17899.25,-1328.88 17918.25,-1328.88"/>
<polyline fill="none" stroke="black" points="17927.75,-1334.88 17927.75,-1346.88"/>
<polyline fill="none" stroke="black" points="17918.25,-1352.88 17899.25,-1352.88"/>
<text xml:space="preserve" text-anchor="middle" x="17908.75" y="-1335.08" font-family="DejaVu Sans Mono" font-size="14.00">try</text>
</g>
<!-- raise -->
<g id="node97" class="node">
<title>raise</title>
<polygon fill="#98fb98" stroke="black" points="17937.1,-463.56 17900.75,-476 17864.4,-463.56 17864.43,-443.44 17937.07,-443.44 17937.1,-463.56"/>
<text xml:space="preserve" text-anchor="middle" x="17900.75" y="-452.2" font-family="DejaVu Sans Mono" font-size="14.00">raise</text>
</g>
<!-- try&#45;&gt;raise -->
</g>
</svg>
