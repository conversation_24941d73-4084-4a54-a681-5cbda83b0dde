#!/usr/bin/env python3
"""
Generate control flow graphs for all Python files in the repository.
Uses py2cfg to generate CFGs and creates a combined visualization.
"""
import os
import subprocess
import tempfile
from pathlib import Path

def generate_cfg_for_file(py_file, output_dir):
    """Generate CFG for a single Python file using py2cfg."""
    basename = os.path.basename(py_file).replace('.py', '')
    output_path = os.path.join(output_dir, f"{basename}_cfg")
    
    try:
        # py2cfg generates a .dot file
        cmd = ['py2cfg', py_file, '--outfile', f'{output_path}.dot']
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            return f"{output_path}.dot"
        else:
            print(f"Error processing {py_file}: {result.stderr}")
            return None
    except Exception as e:
        print(f"Exception processing {py_file}: {e}")
        return None

def main():
    # 1️⃣ Collect all .py files in the repository root (excluding venv/__pycache__/.venv)
    py_files = []
    repo_root = os.path.dirname(os.path.dirname(__file__))
    
    for root, dirs, files in os.walk(repo_root):
        # Skip virtual environments, cache directories, and _temp
        dirs[:] = [d for d in dirs if d not in ("venv", ".venv", "__pycache__", ".git", "_temp", "node_modules")]
        py_files += [os.path.join(root, f) for f in files if f.endswith(".py")]
    
    if not py_files:
        print("No Python files found in repository")
        return
    
    print(f"Found {len(py_files)} Python files")
    
    # 2️⃣ Create output directory for CFGs
    output_dir = os.path.join(os.path.dirname(__file__), "cfg_output")
    os.makedirs(output_dir, exist_ok=True)
    
    # 3️⃣ Generate CFGs for each file
    dot_files = []
    for py_file in py_files:
        print(f"Processing: {os.path.relpath(py_file, repo_root)}")
        dot_file = generate_cfg_for_file(py_file, output_dir)
        if dot_file:
            dot_files.append(dot_file)
    
    print(f"\nGenerated {len(dot_files)} control flow graphs")
    print(f"Output directory: {output_dir}")
    
    # 4️⃣ Generate PNG images from DOT files (optional)
    print("\nConverting DOT files to PNG images...")
    for dot_file in dot_files:
        png_file = dot_file.replace('.dot', '.png')
        try:
            subprocess.run(['dot', '-Tpng', dot_file, '-o', png_file], 
                         capture_output=True, check=True)
            print(f"Created: {os.path.basename(png_file)}")
        except subprocess.CalledProcessError:
            print(f"Warning: Could not convert {dot_file} to PNG (graphviz may not be installed)")
        except FileNotFoundError:
            print("Warning: graphviz not found. Install it to generate PNG images.")
            break

if __name__ == "__main__":
    main()