{"cells": [{"cell_type": "markdown", "metadata": {}, "source": "# Control Flow Graph Generation\n\nThis notebook generates control flow graphs for all Python files in the repository using py2cfg."}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Install Dependencies"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting py2cfg\n", "  Downloading py2cfg-1.0.5-py3-none-any.whl.metadata (6.0 kB)\n", "Collecting astor (from py2cfg)\n", "  Downloading astor-0.8.1-py2.py3-none-any.whl.metadata (4.2 kB)\n", "Collecting pudb (from py2cfg)\n", "  Downloading pudb-2025.1-py3-none-any.whl.metadata (4.5 kB)\n", "Collecting websocket-server (from py2cfg)\n", "  Downloading websocket_server-0.6.4-py3-none-any.whl.metadata (359 bytes)\n", "Requirement already satisfied: graphviz in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from py2cfg) (0.21)\n", "Requirement already satisfied: flask in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from py2cfg) (3.1.1)\n", "Requirement already satisfied: requests in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from py2cfg) (2.32.4)\n", "Requirement already satisfied: blinker>=1.9.0 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from flask->py2cfg) (1.9.0)\n", "Requirement already satisfied: click>=8.1.3 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from flask->py2cfg) (8.2.1)\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from flask->py2cfg) (2.2.0)\n", "Requirement already satisfied: jinja2>=3.1.2 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from flask->py2cfg) (3.1.6)\n", "Requirement already satisfied: markupsafe>=2.1.1 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from flask->py2cfg) (3.0.2)\n", "Requirement already satisfied: werkzeug>=3.1.0 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from flask->py2cfg) (3.1.3)\n", "Requirement already satisfied: jedi<1,>=0.18 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from pudb->py2cfg) (0.19.2)\n", "Requirement already satisfied: packaging>=20.0 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from pudb->py2cfg) (24.2)\n", "Requirement already satisfied: pygments>=2.7.4 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from pudb->py2cfg) (2.19.2)\n", "Collecting urwid-readline (from pudb->py2cfg)\n", "  Downloading urwid_readline-0.15.1.tar.gz (9.0 kB)\n", "  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25ldone\n", "\u001b[?25h  Preparing metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hCollecting urwid (from pudb->py2cfg)\n", "  Downloading urwid-3.0.2-py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from jedi<1,>=0.18->pudb->py2cfg) (0.8.4)\n", "Requirement already satisfied: wcwidth in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from urwid->pudb->py2cfg) (0.2.13)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from requests->py2cfg) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from requests->py2cfg) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from requests->py2cfg) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Documents/Code3-import/Github/Auto-promptgen/.venv/lib/python3.13/site-packages (from requests->py2cfg) (2025.6.15)\n", "Downloading py2cfg-1.0.5-py3-none-any.whl (66 kB)\n", "Downloading astor-0.8.1-py2.py3-none-any.whl (27 kB)\n", "Downloading pudb-2025.1-py3-none-any.whl (89 kB)\n", "Downloading urwid-3.0.2-py3-none-any.whl (295 kB)\n", "Downloading websocket_server-0.6.4-py3-none-any.whl (7.5 kB)\n", "Building wheels for collected packages: urwid-readline\n", "  Building wheel for urwid-readline (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for urwid-readline: filename=urwid_readline-0.15.1-py3-none-any.whl size=9326 sha256=06f9d35fc0ccfb70f2f6758102b1d7f1823a7eef77004f7246bc7b4e2bc12bdd\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/d3/55/71/5e9db4d454d619c5fe8164c5e039d95e06f6b2251e6f434c79\n", "Successfully built urwid-readline\n", "Installing collected packages: websocket-server, urwid, astor, urwid-readline, pudb, py2cfg\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6/6\u001b[0m [py2cfg]2m5/6\u001b[0m [py2cfg]\n", "\u001b[1A\u001b[2KSuccessfully installed astor-0.8.1 pudb-2025.1 py2cfg-1.0.5 urwid-3.0.2 urwid-readline-0.15.1 websocket-server-0.6.4\n"]}], "source": ["!pip install py2cfg"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Import Libraries"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import subprocess\n", "from pathlib import Path\n", "from IPython.display import Image, display, Markdown\n", "import tempfile"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Collect Python Files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "# Collect all .py files in the repository (excluding venv/__pycache__/.venv)\npy_files = []\nrepo_root = os.path.abspath('..')\n\nfor root, dirs, files in os.walk(repo_root):\n    # Skip virtual environments, cache directories, and _temp\n    dirs[:] = [d for d in dirs if d not in (\"venv\", \".venv\", \"__pycache__\", \".git\", \"_temp\", \"node_modules\")]\n    py_files += [os.path.join(root, f) for f in files if f.endswith(\".py\")]\n\nprint(f\"Found {len(py_files)} Python files:\")\nfor f in py_files:\n    print(f\"  - {os.path.relpath(f, repo_root)}\")"}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Generate Control Flow Graphs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "# Create output directory for CFGs\noutput_dir = \"cfg_output\"\nos.makedirs(output_dir, exist_ok=True)\n\n# Generate CFGs for each file\ndot_files = []\nerrors = []\n\nfor py_file in py_files:\n    basename = os.path.basename(py_file).replace('.py', '')\n    output_path = os.path.join(output_dir, f\"{basename}_cfg.dot\")\n    \n    try:\n        # py2cfg generates a .dot file\n        cmd = ['py2cfg', py_file, '--outfile', output_path]\n        result = subprocess.run(cmd, capture_output=True, text=True)\n        \n        if result.returncode == 0:\n            dot_files.append(output_path)\n            print(f\"✓ Processed: {os.path.relpath(py_file, repo_root)}\")\n        else:\n            error_msg = f\"Error processing {os.path.relpath(py_file, repo_root)}: {result.stderr}\"\n            errors.append(error_msg)\n            print(f\"✗ {error_msg}\")\n    except Exception as e:\n        error_msg = f\"Exception processing {os.path.relpath(py_file, repo_root)}: {e}\"\n        errors.append(error_msg)\n        print(f\"✗ {error_msg}\")\n\nprint(f\"\\nSuccessfully processed {len(dot_files)} files\")\nif errors:\n    print(f\"Failed to process {len(errors)} files\")"}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Convert DOT files to PNG (Optional)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Generated 0 PNG images\n"]}], "source": ["# Convert DOT files to PNG images using graphviz\n", "png_files = []\n", "\n", "for dot_file in dot_files:\n", "    png_file = dot_file.replace('.dot', '.png')\n", "    try:\n", "        subprocess.run(['dot', '-Tpng', dot_file, '-o', png_file], \n", "                     capture_output=True, check=True)\n", "        png_files.append(png_file)\n", "        print(f\"Created PNG: {os.path.basename(png_file)}\")\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"Warning: Could not convert {os.path.basename(dot_file)} to PNG\")\n", "    except FileNotFoundError:\n", "        print(\"Warning: graphviz not found. Install it with 'brew install graphviz' to generate PNG images.\")\n", "        break\n", "\n", "print(f\"\\nGenerated {len(png_files)} PNG images\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON> Sample CFGs"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No PNG files generated. Make sure graphviz is installed.\n"]}], "source": ["# Display the first few control flow graphs\n", "if png_files:\n", "    print(\"Displaying first 3 control flow graphs:\")\n", "    for i, png_file in enumerate(png_files[:3]):\n", "        print(f\"\\n{os.path.basename(png_file)}:\")\n", "        display(Image(png_file))\n", "else:\n", "    print(\"No PNG files generated. Make sure graphviz is installed.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. View DOT Files Directly"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# View the content of the first DOT file\n", "if dot_files:\n", "    with open(dot_files[0], 'r') as f:\n", "        dot_content = f.read()\n", "    print(f\"Content of {os.path.basename(dot_files[0])}:\")\n", "    print(\"-\" * 50)\n", "    print(dot_content[:1000] + \"...\" if len(dot_content) > 1000 else dot_content)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Uncomment to open in browser\n", "# webbrowser.open(url)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Could not display inline - URL may be too long. Use the link above instead.\n"]}], "source": ["# Try to display inline (may fail if URL is too long)\n", "try:\n", "    display(IFrame(src=url, width=800, height=600))\n", "except:\n", "    print(\"Could not display inline - URL may be too long. Use the link above instead.\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preview of Mermaid DSL:\n", "--------------------------------------------------\n"]}, {"ename": "NameError", "evalue": "name 'merged_dsl' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[10]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mPreview of Mermaid DSL:\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      3\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m-\u001b[39m\u001b[33m\"\u001b[39m * \u001b[32m50\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m \u001b[38;5;28mprint\u001b[39m(merged_dsl[:\u001b[32m1000\u001b[39m] + \u001b[33m\"\u001b[39m\u001b[33m...\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[43mmerged_dsl\u001b[49m) > \u001b[32m1000\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m merged_dsl)\n", "\u001b[31mNameError\u001b[39m: name 'merged_dsl' is not defined"]}], "source": ["# Show first 1000 characters of the Mermaid DSL\n", "print(\"Preview of Mermaid DSL:\")\n", "print(\"-\" * 50)\n", "print(merged_dsl[:1000] + \"...\" if len(merged_dsl) > 1000 else merged_dsl)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.4"}}, "nbformat": 4, "nbformat_minor": 4}