"""
aa_langchain_pipeline.py  📜  (rev‑B)
=========================
FCIS, Duplo‑block LangChain pipeline w/ PROMPTS singleton & async fan‑out.
Pyramid bullets (why):
1. Swap call_llm signature → (micro, sys, usr) for readability.
2. Centralise prompts in PROMPTS singleton (micro+sys+usr fields).
3. Rename SyntheticDoc → SyntheticTestCases; add SyntheticGraders placeholder.
4. Provide async fan‑out helpers for synthetic TC1s & grader gens.
5. Add ScenarioDoc & ReqDoc w/ _display() to render Markdown.
6. Introduce MasterPrompt to store iterative versions.
"""

# std‑lib -------------------------------------------------------------
from __future__ import annotations

import asyncio
from functools import reduce
from typing import Callable, List

# deps ---------------------------------------------------------------
from dotenv import load_dotenv
from langchain.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, ConfigDict, Field, field_validator

load_dotenv()


# ───────────────────────── PROMPTS singleton ─────────────────────────
class _Prompt(BaseModel):  # simple container
    micro: str
    sys: str
    usr: str


class PROMPTS:  # one line per prompt, <150‑char micro (oner) -------------------
    """PROMPTS singleton that contains predefined prompt templates for various tasks."""

    gen_prompt = _Prompt(
        micro="oner: craft‑initial‑prompt‑terse",
        sys="You are a prompt engineer. Output a terse prompt only.",
        usr="{problem_desc}",
    )
    test_cats = _Prompt(
        micro="oner: list‑3‑json‑categories",
        sys="List three JSON category labels only.",
        usr="{prompt}",
    )
    synth_tests = _Prompt(
        micro="oner: expand‑synthetic‑tests‑json",
        sys="Generate minimal synthetic tests JSON.",
        usr="{cats}",
    )
    refine = _Prompt(
        micro="oner: refine‑prompt‑using‑weak‑areas",
        sys="Improve prompt given weak areas.",
        usr="{payload}",
    )
    compress = _Prompt(
        micro="oner: compress‑prompt‑token‑wise",
        sys="Compress tokens but keep semantics.",
        usr="{prompt}",
    )


# ───────────────────────── data‑schemas (Duplo Blocks) ─────────────────────────
class ProblemDesc(BaseModel):
    model_config = ConfigDict(strict=True, extra="forbid")
    problem_desc: str
    target_model: str
    token_limits: dict[str, int]
    constraints: List[str]


class MasterPrompt(BaseModel):
    versions: List[str] = Field(default_factory=list)

    def current(self) -> str:  # returns latest prompt
        return self.versions[-1]

    def add(self, p: str):
        self.versions.append(p)


class PromptDoc(BaseModel):
    model_config = ConfigDict(strict=True, validate_assignment=True)
    prompt: str = Field(..., min_length=10)
    token_count: int
    version: int = 0

    @field_validator("token_count")
    def _budget(cls, v):
        assert v > 0, "empty prompt?"
        return v


class TestCaseDoc(BaseModel):
    test_categories: List[str]
    template_count: int


class SyntheticTestCases(BaseModel):
    test_cases: List[dict]


class SyntheticGraders(BaseModel):  # placeholder, mirrors TestCase list
    graders: List[dict]


class GradeDoc(BaseModel):
    pass_rate: float
    improvement_areas: List[str]


class ScenarioDoc(BaseModel):
    scenarios: List[str]

    def _display(self) -> str:
        return "## Scenarios\n" + "\n".join(f"- {s}" for s in self.scenarios)


class ReqDoc(BaseModel):
    requirements: List[str]

    def _display(self) -> str:
        return "## Requirements\n" + "\n".join(f"- {r}" for r in self.requirements)


class FinalBundle(BaseModel):
    final_prompt: str
    test_generation_doc: str
    requirements_doc: str


# ────────────────────────── LLM helper  ────────────────────────────
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)


def call_llm(micro: str, sys_prompt: str, user_prompt: str) -> str:
    """LLM wrapper; micro is readability only."""
    tmpl = ChatPromptTemplate.from_messages([("system", sys_prompt), ("user", "{u}")])
    return llm.invoke(tmpl.format_prompt(u=user_prompt).to_messages()).content


# ───────────────────────── pipeline stages  ──────────────────────────


def gen_prompt(inp: ProblemDesc) -> PromptDoc:
    body = call_llm(
        PROMPTS.gen_prompt.micro,
        PROMPTS.gen_prompt.sys,
        PROMPTS.gen_prompt.usr.format(problem_desc=inp.problem_desc),
    )
    return PromptDoc(prompt=body, token_count=len(body.split()))


def gen_testcases(prompt_doc: PromptDoc) -> TestCaseDoc:
    cats = call_llm(
        PROMPTS.test_cats.micro,
        PROMPTS.test_cats.sys,
        PROMPTS.test_cats.usr.format(prompt=prompt_doc.prompt),
    )
    return TestCaseDoc(test_categories=cats.split(), template_count=3)


# ─── simplified sync version for now (async can be added later) ───
def _synth_task_sync(cat: str) -> dict:  # returns one test‑case dict
    resp = call_llm(
        PROMPTS.synth_tests.micro,
        PROMPTS.synth_tests.sys,
        PROMPTS.synth_tests.usr.format(cats=cat),
    )
    return {"raw": resp}


def gen_synth_TC1s_sync(tc_doc: TestCaseDoc) -> SyntheticTestCases:
    tests = [_synth_task_sync(c) for c in tc_doc.test_categories]
    return SyntheticTestCases(test_cases=list(tests))


# stub async grader gen ------------------------------------------------------
async def _grader_task(req: str) -> dict:
    return {"grader": f"grader for {req}"}


async def gen_graders(req_doc: ReqDoc) -> SyntheticGraders:
    gs = await asyncio.gather(*(_grader_task(r) for r in req_doc.requirements))
    return SyntheticGraders(graders=list(gs))


# execute + grade (sync stub) -----------------------------------------------


def grade_tests(synth: SyntheticTestCases) -> GradeDoc:
    return GradeDoc(pass_rate=0.75, improvement_areas=["clarity", "edge cases"])


def refine_prompt(prompt_doc: PromptDoc, grade: GradeDoc) -> PromptDoc:
    payload = f"PROMPT:\n{prompt_doc.prompt}\nISSUES:{','.join(grade.improvement_areas)}"
    new_body = call_llm(
        PROMPTS.refine.micro,
        PROMPTS.refine.sys,
        PROMPTS.refine.usr.format(payload=payload),
    )
    return prompt_doc.model_copy(
        update={
            "prompt": new_body,
            "version": prompt_doc.version + 1,
            "token_count": len(new_body.split()),
        }
    )


def optimise_prompt(prompt_doc: PromptDoc) -> PromptDoc:
    shorter = call_llm(
        PROMPTS.compress.micro,
        PROMPTS.compress.sys,
        PROMPTS.compress.usr.format(prompt=prompt_doc.prompt),
    )
    return prompt_doc.model_copy(update={"prompt": shorter, "token_count": len(shorter.split())})


def deliver(prompt_doc: PromptDoc) -> FinalBundle:
    return FinalBundle(
        final_prompt=prompt_doc.prompt,
        test_generation_doc="<stub>",
        requirements_doc="<stub>",
    )


# ───────────────────────── orchestrator  ────────────────────────────
Stage = Callable[[BaseModel], BaseModel]


def pipe(*stages: Stage) -> Stage:
    return lambda x: reduce(lambda acc, fn: fn(acc), stages, x)


# ─── sync wrapper to run async fan‑outs inside pipeline ───
def run_async(coro):
    return asyncio.run(coro)


# Create a proper pipeline that maintains state between steps
def run_pipeline_revb(prob: ProblemDesc) -> FinalBundle:
    """Complete pipeline execution with proper state management and async support"""
    # Step 1: Generate initial prompt
    prompt_doc = gen_prompt(prob)

    # Step 2: Generate test cases
    test_cases = gen_testcases(prompt_doc)

    # Step 3: Generate synthetic tests (sync for now)
    synthetic = gen_synth_TC1s_sync(test_cases)

    # Step 4: Grade the tests
    grade = grade_tests(synthetic)

    # Step 5: Refine prompt based on grade
    refined_prompt = refine_prompt(prompt_doc, grade)

    # Step 6: Optimize tokens
    optimized_prompt = optimise_prompt(refined_prompt)

    # Step 7: Deliver final bundle
    return deliver(optimized_prompt)


# ───────────────────────── main guard  ──────────────────────────────
if __name__ == "__main__":
    prob = ProblemDesc(
        problem_desc="Summarise legal contract clauses for risk.",
        target_model="gpt-4o-mini",
        token_limits={"hard": 4096},
        constraints=["no hallucinations", "<=2000 tokens"],
    )
    bundle = run_pipeline_revb(prob)
    print("\n===== FINAL PROMPT =====\n", bundle.final_prompt)

    # show markdown displays
    scen_doc = ScenarioDoc(scenarios=["sue for breach", "transfer risk", "terminate early"])
    req_doc = ReqDoc(requirements=["accuracy>90%", "explain liability", "short"])
    print(scen_doc._display())
    print(req_doc._display())
    print(req_doc._display())
