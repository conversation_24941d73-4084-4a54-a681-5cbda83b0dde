"""
 aa_langchain_pipeline.py  📜
 =========================
 Minimal FCIS pipeline that mirrors the Prompt‑Generation flowchart via LangChain.
 ‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑‑
 • Pyramid bullets (why):
   1. Provide an executable, strictly‑typed LangChain pipeline for prompt/test generation.
   2. Keep business logic pure, brittle, & readable (Duplo Blocks).
   3. Make every stage a tiny callable with Pydantic I/O; pipeline = list composition.
   4. Expose one CLI entry so running the file prints deliverables end‑to‑end.
 • Requirements:
   ⏳ backlog — env var OPENAI_API_KEY must be set.
   🟠 planned — unit tests + DocTests per block.
   ☑️ implemented — strict Pydantic, numbered EOL comments, Pipeline orchestrator.
 • Control‑flow (ASCII):
   Input→PromptGen→TCGen→SynthGen→Chunk→Exec→Grade→Feedback→Refine→TokOpt→Final
"""

# std‑lib -------------------------------------------------------------
from __future__ import annotations
from functools import reduce
from typing import Callable, List

# deps ---------------------------------------------------------------
from pydantic import BaseModel, Field, ConfigDict, field_validator
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate

# ───────────────────────── data‑schemas (Duplo Blocks) ─────────────────────────
class ProblemDesc(BaseModel):
    model_config = ConfigDict(strict=True, extra="forbid")  # 1️⃣ fail on unknown keys
    problem_desc: str
    target_model: str
    token_limits: dict[str, int]
    constraints: List[str]

class PromptDoc(BaseModel):
    model_config = ConfigDict(strict=True, validate_assignment=True)
    prompt: str = Field(..., min_length=10)
    token_count: int
    version: int = 0

    @field_validator("token_count")
    def _check_budget(cls, v):  # 2️⃣ guard budget
        budget = 4096  # simplified for now
        assert v <= budget, "token budget blown"
        return v

class TestCaseDoc(BaseModel):
    test_categories: List[str]
    template_count: int

class SyntheticDoc(BaseModel):
    synthetic_tests: List[dict]

class GradeDoc(BaseModel):
    pass_rate: float
    improvement_areas: List[str]

class FinalBundle(BaseModel):
    final_prompt: str
    test_generation_doc: str
    requirements_doc: str

# ────────────────────────── LLM + helper  ────────────────────────────
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

def call_llm(sys_prompt: str, user_prompt: str) -> str:
    """LLM wrapper 3 LOC"""
    tmpl = ChatPromptTemplate.from_messages([
        ("system", sys_prompt),
        ("user", "{u}")
    ])
    return llm.invoke(tmpl.format_prompt(u=user_prompt).to_messages()).content  # returns str

# ───────────────────────── pipeline stages  ──────────────────────────

def gen_prompt(inp: ProblemDesc) -> PromptDoc:
    """Step‑1 Prompt Generator"""
    body = call_llm(
        "You are a prompt engineer. Output a terse prompt only.",
        inp.problem_desc,
    )
    return PromptDoc(prompt=body, token_count=len(body.split()))

def gen_testcases(prompt_doc: PromptDoc) -> TestCaseDoc:
    """Step‑2 Test‑case template generation"""
    cats = call_llm("List three JSON category labels only.", prompt_doc.prompt)
    return TestCaseDoc(test_categories=cats.split(), template_count=3)


def gen_synthetic(tc: TestCaseDoc) -> SyntheticDoc:
    """Step‑3 Synthetic test expansion"""
    synth = call_llm("Generate minimal synthetic tests JSON.", " ".join(tc.test_categories))
    return SyntheticDoc(synthetic_tests=[{"raw": synth}])


def grade_tests(synth: SyntheticDoc) -> GradeDoc:
    """Step‑4 Execute + Grade (mocked)"""
    return GradeDoc(pass_rate=0.7, improvement_areas=["clarity", "edge cases"])


def refine_prompt(prompt_doc: PromptDoc, grade: GradeDoc) -> PromptDoc:
    """Step‑5 Refiner"""
    new_body = call_llm(
        "Improve prompt given weak areas.",
        f"PROMPT:\n{prompt_doc.prompt}\nISSUES:{','.join(grade.improvement_areas)}",
    )
    return prompt_doc.model_copy(update={"prompt": new_body, "version": prompt_doc.version + 1})


def optimise_prompt(prompt_doc: PromptDoc) -> PromptDoc:
    """Step‑6 Token optimiser via LLM compression"""
    shorter = call_llm("Compress tokens but keep semantics.", prompt_doc.prompt)
    return prompt_doc.model_copy(update={"prompt": shorter, "token_count": len(shorter.split())})


def deliver(prompt_doc: PromptDoc) -> FinalBundle:
    """Step‑7 Final bundle"""
    return FinalBundle(
        final_prompt=prompt_doc.prompt,
        test_generation_doc="<stub>",
        requirements_doc="<stub>",
    )

# ───────────────────────── orchestrator  ────────────────────────────
Stage = Callable[[BaseModel], BaseModel]

def pipe(*stages: Stage) -> Stage:
    """functional pipe"""  # 3️⃣ reduce
    return lambda x: reduce(lambda acc, fn: fn(acc), stages, x)

# Create a proper pipeline that maintains state between steps
def run_pipeline(prob: ProblemDesc) -> FinalBundle:
    """Complete pipeline execution with proper state management"""
    # Step 1: Generate initial prompt
    prompt_doc = gen_prompt(prob)
    
    # Step 2: Generate test cases
    test_cases = gen_testcases(prompt_doc)
    
    # Step 3: Generate synthetic tests
    synthetic = gen_synthetic(test_cases)
    
    # Step 4: Grade the tests
    grade = grade_tests(synthetic)
    
    # Step 5: Refine prompt based on grade
    refined_prompt = refine_prompt(prompt_doc, grade)
    
    # Step 6: Optimize tokens
    optimized_prompt = optimise_prompt(refined_prompt)
    
    # Step 7: Deliver final bundle
    return deliver(optimized_prompt)

# ───────────────────────── main guard  ──────────────────────────────
if __name__ == "__main__":
    prob = ProblemDesc(
        problem_desc="Summarise legal contract clauses for risk.",
        target_model="gpt-4o-mini",
        token_limits={"hard": 4096},
        constraints=["no hallucinations", "<=2000 tokens"],
    )
    bundle = run_pipeline(prob)
    print("\n===== FINAL PROMPT =====\n", bundle.final_prompt)
