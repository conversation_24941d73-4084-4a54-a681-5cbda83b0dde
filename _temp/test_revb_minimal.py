#!/usr/bin/env python3
"""Minimal working version of Rev-B with all improvements"""

from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from pydantic import BaseModel, ConfigDict, Field, field_validator
from typing import List

load_dotenv()

# PROMPTS singleton (Rev-B improvement)
class _Prompt(BaseModel):
    micro: str
    sys: str
    usr: str

class PROMPTS:
    gen_prompt = _Prompt(
        micro="oner: craft-initial-prompt-terse",
        sys="You are a prompt engineer. Output a terse prompt only.",
        usr="{problem_desc}",
    )
    refine = _Prompt(
        micro="oner: refine-prompt-using-weak-areas",
        sys="Improve prompt given weak areas.",
        usr="{payload}",
    )

# Enhanced data models (Rev-B improvement)
class ProblemDesc(BaseModel):
    model_config = ConfigDict(strict=True, extra="forbid")
    problem_desc: str
    target_model: str
    token_limits: dict[str, int]
    constraints: List[str]

class MasterPrompt(BaseModel):
    versions: List[str] = Field(default_factory=list)
    
    def current(self) -> str:
        return self.versions[-1] if self.versions else ""
    
    def add(self, p: str):
        self.versions.append(p)

class PromptDoc(BaseModel):
    model_config = ConfigDict(strict=True, validate_assignment=True)
    prompt: str = Field(..., min_length=10)
    token_count: int
    version: int = 0
    
    @field_validator("token_count")
    def _budget(cls, v):
        assert v > 0, "empty prompt?"
        return v

class ScenarioDoc(BaseModel):
    scenarios: List[str]
    
    def _display(self) -> str:
        return "## Scenarios\n" + "\n".join(f"- {s}" for s in self.scenarios)

class ReqDoc(BaseModel):
    requirements: List[str]
    
    def _display(self) -> str:
        return "## Requirements\n" + "\n".join(f"- {r}" for r in self.requirements)

class FinalBundle(BaseModel):
    final_prompt: str
    test_generation_doc: str
    requirements_doc: str

# LLM setup
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)

def call_llm(micro: str, sys_prompt: str, user_prompt: str) -> str:
    """LLM wrapper; micro is readability only."""
    tmpl = ChatPromptTemplate.from_messages([("system", sys_prompt), ("user", "{u}")])
    return llm.invoke(tmpl.format_prompt(u=user_prompt).to_messages()).content

# Simplified pipeline
def run_revb_demo(prob: ProblemDesc) -> FinalBundle:
    """Minimal Rev-B demo showcasing key improvements"""
    
    # Step 1: Generate initial prompt using PROMPTS singleton
    print("Step 1: Generating prompt with PROMPTS singleton...")
    body = call_llm(
        PROMPTS.gen_prompt.micro,
        PROMPTS.gen_prompt.sys,
        PROMPTS.gen_prompt.usr.format(problem_desc=prob.problem_desc),
    )
    prompt_doc = PromptDoc(prompt=body, token_count=len(body.split()))
    
    # Step 2: Use MasterPrompt for version tracking
    print("Step 2: Using MasterPrompt for version tracking...")
    master = MasterPrompt()
    master.add(prompt_doc.prompt)
    
    # Step 3: Mock refinement using PROMPTS singleton
    print("Step 3: Refining with PROMPTS singleton...")
    payload = f"PROMPT:\n{prompt_doc.prompt}\nISSUES:clarity,edge cases"
    refined = call_llm(
        PROMPTS.refine.micro,
        PROMPTS.refine.sys,
        PROMPTS.refine.usr.format(payload=payload),
    )
    
    refined_doc = prompt_doc.model_copy(
        update={"prompt": refined, "version": 1, "token_count": len(refined.split())}
    )
    master.add(refined_doc.prompt)
    
    print(f"Version history: {len(master.versions)} versions")
    
    return FinalBundle(
        final_prompt=master.current(),
        test_generation_doc="<generated using Rev-B patterns>",
        requirements_doc="<enhanced with _display() methods>",
    )

if __name__ == "__main__":
    prob = ProblemDesc(
        problem_desc="Summarise legal contract clauses for risk.",
        target_model="gpt-4o-mini",
        token_limits={"hard": 4096},
        constraints=["no hallucinations", "<=2000 tokens"],
    )
    
    print("=== Rev-B Demo ===")
    bundle = run_revb_demo(prob)
    print("\n===== FINAL PROMPT =====")
    print(bundle.final_prompt)
    
    # Show enhanced display methods
    print("\n=== Enhanced Display Methods ===")
    scen_doc = ScenarioDoc(scenarios=["sue for breach", "transfer risk", "terminate early"])
    req_doc = ReqDoc(requirements=["accuracy>90%", "explain liability", "short"])
    print(scen_doc._display())
    print(req_doc._display())